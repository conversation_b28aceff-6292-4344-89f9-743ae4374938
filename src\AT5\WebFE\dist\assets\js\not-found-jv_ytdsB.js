import{j as e,r as s,B as t,C as r,S as n,T as i,b as o}from"./mui-51Y1Yx8M.js";import{H as a,a as c,b as x,p as h,R as l}from"./index-CQLEdRvh.js";import"./vendor-Csw2ODfV.js";import"./router-Bd8Y3ArC.js";import"./redux-BKAL-i9G.js";const m={title:`Not found | Errors | ${c.name}`};function f(){return e.jsxs(s.Fragment,{children:[e.jsx(a,{children:e.jsx("title",{children:m.title})}),e.jsx(t,{component:"main",sx:{alignItems:"center",display:"flex",flexDirection:"column",justifyContent:"center",minHeight:"100%",py:"64px"},children:e.jsx(r,{maxWidth:"lg",children:e.jsxs(n,{spacing:6,children:[e.jsx(t,{sx:{display:"flex",justifyContent:"center"},children:e.jsx(t,{alt:"Not found",component:"img",src:x.notFound(),sx:{height:"auto",maxWidth:"100%",width:"200px"}})}),e.jsxs(n,{spacing:1,sx:{textAlign:"center"},children:[e.jsx(i,{variant:"h4",children:"404: The page you are looking for isn't here"}),e.jsx(i,{color:"text.secondary",children:"You either tried some shady route or you came here by mistake. Whichever it is, try using the navigation."})]}),e.jsx(t,{sx:{display:"flex",justifyContent:"center"},children:e.jsx(o,{component:l,href:h.home,variant:"contained",children:"Back to home"})})]})})})]})}export{f as Page};
//# sourceMappingURL=not-found.BCKWNb4t.js.map
