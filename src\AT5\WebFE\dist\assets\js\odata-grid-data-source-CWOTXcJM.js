import{E as M}from"./index-CQLEdRvh.js";var $=Object.freeze({__proto__:null,get canonicalFunction(){return d},get contains(){return A},get startsWith(){return W},get endsWith(){return k},get toLower(){return x},get toUpper(){return B},get trim(){return T},get substring(){return z},get concat(){return N},get length(){return U},get indexOf(){return C}});function R(t,n,r){return n&&(r&&t.condition!==r&&(t={condition:r,rules:t.rules.length>1?[t]:t.rules}),t.rules.push(n)),t}function m(t){return typeof t=="function"&&(t=t(new g)),t&&t.toString()}function E(t,n){return t.map(function(r){return y(r,!0)}).join(" "+n+" ")}function y(t,n){return n===void 0&&(n=!1),typeof t!="string"&&(t=t.rules.length===1?y(t.rules[0]):E(t.rules,t.condition)),n?"("+t+")":t}function _(t){return typeof t=="function"?t($):t}function F(t){return typeof t=="string"}function I(t){return typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]"}function S(t){return F(t)?"'"+t+"'":I(t)?t.toISOString():t}function d(t,n,r,e,i){if(e===void 0&&(e=!0),i===void 0&&(i=!1),n=_(n),typeof r>"u"?r=[]:Array.isArray(r)||(r=[r]),r.length===0)return t+"("+n+")";e&&(r=r.map(S));var o=i?[].concat(r,[n]):[n].concat(r);return t+"("+o.join(", ")+")"}function A(t,n){return d("contains",t,n)}function W(t,n){return d("startswith",t,n)}function k(t,n){return d("endswith",t,n)}function x(t){return d("tolower",t)}function B(t){return d("toupper",t)}function T(t){return d("trim",t)}function z(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),e=1;e<n;e++)r[e-1]=arguments[e];return d("substring",t,r)}function N(t,n,r){return d("concat",t,[n],r)}function U(t){return d("length",t)}function C(t,n){return d("indexof",t,[n])}function q(t){var n=m(t);if(n)return"not ("+n+")"}function h(t,n,r,e){return e===void 0&&(e=!0),t=_(t),e&&(r=S(r)),t+" "+n+" "+r}function L(t,n,r,e){return e===void 0&&(e=!0),r?(t=_(t),Array.isArray(r)?r.map(function(i){return h(t,n,i,e)}):[h(t,n,r,e)]):[]}function j(t,n,r){return h(t,"eq",n,r)}function v(t,n,r){return h(t,"ne",n,r)}function D(t,n,r){return h(t,"gt",n,r)}function G(t,n,r){return h(t,"ge",n,r)}function K(t,n,r){return h(t,"lt",n,r)}function H(t,n,r){return h(t,"le",n,r)}function O(t,n){return t.join(" "+n+" ")}function b(t,n,r){return O(L(t,"eq",n,r),"or")}function J(t,n){var r=Object.keys(t),e=r.filter(function(i){return typeof t[i]<"u"}).map(function(i){var o=t[i];return Array.isArray(o)?"("+b(i,o,n)+")":j(i,o,n)});return O(e,"and")}function P(t,n,r){return q(b(t,n,r))}var g=(function(){function t(r){if(r===void 0&&(r="and"),!(this instanceof t))return new t(r);this._condition=r,this._source={condition:r,rules:[]}}var n=t.prototype;return n._add=function(e,i){return i===void 0&&(i=this._condition),this._source=R(this._source,m(e),i),this},n.and=function(e){return this._add(e,"and")},n.or=function(e){return this._add(e,"or")},n.not=function(e){return this._add(q(e))},n.eq=function(e,i,o){return this._add(j(e,i,o))},n.ne=function(e,i,o){return this._add(v(e,i,o))},n.gt=function(e,i,o){return this._add(D(e,i,o))},n.ge=function(e,i,o){return this._add(G(e,i,o))},n.lt=function(e,i,o){return this._add(K(e,i,o))},n.le=function(e,i,o){return this._add(H(e,i,o))},n.in=function(e,i,o){return this._add(b(e,i,o))},n.compareAll=function(e,i){return this._add(J(e,i))},n.notIn=function(e,i,o){return this._add(P(e,i,o))},n.contains=function(e,i){return this._add(A(e,i))},n.startsWith=function(e,i){return this._add(W(e,i))},n.endsWith=function(e,i){return this._add(k(e,i))},n.fn=function(e,i,o,p,l){return this._add(d(e,i,o,p,l))},n.isEmpty=function(){return this._source.rules.length===0},n.toString=function(){return y(this._source)},t})();g.and=function(){return new g("and")};g.or=function(){return new g("or")};g.functions=$;function Y(t,n,r){return{getRows:async e=>{var l;const i={$count:!0};if(e.paginationModel&&(i.$top=e.paginationModel.pageSize,i.$skip=e.paginationModel.page*e.paginationModel.pageSize),e.sortModel&&e.sortModel.length>0){const u=e.sortModel.map(s=>`${s.field} ${s.sort==="desc"?"desc":"asc"}`);i.$orderby=u.join(", ")}const o=(e==null?void 0:e.filterModel)??{},p=Array.isArray(o.items)?o.items:[];if(p.length>0){const u=new g;for(const c of p){const w=(c.operator??c.operatorValue??"").toString(),a=c.field,f=c.value;if(!(!["isEmpty","isNotEmpty"].includes(w)&&(f==null||f==="")))switch(w){case"contains":u.contains(a,f);break;case"equals":u.eq(a,f);break;case"startsWith":u.startsWith(a,f);break;case"endsWith":u.endsWith(a,f);break;case"is":u.eq(a,f);break;case"not":case"isNot":case"!=":u.ne(a,f);break;case"after":case">":u.gt(a,f);break;case"onOrAfter":case">=":u.ge(a,f);break;case"before":case"<":u.lt(a,f);break;case"onOrBefore":case"<=":u.le(a,f);break;case"isEmpty":u.eq(a,null);break;case"isNotEmpty":u.ne(a,null);break}}const s=u.toString();s&&(i.$filter=s)}try{const u=n==null?void 0:n();if(u&&u.length>0){const s=Array.from(new Set(u.filter(Boolean)));s.includes("id")||s.unshift("id"),i.$select=s.join(",")}}catch{}try{const u=r?{...i,...r}:i,s=await M.dispatch(t(u));if(s.error)throw new Error("Failed to fetch jobs data");const c=s.data;return{rows:(c==null?void 0:c.value)??[],rowCount:(c==null?void 0:c.count)??((l=c==null?void 0:c.value)==null?void 0:l.length)??0}}catch(u){throw console.error("Error fetching grid data:",u),u}},updateRow:async e=>(console.log("Update row params:",e),Promise.resolve())}}export{Y as c};
//# sourceMappingURL=odata-grid-data-source.CXLB9acb.js.map
