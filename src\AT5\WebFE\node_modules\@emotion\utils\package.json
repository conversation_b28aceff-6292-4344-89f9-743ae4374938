{"name": "@emotion/utils", "version": "1.4.2", "description": "internal utils for emotion", "main": "dist/emotion-utils.cjs.js", "module": "dist/emotion-utils.esm.js", "types": "dist/emotion-utils.cjs.d.ts", "exports": {".": {"types": {"import": "./dist/emotion-utils.cjs.mjs", "default": "./dist/emotion-utils.cjs.js"}, "edge-light": {"module": "./dist/emotion-utils.edge-light.esm.js", "import": "./dist/emotion-utils.edge-light.cjs.mjs", "default": "./dist/emotion-utils.edge-light.cjs.js"}, "worker": {"module": "./dist/emotion-utils.edge-light.esm.js", "import": "./dist/emotion-utils.edge-light.cjs.mjs", "default": "./dist/emotion-utils.edge-light.cjs.js"}, "workerd": {"module": "./dist/emotion-utils.edge-light.esm.js", "import": "./dist/emotion-utils.edge-light.cjs.mjs", "default": "./dist/emotion-utils.edge-light.cjs.js"}, "browser": {"module": "./dist/emotion-utils.browser.esm.js", "import": "./dist/emotion-utils.browser.cjs.mjs", "default": "./dist/emotion-utils.browser.cjs.js"}, "module": "./dist/emotion-utils.esm.js", "import": "./dist/emotion-utils.cjs.mjs", "default": "./dist/emotion-utils.cjs.js"}, "./package.json": "./package.json"}, "imports": {"#is-browser": {"edge-light": "./src/conditions/false.ts", "workerd": "./src/conditions/false.ts", "worker": "./src/conditions/false.ts", "browser": "./src/conditions/true.ts", "default": "./src/conditions/is-browser.ts"}}, "license": "MIT", "scripts": {"test:typescript": "dtslint types"}, "repository": "https://github.com/emotion-js/emotion/tree/main/packages/utils", "publishConfig": {"access": "public"}, "files": ["src", "dist"], "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "typescript": "^5.4.5"}}