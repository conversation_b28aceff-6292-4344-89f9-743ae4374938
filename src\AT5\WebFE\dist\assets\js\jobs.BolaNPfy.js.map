{"version": 3, "file": "jobs-cMKKix6w.js", "sources": ["../../../node_modules/@mui/icons-material/esm/FilterList.js", "../../../node_modules/@mui/icons-material/esm/ViewColumn.js", "../../../node_modules/@mui/icons-material/esm/Add.js", "../../../node_modules/@mui/icons-material/esm/DeleteOutlined.js", "../../../node_modules/@mui/icons-material/esm/PlayArrowOutlined.js", "../../../src/store/hooks.ts", "../../../src/pages/administration/jobs/components/job-run-list.tsx", "../../../src/pages/administration/jobs/jobs.tsx"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M10 18h4v-2h-4zM3 6v2h18V6zm3 7h12v-2H6z\"\n}), 'FilterList');", "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M14.67 5v14H9.33V5zm1 14H21V5h-5.33zm-7.34 0V5H3v14z\"\n}), 'ViewColumn');", "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z\"\n}), 'Add');", "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M16 9v10H8V9zm-1.5-6h-5l-1 1H5v2h14V4h-3.5zM18 7H6v12c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2z\"\n}), 'DeleteOutlined');", "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M10 8.64 15.27 12 10 15.36zM8 5v14l11-7z\"\n}), 'PlayArrowOutlined');", "import { useDispatch, useSelector } from 'react-redux';\nimport type { TypedUseSelectorHook } from 'react-redux';\nimport type { RootState, AppDispatch } from './index';\n\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\n", "import { createRTKODataGridDataSource } from '@/store/api/odata-grid-data-source';\r\nimport { useAppDispatch, useAppSelector } from '@/store/hooks';\r\nimport { selectFilterModel, selectPage, selectPageSize, selectSortModel, setFilterModel, setPage, setPageSize, setSortModel } from '@/store/slices/job-runs-slice';\r\n\r\nimport { DataGridPremium, GridColDef, useGridApiRef } from '@mui/x-data-grid-premium';\r\nimport React from 'react';\r\nimport { administrationApi } from '@/store/api/administration';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { paths } from '@/paths';\r\nimport { toast } from '@/components/core/toaster';\r\n\r\nexport function JobRunList(): React.JSX.Element {\r\n    const apiRef = useGridApiRef();\r\n    const dispatch = useAppDispatch();\r\n    const pageSize = useAppSelector(selectPageSize);\r\n    const page = useAppSelector(selectPage);\r\n    const sortModel = useAppSelector(selectSortModel);\r\n    const filterModel = useAppSelector(selectFilterModel);\r\n    const navigate = useNavigate();\r\n\r\n    const [dataSource, setDataSource] = React.useState(() => createRTKODataGridDataSource(\r\n        administrationApi.endpoints.atApiServiceEndpointsAdministrationJobsGetJobRunsEndpoint.initiate\r\n        ,\r\n        () => {\r\n            const current = apiRef.current;\r\n            if (!current) return undefined;\r\n            // Collect visible, non-action column fields\r\n            const visible = current.getVisibleColumns?.() ?? [];\r\n            return visible\r\n                .map((c: any) => c.field)\r\n                // Exclude action and internal utility columns like checkbox selection\r\n                .filter((f: string) => f && f !== 'actions' && !f.startsWith('__'));\r\n        }\r\n    ));\r\n\r\n    interface JobRunRow {\r\n        id: number,\r\n        triggered: string | null;\r\n        started: string | null;\r\n        finished: string | null;\r\n        jobName: string;\r\n        authorId?: number;\r\n    }\r\n\r\n    const columns : GridColDef<JobRunRow>[] = [\r\n        {\r\n            field: 'id',\r\n            headerName: 'Id',\r\n            minWidth: 90,\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n            type: 'number',\r\n        },\r\n        {\r\n            field: 'triggered',\r\n            headerName: 'Triggered',\r\n            minWidth: 220,\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n            type: 'dateTime',\r\n            valueGetter: (value) => value ? new Date(value) : null,\r\n        },\r\n        {\r\n            field: 'started',\r\n            headerName: 'Started',\r\n            minWidth: 220,\r\n            type: 'dateTime',\r\n            valueGetter: (value) => value ? new Date(value) : null,\r\n        },\r\n        {\r\n            field: 'finished',\r\n            headerName: 'Finished',\r\n            minWidth: 220,\r\n            type: 'dateTime',\r\n            valueGetter: (value) => value ? new Date(value) : null,\r\n        },\r\n        {\r\n            field: 'jobName',\r\n            headerName: 'Job Name',\r\n            minWidth: 200,\r\n            type: 'string',\r\n        },\r\n        {\r\n            field: 'authorId',\r\n            headerName: 'Author Id',\r\n            width: 90,\r\n            type: 'number',\r\n        },\r\n    ];\r\n\r\n    return (\r\n        <DataGridPremium<JobRunRow>\r\n            apiRef={apiRef}\r\n            columns={columns}\r\n            dataSource={dataSource}\r\n            filterMode='server'\r\n            getRowId={(row) => row.id}\r\n            onRowDoubleClick={(params) => {\r\n                navigate(paths.administration.jobs.jobRuns.view(params.row.id))\r\n            }}\r\n            onDataSourceError={(error) => {\r\n                toast(`DataGrid dataSource error: ${error}`);\r\n            }}\r\n            showToolbar={true}\r\n            disablePivoting\r\n            filterDebounceMs={500}\r\n            ignoreDiacritics\r\n            pagination\r\n            pageSizeOptions={[10, 20, 50, 100]}\r\n            paginationModel={{ pageSize, page }}\r\n            onPaginationModelChange={(model) => {\r\n                if (model.pageSize !== pageSize) {\r\n                    dispatch(setPageSize(model.pageSize));\r\n                }\r\n                if (model.page !== page) {\r\n                    dispatch(setPage(model.page));\r\n                }\r\n            }}\r\n            sortModel={sortModel}\r\n            onSortModelChange={(model) => {\r\n                dispatch(setSortModel(model.map(item => ({\r\n                    field: item.field,\r\n                    sort: item.sort || null\r\n                }))));\r\n            }}\r\n            filterModel={filterModel}\r\n            onFilterModelChange={(model) => {\r\n                dispatch(setFilterModel(model));\r\n            }}\r\n            hideFooterSelectedRowCount\r\n        />\r\n    );\r\n}", "import * as React from \"react\";\nimport Box from \"@mui/material/Box\";\nimport { Helmet } from \"react-helmet-async\";\nimport Tabs from \"@mui/material/Tabs\";\nimport Tab from \"@mui/material/Tab\";\nimport Button from \"@mui/material/Button\";\nimport Stack from \"@mui/material/Stack\";\nimport FilterListIcon from '@mui/icons-material/FilterList';\nimport ViewColumnIcon from '@mui/icons-material/ViewColumn';\nimport AddIcon from '@mui/icons-material/Add';\nimport DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';\nimport PlayArrowOutlinedIcon from '@mui/icons-material/PlayArrowOutlined';\nimport { DataGridPremium, GridColDef, GridRowId, useGridSelector, useGridApiRef, gridRowSelectionIdsSelector } from '@mui/x-data-grid-premium';\nimport { GridPreferencePanelsValue, GridRenderCellParams, gridPreferencePanelStateSelector } from '@mui/x-data-grid-pro';\nimport { useGridPanelContext } from '@mui/x-data-grid-pro/internals';\nimport { GRID_CHECKBOX_SELECTION_FIELD } from '@mui/x-data-grid';\nimport useId from '@mui/utils/useId';\nimport type { Metadata } from \"@/types/metadata\";\nimport { createRTKODataGridDataSource } from \"@/store/api/odata-grid-data-source\";\nimport { useAppDispatch, useAppSelector } from \"@/store/hooks\";\nimport {\n\tsetActiveTab,\n\tsetPageSize,\n\tsetPage,\n\tsetSortModel,\n\tsetFilterModel,\n\tselectActiveTab,\n\tselectPageSize,\n\tselectPage,\n\tselectSortModel,\n\tselectFilterModel\n} from \"@/store/slices/jobs-slice\";\nimport IconButton from \"@mui/material/IconButton\";\nimport { PencilSimpleIcon } from \"@phosphor-icons/react\";\nimport { RouterLink } from \"@/components/core/link\";\nimport { paths } from \"@/paths\";\nimport { JobRunList } from \"./components/job-run-list\";\nimport { administrationApi } from \"@/store/api/administration\";\nimport { useLoaderData } from \"react-router-dom\";\n\nconst metadata = { title: `Jobs` } satisfies Metadata;\n\nfunction JobsToolbar({ apiRef, createNewPath, onDelete, onRun }: { apiRef: any, createNewPath: string, onDelete: (ids: GridRowId[]) => void, onRun: (ids: GridRowId[]) => void }) {\n\tconst selectedIds = useGridSelector(apiRef, gridRowSelectionIdsSelector);\n\tconst selectedCount = selectedIds.size;\n\tconst hasSelection = selectedCount > 0;\n\tconst buttonId = useId();\n\tconst panelId = useId();\n\tconst columnsButtonId = useId();\n\tconst columnsPanelId = useId();\n\tconst panelState = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n\tconst isFiltersOpen = panelState.open && panelState.openedPanelValue === GridPreferencePanelsValue.filters;\n\tconst isColumnsOpen = panelState.open && panelState.openedPanelValue === GridPreferencePanelsValue.columns;\n\tconst { filterPanelTriggerRef, columnsPanelTriggerRef } = useGridPanelContext();\n\treturn (\n\t\t<Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', p: 1 }}>\n\t\t\t<Stack direction=\"row\" spacing={1} alignItems=\"center\">\n\t\t\t\t<Button\n\t\t\t\t\tref={columnsPanelTriggerRef}\n\t\t\t\t\tid={columnsButtonId}\n\t\t\t\t\tvariant=\"outlined\"\n\t\t\t\t\tsx={{ minWidth: 100 }}\n\t\t\t\t\tstartIcon={<ViewColumnIcon />}\n\t\t\t\t\taria-haspopup=\"true\"\n\t\t\t\t\taria-expanded={isColumnsOpen ? 'true' : undefined}\n\t\t\t\t\taria-controls={isColumnsOpen ? columnsPanelId : undefined}\n\t\t\t\t\tonClick={() => {\n\t\t\t\t\t\tconst current = apiRef?.current;\n\t\t\t\t\t\tif (!current) return;\n\t\t\t\t\t\tif (isColumnsOpen) {\n\t\t\t\t\t\t\tcurrent.hidePreferences();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tcurrent.showPreferences(GridPreferencePanelsValue.columns, columnsPanelId, columnsButtonId);\n\t\t\t\t\t\t}\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\tColumns\n\t\t\t\t</Button>\n\t\t\t\t<Button\n\t\t\t\t\tref={filterPanelTriggerRef}\n\t\t\t\t\tid={buttonId}\n\t\t\t\t\tvariant=\"outlined\"\n\t\t\t\t\tsx={{ minWidth: 100 }}\n\t\t\t\t\tstartIcon={<FilterListIcon />}\n\t\t\t\t\taria-haspopup=\"true\"\n\t\t\t\t\taria-expanded={isFiltersOpen ? 'true' : undefined}\n\t\t\t\t\taria-controls={isFiltersOpen ? panelId : undefined}\n\t\t\t\t\tonClick={() => {\n\t\t\t\t\t\tconst current = apiRef?.current;\n\t\t\t\t\t\tif (!current) return;\n\t\t\t\t\t\tif (isFiltersOpen) {\n\t\t\t\t\t\t\tcurrent.hidePreferences();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tcurrent.showPreferences(GridPreferencePanelsValue.filters, panelId, buttonId);\n\t\t\t\t\t\t}\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\tFilter\n\t\t\t\t</Button>\n\t\t\t</Stack>\n\t\t\t<Stack direction=\"row\" spacing={1} alignItems=\"center\">\n\t\t\t\t{!hasSelection ? (\n\t\t\t\t\t<Button startIcon={<AddIcon />} variant=\"contained\" component={RouterLink} href={`${createNewPath}`}>\n\t\t\t\t\t\tCreate New\n\t\t\t\t\t</Button>\n\t\t\t\t) : (\n\t\t\t\t\t<>\n\t\t\t\t\t\t<Box sx={{ display: 'flex', alignItems: 'center', fontSize: 14, color: 'text.secondary', pr: 0.5 }}>\n\t\t\t\t\t\t\t{selectedCount} selected&nbsp;&nbsp;&nbsp;|\n\t\t\t\t\t\t</Box>\n\t\t\t\t\t\t<Button startIcon={<DeleteOutlinedIcon />} color=\"error\" variant=\"text\" onClick={() => onDelete(Array.from(selectedIds.keys()))} sx={{ border: 'none', minWidth: 90 }}>\n\t\t\t\t\t\t\tDelete\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t<Button startIcon={<PlayArrowOutlinedIcon />} variant=\"contained\" color=\"primary\" onClick={() => onRun(Array.from(selectedIds.keys()))}>\n\t\t\t\t\t\t\tRun\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</>\n\t\t\t\t)}\n\t\t\t</Stack>\n\t\t</Box>\n\t);\n}\n\ntype LoaderData = {\n    showLogs: boolean;\n};\n\nexport function Page(): React.JSX.Element {\n\tconst dispatch = useAppDispatch();\n\tconst tab = useAppSelector(selectActiveTab);\n\tconst pageSize = useAppSelector(selectPageSize);\n\tconst page = useAppSelector(selectPage);\n\tconst sortModel = useAppSelector(selectSortModel);\n\tconst filterModel = useAppSelector(selectFilterModel);\n\tconst apiRef = useGridApiRef();\n\tconst { showLogs } = useLoaderData() as LoaderData;\n\n\tconst [dataSource, setDataSource] = React.useState(() => createRTKODataGridDataSource(\n\t\tadministrationApi.endpoints.atApiServiceEndpointsAdministrationJobsGetJobsEndpoint.initiate,\n\t\t() => {\n\t\t\tconst current = apiRef.current;\n\t\t\tif (!current) return undefined;\n\t\t\t// Collect visible, non-action column fields\n\t\t\tconst visible = current.getVisibleColumns?.() ?? [];\n\t\t\treturn visible\n\t\t\t\t.map((c: any) => c.field)\n\t\t\t\t// Exclude action and internal utility columns like checkbox selection\n\t\t\t\t.filter((f: string) => f && f !== 'actions' && !f.startsWith('__'));\n\t\t}\n\t));\n\n\tReact.useEffect(\n\t\t() => {\n\t\t\tif (showLogs){\n\t\t\t\tdispatch(setActiveTab(1));\n\t\t\t}\n\t\t},\n\t\t[]\n\t)\n\n\tconst columns : GridColDef[] = [\n\t\t{\n\t\t\tfield: 'id',\n\t\t\theaderName: 'Id',\n\t\t\twidth: 90,\n\t\t\ttype: 'number',\n\t\t},\n\t\t{\n\t\t\tfield: 'name',\n\t\t\theaderName: 'Name',\n\t\t\twidth: 400,\n\t\t\ttype: 'string',\n\t\t\t// editable: true,\n\t\t},\n\t\t{\n\t\t\tfield: 'description',\n\t\t\theaderName: 'Description',\n\t\t\twidth: 400,\n\t\t\ttype: 'string',\n\t\t\t// editable: true,\n\t\t},\n\t\t{\n\t\t\tfield: 'parameters',\n\t\t\theaderName: 'Parameters',\n\t\t\twidth: 500,\n\t\t\ttype: 'string',\n\t\t\t// editable: true,\n\t\t},\n\t\t{\n\t\t\tfield: 'disabled',\n\t\t\theaderName: 'Disabled',\n\t\t\twidth: 100,\n\t\t\ttype: 'boolean',\n\t\t\t// editable: true,\n\t\t},\n\t\t{\n\t\t\tfield: 'successEmails',\n\t\t\theaderName: 'Success Emails',\n\t\t\twidth: 200,\n\t\t\ttype: 'string',\n\t\t\t// editable: true,\n\t\t},\n\t\t{\n\t\t\tfield: 'errorEmails',\n\t\t\theaderName: 'Error Emails',\n\t\t\twidth: 200,\n\t\t\ttype: 'string',\n\t\t\t// editable: true,\n\t\t},\n\t\t{\n\t\t\tfield: 'type',\n\t\t\theaderName: 'Type',\n\t\t\twidth: 200,\n\t\t\t// type: 'singleSelect',\n\t\t\t// valueOptions: ['ReportJob', 'MyJobType', 'CustomJobType'],\n\t\t\t// editable: true,\n\t\t},\n\t\t{\n\t\t\tfield: 'actions',\n\t\t\trenderCell: (params: GridRenderCellParams) => {\n\t\t\t\treturn (\n\t\t\t\t\t<IconButton component={RouterLink} href={paths.administration.jobs.edit(params.row.id)}>\n\t\t\t\t\t\t<PencilSimpleIcon />\n\t\t\t\t\t</IconButton>\n\t\t\t\t)\n\t\t\t},\n\t\t\theaderName: \"Actions\",\n\t\t\twidth: 100,\n\t\t\talign: \"right\",\n\t\t\ttype: 'actions',\n\t\t},\n\t];\n\n\tconst handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\n\t\tdispatch(setActiveTab(newValue));\n\t};\n\n\tconst handleDelete = (ids: GridRowId[]) => {\n\t\talert('Delete selected jobs: ' + ids.join(', '));\n\t};\n\tconst handleRun = (ids: GridRowId[]) => {\n\t\talert('Run selected jobs: ' + ids.join(', '));\n\t};\n\n\tconst slots = React.useMemo(() => ({\n\t\ttoolbar: () => (\n\t\t\t<JobsToolbar\n\t\t\t\tapiRef={apiRef}\n\t\t\t\tcreateNewPath={paths.administration.jobs.create}\n\t\t\t\tonDelete={handleDelete}\n\t\t\t\tonRun={handleRun}\n\t\t\t/>\n\t\t)\n\t}), [apiRef]);\n\n\treturn (\n\t\t<React.Fragment>\n\t\t\t<Helmet>\n\t\t\t\t<title>{metadata.title}</title>\n\t\t\t</Helmet>\n\t\t\t<Box\n\t\t\t\tsx={{\n\t\t\t\t\tmaxWidth: \"var(--Content-maxWidth)\",\n\t\t\t\t\tm: \"var(--Content-margin)\",\n\t\t\t\t\tp: \"var(--Content-padding)\",\n\t\t\t\t\twidth: \"var(--Content-width)\",\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<Tabs value={tab} onChange={handleTabChange} sx={{ mb: 2 }}>\n\t\t\t\t\t<Tab label=\"Jobs\" />\n\t\t\t\t\t<Tab label=\"Logs\" />\n\t\t\t\t</Tabs>\n\t\t\t\t<Box sx={{ display: tab === 0 ? 'block' : 'none' }}>\n\t\t\t\t\t<DataGridPremium\n\t\t\t\t\t\tapiRef={apiRef}\n\t\t\t\t\t\tcolumns={columns}\n\t\t\t\t\t\tdataSource={dataSource}\n\t\t\t\t\t\tfilterMode=\"server\"\n\t\t\t\t\t\tshowToolbar={true}\n\t\t\t\t\t\tdisablePivoting\n\t\t\t\t\t\tfilterDebounceMs={500}\n\t\t\t\t\t\tignoreDiacritics\n\t\t\t\t\t\tpagination\n\t\t\t\t\t\tpageSizeOptions={[10, 20, 50, 100]}\n\t\t\t\t\t\tpaginationModel={{ pageSize, page }}\n\t\t\t\t\t\tonPaginationModelChange={(model) => {\n\t\t\t\t\t\t\tif (model.pageSize !== pageSize) {\n\t\t\t\t\t\t\t\tdispatch(setPageSize(model.pageSize));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (model.page !== page) {\n\t\t\t\t\t\t\t\tdispatch(setPage(model.page));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}}\n\t\t\t\t\t\tsortModel={sortModel}\n\t\t\t\t\t\tonSortModelChange={(model) => {\n\t\t\t\t\t\t\tdispatch(setSortModel(model.map(item => ({\n\t\t\t\t\t\t\t\tfield: item.field,\n\t\t\t\t\t\t\t\tsort: item.sort ?? null\n\t\t\t\t\t\t\t}))));\n\t\t\t\t\t\t}}\n\t\t\t\t\t\tfilterModel={filterModel}\n\t\t\t\t\t\tonFilterModelChange={(model) => {\n\t\t\t\t\t\t\tdispatch(setFilterModel(model));\n\t\t\t\t\t\t}}\n\t\t\t\t\t\tcheckboxSelection\n\t\t\t\t\t\tslots={slots}\n\t\t\t\t\t\tslotProps={{\n\t\t\t\t\t\t\tcolumnsManagement: {\n\t\t\t\t\t\t\t\t// Do not display the checkbox column in the columns panel.\n\t\t\t\t\t\t\t\tgetTogglableColumns: (cols: GridColDef[]): string[] =>\n\t\t\t\t\t\t\t\t\tcols\n\t\t\t\t\t\t\t\t\t\t.map((col: GridColDef) => col.field)\n\t\t\t\t\t\t\t\t\t\t.filter((field: string) => field !== GRID_CHECKBOX_SELECTION_FIELD),\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t}}\n\t\t\t\t\t\thideFooterSelectedRowCount\n\t\t\t\t\t/>\n\t\t\t\t</Box>\n\t\t\t\t<Box sx={{ display: tab === 1 ? 'block' : 'none' }}>\n\t\t\t\t\t<JobRunList/>\n\t\t\t\t</Box>\n\t\t\t</Box>\n\t\t</React.Fragment>\n\t);\n}\n"], "names": ["FilterListIcon", "createSvgIcon", "_jsx", "ViewColumnIcon", "AddIcon", "DeleteOutlinedIcon", "PlayArrowOutlinedIcon", "useAppDispatch", "useDispatch", "useAppSelector", "useSelector", "JobRunList", "apiRef", "useGridApiRef", "dispatch", "pageSize", "selectPageSize", "page", "selectPage", "sortModel", "selectSortModel", "filterModel", "selectFilterModel", "navigate", "useNavigate", "dataSource", "setDataSource", "React", "createRTKODataGridDataSource", "administrationApi", "current", "_a", "c", "f", "columns", "value", "jsx", "DataGridPremium", "row", "params", "paths", "error", "toast", "model", "setPageSize", "setPage", "setSortModel", "item", "setFilterModel", "metadata", "JobsToolbar", "createNewPath", "onDelete", "onRun", "selectedIds", "useGridSelector", "gridRowSelectionIdsSelector", "selectedCount", "hasSelection", "buttonId", "useId", "panelId", "columnsButtonId", "columnsPanelId", "panelState", "gridPreferencePanelStateSelector", "isFiltersOpen", "GridPreferencePanelsValue", "isColumnsOpen", "filterPanelTriggerRef", "columnsPanelTriggerRef", "useGridPanelContext", "jsxs", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "Fragment", "RouterLink", "Page", "tab", "selectActiveTab", "showLogs", "useLoaderData", "React.useState", "React.useEffect", "setActiveTab", "IconButton", "PencilSimpleIcon", "handleTabChange", "event", "newValue", "handleDelete", "ids", "handleRun", "slots", "React.useMemo", "React.Fragment", "<PERSON><PERSON><PERSON>", "Tabs", "Tab", "cols", "col", "field", "GRID_CHECKBOX_SELECTION_FIELD"], "mappings": "6uBAIA,MAAAA,GAAeC,EAA2BC,EAAAA,IAAK,OAAQ,CACrD,EAAG,0CACL,CAAC,CAAe,ECFhBC,GAAeF,EAA2BC,EAAAA,IAAK,OAAQ,CACrD,EAAG,sDACL,CAAC,CAAe,ECFhBE,GAAeH,EAA2BC,EAAAA,IAAK,OAAQ,CACrD,EAAG,mCACL,CAAC,CAAQ,ECFTG,GAAeJ,EAA2BC,EAAAA,IAAK,OAAQ,CACrD,EAAG,uFACL,CAAC,CAAmB,ECFpBI,GAAeL,EAA2BC,EAAAA,IAAK,OAAQ,CACrD,EAAG,0CACL,CAAC,CAAsB,ECDVK,EAAiB,IAAMC,GAAA,EACvBC,EAAkDC,GCKxD,SAASC,IAAgC,CAC5C,MAAMC,EAASC,EAAA,EACTC,EAAWP,EAAA,EACXQ,EAAWN,EAAeO,CAAc,EACxCC,EAAOR,EAAeS,CAAU,EAChCC,EAAYV,EAAeW,CAAe,EAC1CC,EAAcZ,EAAea,CAAiB,EAC9CC,EAAWC,GAAA,EAEX,CAACC,EAAYC,CAAa,EAAIC,EAAM,SAAS,IAAMC,EACrDC,EAAkB,UAAU,0DAA0D,SAEtF,IAAM,OACF,MAAMC,EAAUlB,EAAO,QACvB,OAAKkB,KAEWC,EAAAD,EAAQ,oBAAR,YAAAC,EAAA,KAAAD,KAAiC,CAAA,GAE5C,IAAKE,GAAWA,EAAE,KAAK,EAEvB,OAAQC,GAAcA,GAAKA,IAAM,WAAa,CAACA,EAAE,WAAW,IAAI,CAAC,EANxD,MAOlB,CAAA,CACH,EAWKC,EAAoC,CACtC,CACI,MAAO,KACP,WAAY,KACZ,SAAU,GACV,MAAO,SACP,YAAa,SACb,KAAM,QAAA,EAEV,CACI,MAAO,YACP,WAAY,YACZ,SAAU,IACV,MAAO,SACP,YAAa,SACb,KAAM,WACN,YAAcC,GAAUA,EAAQ,IAAI,KAAKA,CAAK,EAAI,IAAA,EAEtD,CACI,MAAO,UACP,WAAY,UACZ,SAAU,IACV,KAAM,WACN,YAAcA,GAAUA,EAAQ,IAAI,KAAKA,CAAK,EAAI,IAAA,EAEtD,CACI,MAAO,WACP,WAAY,WACZ,SAAU,IACV,KAAM,WACN,YAAcA,GAAUA,EAAQ,IAAI,KAAKA,CAAK,EAAI,IAAA,EAEtD,CACI,MAAO,UACP,WAAY,WACZ,SAAU,IACV,KAAM,QAAA,EAEV,CACI,MAAO,WACP,WAAY,YACZ,MAAO,GACP,KAAM,QAAA,CACV,EAGJ,OACIC,EAAAA,IAACC,EAAA,CACG,OAAAzB,EACA,QAAAsB,EACA,WAAAT,EACA,WAAW,SACX,SAAWa,GAAQA,EAAI,GACvB,iBAAmBC,GAAW,CAC1BhB,EAASiB,EAAM,eAAe,KAAK,QAAQ,KAAKD,EAAO,IAAI,EAAE,CAAC,CAClE,EACA,kBAAoBE,GAAU,CAC1BC,EAAM,8BAA8BD,CAAK,EAAE,CAC/C,EACA,YAAa,GACb,gBAAe,GACf,iBAAkB,IAClB,iBAAgB,GAChB,WAAU,GACV,gBAAiB,CAAC,GAAI,GAAI,GAAI,GAAG,EACjC,gBAAiB,CAAE,SAAA1B,EAAU,KAAAE,CAAA,EAC7B,wBAA0B0B,GAAU,CAC5BA,EAAM,WAAa5B,GACnBD,EAAS8B,EAAYD,EAAM,QAAQ,CAAC,EAEpCA,EAAM,OAAS1B,GACfH,EAAS+B,EAAQF,EAAM,IAAI,CAAC,CAEpC,EACA,UAAAxB,EACA,kBAAoBwB,GAAU,CAC1B7B,EAASgC,EAAaH,EAAM,IAAII,IAAS,CACrC,MAAOA,EAAK,MACZ,KAAMA,EAAK,MAAQ,IAAA,EACrB,CAAC,CAAC,CACR,EACA,YAAA1B,EACA,oBAAsBsB,GAAU,CAC5B7B,EAASkC,EAAeL,CAAK,CAAC,CAClC,EACA,2BAA0B,EAAA,CAAA,CAGtC,CC5FA,MAAMM,GAAW,CAAE,MAAO,MAAA,EAE1B,SAASC,GAAY,CAAE,OAAAtC,EAAQ,cAAAuC,EAAe,SAAAC,EAAU,MAAAC,GAA0H,CACjL,MAAMC,EAAcC,EAAgB3C,EAAQ4C,EAA2B,EACjEC,EAAgBH,EAAY,KAC5BI,EAAeD,EAAgB,EAC/BE,EAAWC,EAAA,EACXC,EAAUD,EAAA,EACVE,EAAkBF,EAAA,EAClBG,EAAiBH,EAAA,EACjBI,EAAaT,EAAgB3C,EAAQqD,EAAgC,EACrEC,EAAgBF,EAAW,MAAQA,EAAW,mBAAqBG,EAA0B,QAC7FC,EAAgBJ,EAAW,MAAQA,EAAW,mBAAqBG,EAA0B,QAC7F,CAAE,sBAAAE,EAAuB,uBAAAC,CAAA,EAA2BC,GAAA,EAC1D,OACCC,EAAAA,KAACC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,gBAAiB,EAAG,CAAA,EACrF,SAAA,CAAAD,OAACE,GAAM,UAAU,MAAM,QAAS,EAAG,WAAW,SAC7C,SAAA,CAAAtC,EAAAA,IAACuC,EAAA,CACA,IAAKL,EACL,GAAIR,EACJ,QAAQ,WACR,GAAI,CAAE,SAAU,GAAA,EAChB,gBAAY3D,GAAA,EAAe,EAC3B,gBAAc,OACd,gBAAeiE,EAAgB,OAAS,OACxC,gBAAeA,EAAgBL,EAAiB,OAChD,QAAS,IAAM,CACd,MAAMjC,EAAUlB,GAAA,YAAAA,EAAQ,QACnBkB,IACDsC,EACHtC,EAAQ,gBAAA,EAERA,EAAQ,gBAAgBqC,EAA0B,QAASJ,EAAgBD,CAAe,EAE5F,EACA,SAAA,SAAA,CAAA,EAGD1B,EAAAA,IAACuC,EAAA,CACA,IAAKN,EACL,GAAIV,EACJ,QAAQ,WACR,GAAI,CAAE,SAAU,GAAA,EAChB,gBAAY3D,GAAA,EAAe,EAC3B,gBAAc,OACd,gBAAekE,EAAgB,OAAS,OACxC,gBAAeA,EAAgBL,EAAU,OACzC,QAAS,IAAM,CACd,MAAM/B,EAAUlB,GAAA,YAAAA,EAAQ,QACnBkB,IACDoC,EACHpC,EAAQ,gBAAA,EAERA,EAAQ,gBAAgBqC,EAA0B,QAASN,EAASF,CAAQ,EAE9E,EACA,SAAA,QAAA,CAAA,CAED,EACD,EACAvB,EAAAA,IAACsC,EAAA,CAAM,UAAU,MAAM,QAAS,EAAG,WAAW,SAC5C,SAAChB,EAKDc,EAAAA,KAAAI,EAAAA,SAAA,CACC,SAAA,CAAAJ,EAAAA,KAACC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,SAAU,GAAI,MAAO,iBAAkB,GAAI,IAC3F,SAAA,CAAAhB,EAAc,eAAA,EAChB,EACArB,EAAAA,IAACuC,EAAA,CAAO,UAAWvC,EAAAA,IAAC/B,GAAA,CAAA,CAAmB,EAAI,MAAM,QAAQ,QAAQ,OAAO,QAAS,IAAM+C,EAAS,MAAM,KAAKE,EAAY,MAAM,CAAC,EAAG,GAAI,CAAE,OAAQ,OAAQ,SAAU,EAAA,EAAM,SAAA,QAAA,CAEvK,EACAlB,MAACuC,GAAO,UAAWvC,EAAAA,IAAC9B,KAAsB,EAAI,QAAQ,YAAY,MAAM,UAAU,QAAS,IAAM+C,EAAM,MAAM,KAAKC,EAAY,MAAM,CAAC,EAAG,SAAA,KAAA,CAExI,CAAA,CAAA,CACD,EAdAlB,EAAAA,IAACuC,EAAA,CAAO,gBAAYvE,GAAA,CAAA,CAAQ,EAAI,QAAQ,YAAY,UAAWyE,EAAY,KAAM,GAAG1B,CAAa,GAAI,SAAA,YAAA,CAErG,CAYA,CAEF,CAAA,EACD,CAEF,CAMO,SAAS2B,IAA0B,CACzC,MAAMhE,EAAWP,EAAA,EACXwE,EAAMtE,EAAeuE,CAAe,EACpCjE,EAAWN,EAAeO,CAAc,EACxCC,EAAOR,EAAeS,CAAU,EAChCC,EAAYV,EAAeW,CAAe,EAC1CC,EAAcZ,EAAea,EAAiB,EAC9CV,EAASC,EAAA,EACT,CAAE,SAAAoE,CAAA,EAAaC,GAAA,EAEf,CAACzD,EAAYC,CAAa,EAAIyD,EAAAA,SAAe,IAAMvD,EACxDC,EAAkB,UAAU,uDAAuD,SACnF,IAAM,OACL,MAAMC,EAAUlB,EAAO,QACvB,OAAKkB,KAEWC,EAAAD,EAAQ,oBAAR,YAAAC,EAAA,KAAAD,KAAiC,CAAA,GAE/C,IAAKE,GAAWA,EAAE,KAAK,EAEvB,OAAQC,GAAcA,GAAKA,IAAM,WAAa,CAACA,EAAE,WAAW,IAAI,CAAC,EANrD,MAOf,CAAA,CACA,EAEDmD,EAAAA,UACC,IAAM,CACDH,GACHnE,EAASuE,EAAa,CAAC,CAAC,CAE1B,EACA,CAAA,CAAC,EAGF,MAAMnD,EAAyB,CAC9B,CACC,MAAO,KACP,WAAY,KACZ,MAAO,GACP,KAAM,QAAA,EAEP,CACC,MAAO,OACP,WAAY,OACZ,MAAO,IACP,KAAM,QAAA,EAGP,CACC,MAAO,cACP,WAAY,cACZ,MAAO,IACP,KAAM,QAAA,EAGP,CACC,MAAO,aACP,WAAY,aACZ,MAAO,IACP,KAAM,QAAA,EAGP,CACC,MAAO,WACP,WAAY,WACZ,MAAO,IACP,KAAM,SAAA,EAGP,CACC,MAAO,gBACP,WAAY,iBACZ,MAAO,IACP,KAAM,QAAA,EAGP,CACC,MAAO,cACP,WAAY,eACZ,MAAO,IACP,KAAM,QAAA,EAGP,CACC,MAAO,OACP,WAAY,OACZ,MAAO,GAAA,EAKR,CACC,MAAO,UACP,WAAaK,GAEXH,EAAAA,IAACkD,EAAA,CAAW,UAAWT,EAAY,KAAMrC,EAAM,eAAe,KAAK,KAAKD,EAAO,IAAI,EAAE,EACpF,SAAAH,EAAAA,IAACmD,KAAiB,EACnB,EAGF,WAAY,UACZ,MAAO,IACP,MAAO,QACP,KAAM,SAAA,CACP,EAGKC,EAAkB,CAACC,EAA6BC,IAAqB,CAC1E5E,EAASuE,EAAaK,CAAQ,CAAC,CAChC,EAEMC,EAAgBC,GAAqB,CAC1C,MAAM,yBAA2BA,EAAI,KAAK,IAAI,CAAC,CAChD,EACMC,EAAaD,GAAqB,CACvC,MAAM,sBAAwBA,EAAI,KAAK,IAAI,CAAC,CAC7C,EAEME,EAAQC,EAAAA,QAAc,KAAO,CAClC,QAAS,IACR3D,EAAAA,IAACc,GAAA,CACA,OAAAtC,EACA,cAAe4B,EAAM,eAAe,KAAK,OACzC,SAAUmD,EACV,MAAOE,CAAA,CAAA,CACR,GAEE,CAACjF,CAAM,CAAC,EAEZ,OACC4D,EAAAA,KAACwB,WAAA,CACA,SAAA,CAAA5D,EAAAA,IAAC6D,GAAA,CACA,SAAA7D,MAAC,QAAA,CAAO,SAAAa,GAAS,MAAM,EACxB,EACAuB,EAAAA,KAACC,EAAA,CACA,GAAI,CACH,SAAU,0BACV,EAAG,wBACH,EAAG,yBACH,MAAO,sBAAA,EAGR,SAAA,CAAAD,EAAAA,KAAC0B,EAAA,CAAK,MAAOnB,EAAK,SAAUS,EAAiB,GAAI,CAAE,GAAI,CAAA,EACtD,SAAA,CAAApD,EAAAA,IAAC+D,EAAA,CAAI,MAAM,MAAA,CAAO,EAClB/D,EAAAA,IAAC+D,EAAA,CAAI,MAAM,MAAA,CAAO,CAAA,EACnB,EACA/D,EAAAA,IAACqC,GAAI,GAAI,CAAE,QAASM,IAAQ,EAAI,QAAU,MAAA,EACzC,SAAA3C,EAAAA,IAACC,EAAA,CACA,OAAAzB,EACA,QAAAsB,EACA,WAAAT,EACA,WAAW,SACX,YAAa,GACb,gBAAe,GACf,iBAAkB,IAClB,iBAAgB,GAChB,WAAU,GACV,gBAAiB,CAAC,GAAI,GAAI,GAAI,GAAG,EACjC,gBAAiB,CAAE,SAAAV,EAAU,KAAAE,CAAA,EAC7B,wBAA0B0B,GAAU,CAC/BA,EAAM,WAAa5B,GACtBD,EAAS8B,GAAYD,EAAM,QAAQ,CAAC,EAEjCA,EAAM,OAAS1B,GAClBH,EAAS+B,GAAQF,EAAM,IAAI,CAAC,CAE9B,EACA,UAAAxB,EACA,kBAAoBwB,GAAU,CAC7B7B,EAASgC,GAAaH,EAAM,IAAII,IAAS,CACxC,MAAOA,EAAK,MACZ,KAAMA,EAAK,MAAQ,IAAA,EAClB,CAAC,CAAC,CACL,EACA,YAAA1B,EACA,oBAAsBsB,GAAU,CAC/B7B,EAASkC,GAAeL,CAAK,CAAC,CAC/B,EACA,kBAAiB,GACjB,MAAAmD,EACA,UAAW,CACV,kBAAmB,CAElB,oBAAsBM,GACrBA,EACE,IAAKC,GAAoBA,EAAI,KAAK,EAClC,OAAQC,GAAkBA,IAAUC,EAA6B,CAAA,CACrE,EAED,2BAA0B,EAAA,CAAA,EAE5B,EACAnE,EAAAA,IAACqC,EAAA,CAAI,GAAI,CAAE,QAASM,IAAQ,EAAI,QAAU,MAAA,EACzC,SAAA3C,EAAAA,IAACzB,GAAA,CAAA,CAAU,CAAA,CACZ,CAAA,CAAA,CAAA,CACD,EACD,CAEF", "x_google_ignoreList": [0, 1, 2, 3, 4]}