import{j as e,r as s,B as t,C as i,S as r,T as n,b as a}from"./mui-51Y1Yx8M.js";import{H as o,a as c,b as x,p as l,R as h}from"./index-CQLEdRvh.js";import"./vendor-Csw2ODfV.js";import"./router-Bd8Y3ArC.js";import"./redux-BKAL-i9G.js";const m={title:`Internal server error | Errors | ${c.name}`};function y(){return e.jsxs(s.Fragment,{children:[e.jsx(o,{children:e.jsx("title",{children:m.title})}),e.jsx(t,{component:"main",sx:{alignItems:"center",display:"flex",flexDirection:"column",justifyContent:"center",minHeight:"100%",py:"64px"},children:e.jsx(i,{maxWidth:"lg",children:e.jsxs(r,{spacing:6,children:[e.jsx(t,{sx:{display:"flex",justifyContent:"center"},children:e.jsx(t,{alt:"Internal server error",component:"img",src:x.error(),sx:{height:"auto",maxWidth:"100%",width:"200px"}})}),e.jsxs(r,{spacing:1,sx:{textAlign:"center"},children:[e.jsx(n,{variant:"h4",children:"500: Internal server error"}),e.jsx(n,{color:"text.secondary",children:"You either tried some shady route or you came here by mistake. Whichever it is, try using the navigation."})]}),e.jsx(t,{sx:{display:"flex",justifyContent:"center"},children:e.jsx(a,{component:h,href:l.home,variant:"contained",children:"Back to home"})})]})})})]})}export{y as Page};
//# sourceMappingURL=internal-server-error.BGmZPbfS.js.map
