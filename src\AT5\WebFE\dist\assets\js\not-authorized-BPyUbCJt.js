import{j as t,r as n,B as e,C as s,S as i,T as r,b as a}from"./mui-51Y1Yx8M.js";import{H as o,a as c,b as h,p as x,R as l}from"./index-CQLEdRvh.js";import"./vendor-Csw2ODfV.js";import"./router-Bd8Y3ArC.js";import"./redux-BKAL-i9G.js";const m={title:`Not authorized | Errors | ${c.name}`};function y(){return t.jsxs(n.Fragment,{children:[t.jsx(o,{children:t.jsx("title",{children:m.title})}),t.jsx(e,{component:"main",sx:{alignItems:"center",display:"flex",flexDirection:"column",justifyContent:"center",minHeight:"100%",py:"64px"},children:t.jsx(s,{maxWidth:"lg",children:t.jsxs(i,{spacing:6,children:[t.jsx(e,{sx:{display:"flex",justifyContent:"center"},children:t.jsx(e,{alt:"Not authorized",component:"img",src:h.error(),sx:{height:"auto",maxWidth:"100%",width:"200px"}})}),t.jsxs(i,{spacing:1,sx:{textAlign:"center"},children:[t.jsx(r,{variant:"h4",children:"401: Authorization required"}),t.jsx(r,{color:"text.secondary",children:"You either tried some shady route or you came here by mistake. Whichever it is, try using the navigation."})]}),t.jsx(e,{sx:{display:"flex",justifyContent:"center"},children:t.jsx(a,{component:l,href:x.home,variant:"contained",children:"Back to home"})})]})})})]})}export{y as Page};
//# sourceMappingURL=not-authorized.DMwmtVpS.js.map
