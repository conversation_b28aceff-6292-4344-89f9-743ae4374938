import{r as g,a1 as Cx,j as T,a2 as hr,a3 as da,A as Nt,I as pa,L as Wf,h as Uf,i as Ix,k as Gf,f as kx,l as Kf,T as ie,g as dr,a4 as va,b as Rt,a5 as Q,R as P,a6 as ne,a7 as rn,B as Ve,S as Me,a8 as Rx,s as Dx,a9 as We}from"./mui-51Y1Yx8M.js";import{w as Ze,d as Xe,H as Nx,a as Lx,r as qx,b as ct,e as Bx}from"./index-CQLEdRvh.js";import{e as Hx}from"./Plus.es-D5ayH55j.js";import{c as Pa,g as se}from"./vendor-Csw2ODfV.js";import"./router-Bd8Y3ArC.js";import"./redux-BKAL-i9G.js";function eg(e){const{children:t,defer:r=!1,fallback:n=null}=e,[a,i]=g.useState(!1);return Cx(()=>{r||i(!0)},[r]),g.useEffect(()=>{r&&i(!0)},[r]),T.jsx(g.Fragment,{children:a?t:n})}const Fx=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M224.49,136.49l-72,72a12,12,0,0,1-17-17L187,140H40a12,12,0,0,1,0-24H187L135.51,64.48a12,12,0,0,1,17-17l72,72A12,12,0,0,1,224.49,136.49Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M216,128l-72,72V56Z",opacity:"0.2"}),g.createElement("path",{d:"M221.66,122.34l-72-72A8,8,0,0,0,136,56v64H40a8,8,0,0,0,0,16h96v64a8,8,0,0,0,13.66,5.66l72-72A8,8,0,0,0,221.66,122.34ZM152,180.69V75.31L204.69,128Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M221.66,133.66l-72,72A8,8,0,0,1,136,200V136H40a8,8,0,0,1,0-16h96V56a8,8,0,0,1,13.66-5.66l72,72A8,8,0,0,1,221.66,133.66Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M220.24,132.24l-72,72a6,6,0,0,1-8.48-8.48L201.51,134H40a6,6,0,0,1,0-12H201.51L139.76,60.24a6,6,0,0,1,8.48-8.48l72,72A6,6,0,0,1,220.24,132.24Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M218.83,130.83l-72,72a4,4,0,0,1-5.66-5.66L206.34,132H40a4,4,0,0,1,0-8H206.34L141.17,58.83a4,4,0,0,1,5.66-5.66l72,72A4,4,0,0,1,218.83,130.83Z"}))]]),ar=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:Fx}));ar.displayName="ArrowRightIcon";const Vx=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M100,100a12,12,0,0,1,12-12h32a12,12,0,0,1,0,24H112A12,12,0,0,1,100,100ZM236,68V196a20,20,0,0,1-20,20H40a20,20,0,0,1-20-20V68A20,20,0,0,1,40,48H76V40a28,28,0,0,1,28-28h48a28,28,0,0,1,28,28v8h36A20,20,0,0,1,236,68ZM100,48h56V40a4,4,0,0,0-4-4H104a4,4,0,0,0-4,4ZM44,72v35.23A180.06,180.06,0,0,0,128,128a180,180,0,0,0,84-20.78V72ZM212,192V133.94A204.27,204.27,0,0,1,128,152a204.21,204.21,0,0,1-84-18.06V192Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M224,118.31V200a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V118.31h0A191.14,191.14,0,0,0,128,144,191.08,191.08,0,0,0,224,118.31Z",opacity:"0.2"}),g.createElement("path",{d:"M104,112a8,8,0,0,1,8-8h32a8,8,0,0,1,0,16H112A8,8,0,0,1,104,112ZM232,72V200a16,16,0,0,1-16,16H40a16,16,0,0,1-16-16V72A16,16,0,0,1,40,56H80V48a24,24,0,0,1,24-24h48a24,24,0,0,1,24,24v8h40A16,16,0,0,1,232,72ZM96,56h64V48a8,8,0,0,0-8-8H104a8,8,0,0,0-8,8ZM40,72v41.62A184.07,184.07,0,0,0,128,136a184,184,0,0,0,88-22.39V72ZM216,200V131.63A200.25,200.25,0,0,1,128,152a200.19,200.19,0,0,1-88-20.36V200H216Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M152,112a8,8,0,0,1-8,8H112a8,8,0,0,1,0-16h32A8,8,0,0,1,152,112Zm80-40V200a16,16,0,0,1-16,16H40a16,16,0,0,1-16-16V72A16,16,0,0,1,40,56H80V48a24,24,0,0,1,24-24h48a24,24,0,0,1,24,24v8h40A16,16,0,0,1,232,72ZM96,56h64V48a8,8,0,0,0-8-8H104a8,8,0,0,0-8,8Zm120,57.61V72H40v41.61A184,184,0,0,0,128,136,184,184,0,0,0,216,113.61Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M106,112a6,6,0,0,1,6-6h32a6,6,0,0,1,0,12H112A6,6,0,0,1,106,112ZM230,72V200a14,14,0,0,1-14,14H40a14,14,0,0,1-14-14V72A14,14,0,0,1,40,58H82V48a22,22,0,0,1,22-22h48a22,22,0,0,1,22,22V58h42A14,14,0,0,1,230,72ZM94,58h68V48a10,10,0,0,0-10-10H104A10,10,0,0,0,94,48ZM38,72v42.79A186,186,0,0,0,128,138a185.91,185.91,0,0,0,90-23.22V72a2,2,0,0,0-2-2H40A2,2,0,0,0,38,72ZM218,200V128.37A198.12,198.12,0,0,1,128,150a198.05,198.05,0,0,1-90-21.62V200a2,2,0,0,0,2,2H216A2,2,0,0,0,218,200Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M216,56H176V48a24,24,0,0,0-24-24H104A24,24,0,0,0,80,48v8H40A16,16,0,0,0,24,72V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V72A16,16,0,0,0,216,56ZM96,48a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8v8H96ZM216,72v41.61A184,184,0,0,1,128,136a184.07,184.07,0,0,1-88-22.38V72Zm0,128H40V131.64A200.19,200.19,0,0,0,128,152a200.25,200.25,0,0,0,88-20.37V200ZM104,112a8,8,0,0,1,8-8h32a8,8,0,0,1,0,16H112A8,8,0,0,1,104,112Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M108,112a4,4,0,0,1,4-4h32a4,4,0,0,1,0,8H112A4,4,0,0,1,108,112ZM228,72V200a12,12,0,0,1-12,12H40a12,12,0,0,1-12-12V72A12,12,0,0,1,40,60H84V48a20,20,0,0,1,20-20h48a20,20,0,0,1,20,20V60h44A12,12,0,0,1,228,72ZM92,60h72V48a12,12,0,0,0-12-12H104A12,12,0,0,0,92,48ZM36,72v44a188,188,0,0,0,92,24,188,188,0,0,0,92-24V72a4,4,0,0,0-4-4H40A4,4,0,0,0,36,72ZM220,200V125.1A196.06,196.06,0,0,1,128,148a196,196,0,0,1-92-22.9V200a4,4,0,0,0,4,4H216A4,4,0,0,0,220,200Z"}))]]),tg=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:Vx}));tg.displayName="BriefcaseIcon";const zx=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M180.49,143.51a12,12,0,0,1,0,17l-24,24a12,12,0,0,1-17-17L155,152l-15.52-15.51a12,12,0,1,1,17-17Zm-64-24a12,12,0,0,0-17,0l-24,24a12,12,0,0,0,0,17l24,24a12,12,0,0,0,17-17L101,152l15.52-15.51A12,12,0,0,0,116.49,119.51ZM220,88V216a20,20,0,0,1-20,20H56a20,20,0,0,1-20-20V40A20,20,0,0,1,56,20h96a12,12,0,0,1,8.49,3.52l56,56A12,12,0,0,1,220,88ZM160,57V80h23Zm36,155V104H148a12,12,0,0,1-12-12V44H60V212Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M208,88H152V32Z",opacity:"0.2"}),g.createElement("path",{d:"M181.66,146.34a8,8,0,0,1,0,11.32l-24,24a8,8,0,0,1-11.32-11.32L164.69,152l-18.35-18.34a8,8,0,0,1,11.32-11.32Zm-72-24a8,8,0,0,0-11.32,0l-24,24a8,8,0,0,0,0,11.32l24,24a8,8,0,0,0,11.32-11.32L91.31,152l18.35-18.34A8,8,0,0,0,109.66,122.34ZM216,88V216a16,16,0,0,1-16,16H56a16,16,0,0,1-16-16V40A16,16,0,0,1,56,24h96a8,8,0,0,1,5.66,2.34l56,56A8,8,0,0,1,216,88Zm-56-8h28.69L160,51.31Zm40,136V96H152a8,8,0,0,1-8-8V40H56V216H200Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34Zm-104,88a8,8,0,0,1-11.32,11.32l-24-24a8,8,0,0,1,0-11.32l24-24a8,8,0,0,1,11.32,11.32L91.31,152Zm72-12.68-24,24a8,8,0,0,1-11.32-11.32L164.69,152l-18.35-18.34a8,8,0,0,1,11.32-11.32l24,24A8,8,0,0,1,181.66,157.66ZM152,88V44l44,44Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M180.24,147.76a6,6,0,0,1,0,8.48l-24,24a6,6,0,0,1-8.48-8.48L167.51,152l-19.75-19.76a6,6,0,1,1,8.48-8.48Zm-72-24a6,6,0,0,0-8.48,0l-24,24a6,6,0,0,0,0,8.48l24,24a6,6,0,1,0,8.48-8.48L88.49,152l19.75-19.76A6,6,0,0,0,108.24,123.76ZM214,88V216a14,14,0,0,1-14,14H56a14,14,0,0,1-14-14V40A14,14,0,0,1,56,26h96a6,6,0,0,1,4.25,1.76l56,56A6,6,0,0,1,214,88Zm-56-6h35.52L158,46.48Zm44,134V94H152a6,6,0,0,1-6-6V38H56a2,2,0,0,0-2,2V216a2,2,0,0,0,2,2H200A2,2,0,0,0,202,216Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M181.66,146.34a8,8,0,0,1,0,11.32l-24,24a8,8,0,0,1-11.32-11.32L164.69,152l-18.35-18.34a8,8,0,0,1,11.32-11.32Zm-72-24a8,8,0,0,0-11.32,0l-24,24a8,8,0,0,0,0,11.32l24,24a8,8,0,0,0,11.32-11.32L91.31,152l18.35-18.34A8,8,0,0,0,109.66,122.34ZM216,88V216a16,16,0,0,1-16,16H56a16,16,0,0,1-16-16V40A16,16,0,0,1,56,24h96a8,8,0,0,1,5.66,2.34l56,56A8,8,0,0,1,216,88Zm-56-8h28.69L160,51.31Zm40,136V96H152a8,8,0,0,1-8-8V40H56V216H200Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M178.83,149.17a4,4,0,0,1,0,5.66l-24,24a4,4,0,0,1-5.66-5.66L170.34,152l-21.17-21.17a4,4,0,1,1,5.66-5.66Zm-72-24a4,4,0,0,0-5.66,0l-24,24a4,4,0,0,0,0,5.66l24,24a4,4,0,1,0,5.66-5.66L85.66,152l21.17-21.17A4,4,0,0,0,106.83,125.17ZM212,88V216a12,12,0,0,1-12,12H56a12,12,0,0,1-12-12V40A12,12,0,0,1,56,28h96a4,4,0,0,1,2.83,1.17l56,56A4,4,0,0,1,212,88Zm-56-4h42.34L156,41.65Zm48,132V92H152a4,4,0,0,1-4-4V36H56a4,4,0,0,0-4,4V216a4,4,0,0,0,4,4H200A4,4,0,0,0,204,216Z"}))]]),rg=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:zx}));rg.displayName="FileCodeIcon";const Zx=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M228,128a12,12,0,0,1-12,12H128a12,12,0,0,1,0-24h88A12,12,0,0,1,228,128ZM128,76h88a12,12,0,0,0,0-24H128a12,12,0,0,0,0,24Zm88,104H128a12,12,0,0,0,0,24h88a12,12,0,0,0,0-24ZM79.51,39.51,56,63l-7.51-7.52a12,12,0,0,0-17,17l16,16a12,12,0,0,0,17,0l32-32a12,12,0,0,0-17-17Zm0,64L56,127l-7.51-7.52a12,12,0,1,0-17,17l16,16a12,12,0,0,0,17,0l32-32a12,12,0,0,0-17-17Zm0,64L56,191l-7.51-7.52a12,12,0,1,0-17,17l16,16a12,12,0,0,0,17,0l32-32a12,12,0,0,0-17-17Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M216,64V192H128V64Z",opacity:"0.2"}),g.createElement("path",{d:"M224,128a8,8,0,0,1-8,8H128a8,8,0,0,1,0-16h88A8,8,0,0,1,224,128ZM128,72h88a8,8,0,0,0,0-16H128a8,8,0,0,0,0,16Zm88,112H128a8,8,0,0,0,0,16h88a8,8,0,0,0,0-16ZM82.34,42.34,56,68.69,45.66,58.34A8,8,0,0,0,34.34,69.66l16,16a8,8,0,0,0,11.32,0l32-32A8,8,0,0,0,82.34,42.34Zm0,64L56,132.69,45.66,122.34a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32,0l32-32a8,8,0,0,0-11.32-11.32Zm0,64L56,196.69,45.66,186.34a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32,0l32-32a8,8,0,0,0-11.32-11.32Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M208,32H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM117.66,149.66l-32,32a8,8,0,0,1-11.32,0l-16-16a8,8,0,0,1,11.32-11.32L80,164.69l26.34-26.35a8,8,0,0,1,11.32,11.32Zm0-64-32,32a8,8,0,0,1-11.32,0l-16-16A8,8,0,0,1,69.66,90.34L80,100.69l26.34-26.35a8,8,0,0,1,11.32,11.32ZM192,168H144a8,8,0,0,1,0-16h48a8,8,0,0,1,0,16Zm0-64H144a8,8,0,0,1,0-16h48a8,8,0,0,1,0,16Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M222,128a6,6,0,0,1-6,6H128a6,6,0,0,1,0-12h88A6,6,0,0,1,222,128ZM128,70h88a6,6,0,0,0,0-12H128a6,6,0,0,0,0,12Zm88,116H128a6,6,0,0,0,0,12h88a6,6,0,0,0,0-12ZM83.76,43.76,56,71.51,44.24,59.76a6,6,0,0,0-8.48,8.48l16,16a6,6,0,0,0,8.48,0l32-32a6,6,0,0,0-8.48-8.48Zm0,64L56,135.51,44.24,123.76a6,6,0,1,0-8.48,8.48l16,16a6,6,0,0,0,8.48,0l32-32a6,6,0,0,0-8.48-8.48Zm0,64L56,199.51,44.24,187.76a6,6,0,0,0-8.48,8.48l16,16a6,6,0,0,0,8.48,0l32-32a6,6,0,0,0-8.48-8.48Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M224,128a8,8,0,0,1-8,8H128a8,8,0,0,1,0-16h88A8,8,0,0,1,224,128ZM128,72h88a8,8,0,0,0,0-16H128a8,8,0,0,0,0,16Zm88,112H128a8,8,0,0,0,0,16h88a8,8,0,0,0,0-16ZM82.34,42.34,56,68.69,45.66,58.34A8,8,0,0,0,34.34,69.66l16,16a8,8,0,0,0,11.32,0l32-32A8,8,0,0,0,82.34,42.34Zm0,64L56,132.69,45.66,122.34a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32,0l32-32a8,8,0,0,0-11.32-11.32Zm0,64L56,196.69,45.66,186.34a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32,0l32-32a8,8,0,0,0-11.32-11.32Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M220,128a4,4,0,0,1-4,4H128a4,4,0,0,1,0-8h88A4,4,0,0,1,220,128ZM128,68h88a4,4,0,0,0,0-8H128a4,4,0,0,0,0,8Zm88,120H128a4,4,0,0,0,0,8h88a4,4,0,0,0,0-8ZM85.17,45.17,56,74.34,42.83,61.17a4,4,0,0,0-5.66,5.66l16,16a4,4,0,0,0,5.66,0l32-32a4,4,0,0,0-5.66-5.66Zm0,64L56,138.34,42.83,125.17a4,4,0,1,0-5.66,5.66l16,16a4,4,0,0,0,5.66,0l32-32a4,4,0,0,0-5.66-5.66Zm0,64L56,202.34,42.83,189.17a4,4,0,0,0-5.66,5.66l16,16a4,4,0,0,0,5.66,0l32-32a4,4,0,0,0-5.66-5.66Z"}))]]),ng=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:Zx}));ng.displayName="ListChecksIcon";const Wx=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M240.26,186.1,152.81,34.23h0a28.74,28.74,0,0,0-49.62,0L15.74,186.1a27.45,27.45,0,0,0,0,27.71A28.31,28.31,0,0,0,40.55,228h174.9a28.31,28.31,0,0,0,24.79-14.19A27.45,27.45,0,0,0,240.26,186.1Zm-20.8,15.7a4.46,4.46,0,0,1-4,2.2H40.55a4.46,4.46,0,0,1-4-2.2,3.56,3.56,0,0,1,0-3.73L124,46.2a4.77,4.77,0,0,1,8,0l87.44,151.87A3.56,3.56,0,0,1,219.46,201.8ZM116,136V104a12,12,0,0,1,24,0v32a12,12,0,0,1-24,0Zm28,40a16,16,0,1,1-16-16A16,16,0,0,1,144,176Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M215.46,216H40.54C27.92,216,20,202.79,26.13,192.09L113.59,40.22c6.3-11,22.52-11,28.82,0l87.46,151.87C236,202.79,228.08,216,215.46,216Z",opacity:"0.2"}),g.createElement("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8ZM120,144V104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,180Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM120,104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm8,88a12,12,0,1,1,12-12A12,12,0,0,1,128,192Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M235.07,189.09,147.61,37.22h0a22.75,22.75,0,0,0-39.22,0L20.93,189.09a21.53,21.53,0,0,0,0,21.72A22.35,22.35,0,0,0,40.55,222h174.9a22.35,22.35,0,0,0,19.6-11.19A21.53,21.53,0,0,0,235.07,189.09ZM224.66,204.8a10.46,10.46,0,0,1-9.21,5.2H40.55a10.46,10.46,0,0,1-9.21-5.2,9.51,9.51,0,0,1,0-9.72L118.79,43.21a10.75,10.75,0,0,1,18.42,0l87.46,151.87A9.51,9.51,0,0,1,224.66,204.8ZM122,144V104a6,6,0,0,1,12,0v40a6,6,0,0,1-12,0Zm16,36a10,10,0,1,1-10-10A10,10,0,0,1,138,180Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8ZM120,144V104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,180Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M233.34,190.09,145.88,38.22h0a20.75,20.75,0,0,0-35.76,0L22.66,190.09a19.52,19.52,0,0,0,0,19.71A20.36,20.36,0,0,0,40.54,220H215.46a20.36,20.36,0,0,0,17.86-10.2A19.52,19.52,0,0,0,233.34,190.09ZM226.4,205.8a12.47,12.47,0,0,1-10.94,6.2H40.54a12.47,12.47,0,0,1-10.94-6.2,11.45,11.45,0,0,1,0-11.72L117.05,42.21a12.76,12.76,0,0,1,21.9,0L226.4,194.08A11.45,11.45,0,0,1,226.4,205.8ZM124,144V104a4,4,0,0,1,8,0v40a4,4,0,0,1-8,0Zm12,36a8,8,0,1,1-8-8A8,8,0,0,1,136,180Z"}))]]),ag=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:Wx}));ag.displayName="WarningIcon";const Ux=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M172,108a12,12,0,0,1-12,12H96a12,12,0,0,1,0-24h64A12,12,0,0,1,172,108Zm-12,28H96a12,12,0,0,0,0,24h64a12,12,0,0,0,0-24Zm76-8A108,108,0,0,1,78.77,224.15L46.34,235A20,20,0,0,1,21,209.66l10.81-32.43A108,108,0,1,1,236,128Zm-24,0A84,84,0,1,0,55.27,170.06a12,12,0,0,1,1,9.81l-9.93,29.79,29.79-9.93a12.1,12.1,0,0,1,3.8-.62,12,12,0,0,1,6,1.62A84,84,0,0,0,212,128Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M224,128A96,96,0,0,1,79.93,211.11h0L42.54,223.58a8,8,0,0,1-10.12-10.12l12.47-37.39h0A96,96,0,1,1,224,128Z",opacity:"0.2"}),g.createElement("path",{d:"M128,24A104,104,0,0,0,36.18,176.88L24.83,210.93a16,16,0,0,0,20.24,20.24l34.05-11.35A104,104,0,1,0,128,24Zm0,192a87.87,87.87,0,0,1-44.06-11.81,8,8,0,0,0-4-1.08,7.85,7.85,0,0,0-2.53.42L40,216,52.47,178.6a8,8,0,0,0-.66-6.54A88,88,0,1,1,128,216Zm40-104a8,8,0,0,1-8,8H96a8,8,0,0,1,0-16h64A8,8,0,0,1,168,112Zm0,32a8,8,0,0,1-8,8H96a8,8,0,0,1,0-16h64A8,8,0,0,1,168,144Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M128,24A104,104,0,0,0,36.18,176.88L24.83,210.93a16,16,0,0,0,20.24,20.24l34.05-11.35A104,104,0,1,0,128,24Zm32,128H96a8,8,0,0,1,0-16h64a8,8,0,0,1,0,16Zm0-32H96a8,8,0,0,1,0-16h64a8,8,0,0,1,0,16Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M166,112a6,6,0,0,1-6,6H96a6,6,0,0,1,0-12h64A6,6,0,0,1,166,112Zm-6,26H96a6,6,0,0,0,0,12h64a6,6,0,0,0,0-12Zm70-10A102,102,0,0,1,79.31,217.65L44.44,229.27a14,14,0,0,1-17.71-17.71l11.62-34.87A102,102,0,1,1,230,128Zm-12,0A90,90,0,1,0,50.08,173.06a6,6,0,0,1,.5,4.91L38.12,215.35a2,2,0,0,0,2.53,2.53L78,205.42a6.2,6.2,0,0,1,1.9-.31,6.09,6.09,0,0,1,3,.81A90,90,0,0,0,218,128Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M168,112a8,8,0,0,1-8,8H96a8,8,0,0,1,0-16h64A8,8,0,0,1,168,112Zm-8,24H96a8,8,0,0,0,0,16h64a8,8,0,0,0,0-16Zm72-8A104,104,0,0,1,79.12,219.82L45.07,231.17a16,16,0,0,1-20.24-20.24l11.35-34.05A104,104,0,1,1,232,128Zm-16,0A88,88,0,1,0,51.81,172.06a8,8,0,0,1,.66,6.54L40,216,77.4,203.53a7.85,7.85,0,0,1,2.53-.42,8,8,0,0,1,4,1.08A88,88,0,0,0,216,128Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M164,112a4,4,0,0,1-4,4H96a4,4,0,0,1,0-8h64A4,4,0,0,1,164,112Zm-4,28H96a4,4,0,0,0,0,8h64a4,4,0,0,0,0-8Zm68-12A100,100,0,0,1,79.5,215.47l-35.69,11.9a12,12,0,0,1-15.18-15.18l11.9-35.69A100,100,0,1,1,228,128Zm-8,0A92,92,0,1,0,48.35,174.07a4,4,0,0,1,.33,3.27L36.22,214.72a4,4,0,0,0,5.06,5.06l37.38-12.46a3.93,3.93,0,0,1,1.27-.21,4.05,4.05,0,0,1,2,.54A92,92,0,0,0,220,128Z"}))]]),ig=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:Ux}));ig.displayName="ChatCircleTextIcon";const Gx=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M144,128a16,16,0,1,1-16-16A16,16,0,0,1,144,128ZM60,112a16,16,0,1,0,16,16A16,16,0,0,0,60,112Zm136,0a16,16,0,1,0,16,16A16,16,0,0,0,196,112Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M240,96v64a16,16,0,0,1-16,16H32a16,16,0,0,1-16-16V96A16,16,0,0,1,32,80H224A16,16,0,0,1,240,96Z",opacity:"0.2"}),g.createElement("path",{d:"M140,128a12,12,0,1,1-12-12A12,12,0,0,1,140,128Zm56-12a12,12,0,1,0,12,12A12,12,0,0,0,196,116ZM60,116a12,12,0,1,0,12,12A12,12,0,0,0,60,116Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M224,80H32A16,16,0,0,0,16,96v64a16,16,0,0,0,16,16H224a16,16,0,0,0,16-16V96A16,16,0,0,0,224,80ZM60,140a12,12,0,1,1,12-12A12,12,0,0,1,60,140Zm68,0a12,12,0,1,1,12-12A12,12,0,0,1,128,140Zm68,0a12,12,0,1,1,12-12A12,12,0,0,1,196,140Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M138,128a10,10,0,1,1-10-10A10,10,0,0,1,138,128ZM60,118a10,10,0,1,0,10,10A10,10,0,0,0,60,118Zm136,0a10,10,0,1,0,10,10A10,10,0,0,0,196,118Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M140,128a12,12,0,1,1-12-12A12,12,0,0,1,140,128Zm56-12a12,12,0,1,0,12,12A12,12,0,0,0,196,116ZM60,116a12,12,0,1,0,12,12A12,12,0,0,0,60,116Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M136,128a8,8,0,1,1-8-8A8,8,0,0,1,136,128Zm-76-8a8,8,0,1,0,8,8A8,8,0,0,0,60,120Zm136,0a8,8,0,1,0,8,8A8,8,0,0,0,196,120Z"}))]]),ya=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:Gx}));ya.displayName="DotsThreeIcon";function Kx({messages:e}){return T.jsxs(hr,{children:[T.jsx(da,{action:T.jsx(pa,{children:T.jsx(ya,{weight:"bold"})}),avatar:T.jsx(Nt,{children:T.jsx(ig,{fontSize:"var(--Icon-fontSize)"})}),title:"App chat"}),T.jsx(Wf,{disablePadding:!0,sx:{p:1,"& .MuiListItemButton-root":{borderRadius:1},"& .MuiBadge-dot":{border:"2px solid var(--mui-palette-background-paper)",borderRadius:"50%",bottom:"5px",height:"12px",right:"5px",width:"12px"}},children:e.map(t=>T.jsx(Uf,{disablePadding:!0,children:T.jsxs(Ix,{children:[T.jsx(Gf,{children:t.author.status==="online"?T.jsx(kx,{anchorOrigin:{horizontal:"right",vertical:"bottom"},color:"success",variant:"dot",children:T.jsx(Nt,{src:t.author.avatar})}):T.jsx(Nt,{src:t.author.avatar})}),T.jsx(Kf,{disableTypography:!0,primary:T.jsx(ie,{noWrap:!0,variant:"subtitle2",children:t.author.name}),secondary:T.jsx(ie,{color:"text.secondary",noWrap:!0,variant:"body2",children:t.content})}),T.jsx(ie,{color:"text.secondary",sx:{whiteSpace:"nowrap"},variant:"caption",children:Xe(t.createdAt).fromNow()})]})},t.id))}),T.jsx(dr,{}),T.jsx(va,{children:T.jsx(Rt,{color:"secondary",endIcon:T.jsx(ar,{}),size:"small",children:"Go to chat"})})]})}const Xx=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M156,88H100a12,12,0,0,0-12,12v56a12,12,0,0,0,12,12h56a12,12,0,0,0,12-12V100A12,12,0,0,0,156,88Zm-12,56H112V112h32Zm88-4H220V116h12a12,12,0,0,0,0-24H220V56a20,20,0,0,0-20-20H164V24a12,12,0,0,0-24,0V36H116V24a12,12,0,0,0-24,0V36H56A20,20,0,0,0,36,56V92H24a12,12,0,0,0,0,24H36v24H24a12,12,0,0,0,0,24H36v36a20,20,0,0,0,20,20H92v12a12,12,0,0,0,24,0V220h24v12a12,12,0,0,0,24,0V220h36a20,20,0,0,0,20-20V164h12a12,12,0,0,0,0-24Zm-36,56H60V60H196Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M200,48H56a8,8,0,0,0-8,8V200a8,8,0,0,0,8,8H200a8,8,0,0,0,8-8V56A8,8,0,0,0,200,48ZM152,152H104V104h48Z",opacity:"0.2"}),g.createElement("path",{d:"M152,96H104a8,8,0,0,0-8,8v48a8,8,0,0,0,8,8h48a8,8,0,0,0,8-8V104A8,8,0,0,0,152,96Zm-8,48H112V112h32Zm88,0H216V112h16a8,8,0,0,0,0-16H216V56a16,16,0,0,0-16-16H160V24a8,8,0,0,0-16,0V40H112V24a8,8,0,0,0-16,0V40H56A16,16,0,0,0,40,56V96H24a8,8,0,0,0,0,16H40v32H24a8,8,0,0,0,0,16H40v40a16,16,0,0,0,16,16H96v16a8,8,0,0,0,16,0V216h32v16a8,8,0,0,0,16,0V216h40a16,16,0,0,0,16-16V160h16a8,8,0,0,0,0-16Zm-32,56H56V56H200v95.87s0,.09,0,.13,0,.09,0,.13V200Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M104,104h48v48H104Zm136,48a8,8,0,0,1-8,8H216v40a16,16,0,0,1-16,16H160v16a8,8,0,0,1-16,0V216H112v16a8,8,0,0,1-16,0V216H56a16,16,0,0,1-16-16V160H24a8,8,0,0,1,0-16H40V112H24a8,8,0,0,1,0-16H40V56A16,16,0,0,1,56,40H96V24a8,8,0,0,1,16,0V40h32V24a8,8,0,0,1,16,0V40h40a16,16,0,0,1,16,16V96h16a8,8,0,0,1,0,16H216v32h16A8,8,0,0,1,240,152ZM168,96a8,8,0,0,0-8-8H96a8,8,0,0,0-8,8v64a8,8,0,0,0,8,8h64a8,8,0,0,0,8-8Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M152,98H104a6,6,0,0,0-6,6v48a6,6,0,0,0,6,6h48a6,6,0,0,0,6-6V104A6,6,0,0,0,152,98Zm-6,48H110V110h36Zm86,0H214V110h18a6,6,0,0,0,0-12H214V56a14,14,0,0,0-14-14H158V24a6,6,0,0,0-12,0V42H110V24a6,6,0,0,0-12,0V42H56A14,14,0,0,0,42,56V98H24a6,6,0,0,0,0,12H42v36H24a6,6,0,0,0,0,12H42v42a14,14,0,0,0,14,14H98v18a6,6,0,0,0,12,0V214h36v18a6,6,0,0,0,12,0V214h42a14,14,0,0,0,14-14V158h18a6,6,0,0,0,0-12Zm-30,54a2,2,0,0,1-2,2H56a2,2,0,0,1-2-2V56a2,2,0,0,1,2-2H200a2,2,0,0,1,2,2Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M152,96H104a8,8,0,0,0-8,8v48a8,8,0,0,0,8,8h48a8,8,0,0,0,8-8V104A8,8,0,0,0,152,96Zm-8,48H112V112h32Zm88,0H216V112h16a8,8,0,0,0,0-16H216V56a16,16,0,0,0-16-16H160V24a8,8,0,0,0-16,0V40H112V24a8,8,0,0,0-16,0V40H56A16,16,0,0,0,40,56V96H24a8,8,0,0,0,0,16H40v32H24a8,8,0,0,0,0,16H40v40a16,16,0,0,0,16,16H96v16a8,8,0,0,0,16,0V216h32v16a8,8,0,0,0,16,0V216h40a16,16,0,0,0,16-16V160h16a8,8,0,0,0,0-16Zm-32,56H56V56H200v95.87s0,.09,0,.13,0,.09,0,.13V200Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M152,100H104a4,4,0,0,0-4,4v48a4,4,0,0,0,4,4h48a4,4,0,0,0,4-4V104A4,4,0,0,0,152,100Zm-4,48H108V108h40Zm84,0H212V108h20a4,4,0,0,0,0-8H212V56a12,12,0,0,0-12-12H156V24a4,4,0,0,0-8,0V44H108V24a4,4,0,0,0-8,0V44H56A12,12,0,0,0,44,56v44H24a4,4,0,0,0,0,8H44v40H24a4,4,0,0,0,0,8H44v44a12,12,0,0,0,12,12h44v20a4,4,0,0,0,8,0V212h40v20a4,4,0,0,0,8,0V212h44a12,12,0,0,0,12-12V156h20a4,4,0,0,0,0-8Zm-28,52a4,4,0,0,1-4,4H56a4,4,0,0,1-4-4V56a4,4,0,0,1,4-4H200a4,4,0,0,1,4,4Z"}))]]),og=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:Xx}));og.displayName="CpuIcon";const Yx=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M219.71,117.38a12,12,0,0,0-7.25-8.52L161.28,88.39l10.59-70.61a12,12,0,0,0-20.64-10l-112,120a12,12,0,0,0,4.31,19.33l51.18,20.47L84.13,238.22a12,12,0,0,0,20.64,10l112-120A12,12,0,0,0,219.71,117.38ZM113.6,203.55l6.27-41.77a12,12,0,0,0-7.41-12.92L68.74,131.37,142.4,52.45l-6.27,41.77a12,12,0,0,0,7.41,12.92l43.72,17.49Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M96,240l16-80L48,136,160,16,144,96l64,24Z",opacity:"0.2"}),g.createElement("path",{d:"M215.79,118.17a8,8,0,0,0-5-5.66L153.18,90.9l14.66-73.33a8,8,0,0,0-13.69-7l-112,120a8,8,0,0,0,3,13l57.63,21.61L88.16,238.43a8,8,0,0,0,13.69,7l112-120A8,8,0,0,0,215.79,118.17ZM109.37,214l10.47-52.38a8,8,0,0,0-5-9.06L62,132.71l84.62-90.66L136.16,94.43a8,8,0,0,0,5,9.06l52.8,19.8Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M213.85,125.46l-112,120a8,8,0,0,1-13.69-7l14.66-73.33L45.19,143.49a8,8,0,0,1-3-13l112-120a8,8,0,0,1,13.69,7L153.18,90.9l57.63,21.61a8,8,0,0,1,3,12.95Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M213.84,118.63a6,6,0,0,0-3.73-4.25L150.88,92.17l15-75a6,6,0,0,0-10.27-5.27l-112,120a6,6,0,0,0,2.28,9.71l59.23,22.21-15,75a6,6,0,0,0,3.14,6.52A6.07,6.07,0,0,0,96,246a6,6,0,0,0,4.39-1.91l112-120A6,6,0,0,0,213.84,118.63ZM106,220.46l11.85-59.28a6,6,0,0,0-3.77-6.8l-55.6-20.85,91.46-98L138.12,94.82a6,6,0,0,0,3.77,6.8l55.6,20.85Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M215.79,118.17a8,8,0,0,0-5-5.66L153.18,90.9l14.66-73.33a8,8,0,0,0-13.69-7l-112,120a8,8,0,0,0,3,13l57.63,21.61L88.16,238.43a8,8,0,0,0,13.69,7l112-120A8,8,0,0,0,215.79,118.17ZM109.37,214l10.47-52.38a8,8,0,0,0-5-9.06L62,132.71l84.62-90.66L136.16,94.43a8,8,0,0,0,5,9.06l52.8,19.8Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M211.89,119.09a4,4,0,0,0-2.49-2.84l-60.81-22.8,15.33-76.67a4,4,0,0,0-6.84-3.51l-112,120a4,4,0,0,0-1,3.64,4,4,0,0,0,2.49,2.84l60.81,22.8L92.08,239.22a4,4,0,0,0,6.84,3.51l112-120A4,4,0,0,0,211.89,119.09ZM102.68,227l13.24-66.2a4,4,0,0,0-2.52-4.53L55,134.36,153.32,29l-13.24,66.2a4,4,0,0,0,2.52,4.53L201,121.64Z"}))]]),ug=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:Yx}));ug.displayName="LightningIcon";var _o,i0;function qe(){if(i0)return _o;i0=1;var e=Array.isArray;return _o=e,_o}var So,o0;function cg(){if(o0)return So;o0=1;var e=typeof Pa=="object"&&Pa&&Pa.Object===Object&&Pa;return So=e,So}var Po,u0;function pt(){if(u0)return Po;u0=1;var e=cg(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return Po=r,Po}var Eo,c0;function ma(){if(c0)return Eo;c0=1;var e=pt(),t=e.Symbol;return Eo=t,Eo}var jo,s0;function Jx(){if(s0)return jo;s0=1;var e=ma(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,a=e?e.toStringTag:void 0;function i(o){var u=r.call(o,a),c=o[a];try{o[a]=void 0;var s=!0}catch{}var f=n.call(o);return s&&(u?o[a]=c:delete o[a]),f}return jo=i,jo}var To,l0;function Qx(){if(l0)return To;l0=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return To=r,To}var Mo,f0;function Tt(){if(f0)return Mo;f0=1;var e=ma(),t=Jx(),r=Qx(),n="[object Null]",a="[object Undefined]",i=e?e.toStringTag:void 0;function o(u){return u==null?u===void 0?a:n:i&&i in Object(u)?t(u):r(u)}return Mo=o,Mo}var $o,h0;function Mt(){if(h0)return $o;h0=1;function e(t){return t!=null&&typeof t=="object"}return $o=e,$o}var Co,d0;function nn(){if(d0)return Co;d0=1;var e=Tt(),t=Mt(),r="[object Symbol]";function n(a){return typeof a=="symbol"||t(a)&&e(a)==r}return Co=n,Co}var Io,p0;function Xf(){if(p0)return Io;p0=1;var e=qe(),t=nn(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function a(i,o){if(e(i))return!1;var u=typeof i;return u=="number"||u=="symbol"||u=="boolean"||i==null||t(i)?!0:n.test(i)||!r.test(i)||o!=null&&i in Object(o)}return Io=a,Io}var ko,v0;function Ht(){if(v0)return ko;v0=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return ko=e,ko}var Ro,y0;function Yf(){if(y0)return Ro;y0=1;var e=Tt(),t=Ht(),r="[object AsyncFunction]",n="[object Function]",a="[object GeneratorFunction]",i="[object Proxy]";function o(u){if(!t(u))return!1;var c=e(u);return c==n||c==a||c==r||c==i}return Ro=o,Ro}var Do,m0;function ew(){if(m0)return Do;m0=1;var e=pt(),t=e["__core-js_shared__"];return Do=t,Do}var No,g0;function tw(){if(g0)return No;g0=1;var e=ew(),t=(function(){var n=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""})();function r(n){return!!t&&t in n}return No=r,No}var Lo,b0;function sg(){if(b0)return Lo;b0=1;var e=Function.prototype,t=e.toString;function r(n){if(n!=null){try{return t.call(n)}catch{}try{return n+""}catch{}}return""}return Lo=r,Lo}var qo,x0;function rw(){if(x0)return qo;x0=1;var e=Yf(),t=tw(),r=Ht(),n=sg(),a=/[\\^$.*+?()[\]{}|]/g,i=/^\[object .+?Constructor\]$/,o=Function.prototype,u=Object.prototype,c=o.toString,s=u.hasOwnProperty,f=RegExp("^"+c.call(s).replace(a,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function l(h){if(!r(h)||t(h))return!1;var d=e(h)?f:i;return d.test(n(h))}return qo=l,qo}var Bo,w0;function nw(){if(w0)return Bo;w0=1;function e(t,r){return t==null?void 0:t[r]}return Bo=e,Bo}var Ho,O0;function pr(){if(O0)return Ho;O0=1;var e=rw(),t=nw();function r(n,a){var i=t(n,a);return e(i)?i:void 0}return Ho=r,Ho}var Fo,A0;function Ni(){if(A0)return Fo;A0=1;var e=pr(),t=e(Object,"create");return Fo=t,Fo}var Vo,_0;function aw(){if(_0)return Vo;_0=1;var e=Ni();function t(){this.__data__=e?e(null):{},this.size=0}return Vo=t,Vo}var zo,S0;function iw(){if(S0)return zo;S0=1;function e(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}return zo=e,zo}var Zo,P0;function ow(){if(P0)return Zo;P0=1;var e=Ni(),t="__lodash_hash_undefined__",r=Object.prototype,n=r.hasOwnProperty;function a(i){var o=this.__data__;if(e){var u=o[i];return u===t?void 0:u}return n.call(o,i)?o[i]:void 0}return Zo=a,Zo}var Wo,E0;function uw(){if(E0)return Wo;E0=1;var e=Ni(),t=Object.prototype,r=t.hasOwnProperty;function n(a){var i=this.__data__;return e?i[a]!==void 0:r.call(i,a)}return Wo=n,Wo}var Uo,j0;function cw(){if(j0)return Uo;j0=1;var e=Ni(),t="__lodash_hash_undefined__";function r(n,a){var i=this.__data__;return this.size+=this.has(n)?0:1,i[n]=e&&a===void 0?t:a,this}return Uo=r,Uo}var Go,T0;function sw(){if(T0)return Go;T0=1;var e=aw(),t=iw(),r=ow(),n=uw(),a=cw();function i(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return i.prototype.clear=e,i.prototype.delete=t,i.prototype.get=r,i.prototype.has=n,i.prototype.set=a,Go=i,Go}var Ko,M0;function lw(){if(M0)return Ko;M0=1;function e(){this.__data__=[],this.size=0}return Ko=e,Ko}var Xo,$0;function Jf(){if($0)return Xo;$0=1;function e(t,r){return t===r||t!==t&&r!==r}return Xo=e,Xo}var Yo,C0;function Li(){if(C0)return Yo;C0=1;var e=Jf();function t(r,n){for(var a=r.length;a--;)if(e(r[a][0],n))return a;return-1}return Yo=t,Yo}var Jo,I0;function fw(){if(I0)return Jo;I0=1;var e=Li(),t=Array.prototype,r=t.splice;function n(a){var i=this.__data__,o=e(i,a);if(o<0)return!1;var u=i.length-1;return o==u?i.pop():r.call(i,o,1),--this.size,!0}return Jo=n,Jo}var Qo,k0;function hw(){if(k0)return Qo;k0=1;var e=Li();function t(r){var n=this.__data__,a=e(n,r);return a<0?void 0:n[a][1]}return Qo=t,Qo}var eu,R0;function dw(){if(R0)return eu;R0=1;var e=Li();function t(r){return e(this.__data__,r)>-1}return eu=t,eu}var tu,D0;function pw(){if(D0)return tu;D0=1;var e=Li();function t(r,n){var a=this.__data__,i=e(a,r);return i<0?(++this.size,a.push([r,n])):a[i][1]=n,this}return tu=t,tu}var ru,N0;function qi(){if(N0)return ru;N0=1;var e=lw(),t=fw(),r=hw(),n=dw(),a=pw();function i(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return i.prototype.clear=e,i.prototype.delete=t,i.prototype.get=r,i.prototype.has=n,i.prototype.set=a,ru=i,ru}var nu,L0;function Qf(){if(L0)return nu;L0=1;var e=pr(),t=pt(),r=e(t,"Map");return nu=r,nu}var au,q0;function vw(){if(q0)return au;q0=1;var e=sw(),t=qi(),r=Qf();function n(){this.size=0,this.__data__={hash:new e,map:new(r||t),string:new e}}return au=n,au}var iu,B0;function yw(){if(B0)return iu;B0=1;function e(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}return iu=e,iu}var ou,H0;function Bi(){if(H0)return ou;H0=1;var e=yw();function t(r,n){var a=r.__data__;return e(n)?a[typeof n=="string"?"string":"hash"]:a.map}return ou=t,ou}var uu,F0;function mw(){if(F0)return uu;F0=1;var e=Bi();function t(r){var n=e(this,r).delete(r);return this.size-=n?1:0,n}return uu=t,uu}var cu,V0;function gw(){if(V0)return cu;V0=1;var e=Bi();function t(r){return e(this,r).get(r)}return cu=t,cu}var su,z0;function bw(){if(z0)return su;z0=1;var e=Bi();function t(r){return e(this,r).has(r)}return su=t,su}var lu,Z0;function xw(){if(Z0)return lu;Z0=1;var e=Bi();function t(r,n){var a=e(this,r),i=a.size;return a.set(r,n),this.size+=a.size==i?0:1,this}return lu=t,lu}var fu,W0;function eh(){if(W0)return fu;W0=1;var e=vw(),t=mw(),r=gw(),n=bw(),a=xw();function i(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return i.prototype.clear=e,i.prototype.delete=t,i.prototype.get=r,i.prototype.has=n,i.prototype.set=a,fu=i,fu}var hu,U0;function lg(){if(U0)return hu;U0=1;var e=eh(),t="Expected a function";function r(n,a){if(typeof n!="function"||a!=null&&typeof a!="function")throw new TypeError(t);var i=function(){var o=arguments,u=a?a.apply(this,o):o[0],c=i.cache;if(c.has(u))return c.get(u);var s=n.apply(this,o);return i.cache=c.set(u,s)||c,s};return i.cache=new(r.Cache||e),i}return r.Cache=e,hu=r,hu}var du,G0;function ww(){if(G0)return du;G0=1;var e=lg(),t=500;function r(n){var a=e(n,function(o){return i.size===t&&i.clear(),o}),i=a.cache;return a}return du=r,du}var pu,K0;function Ow(){if(K0)return pu;K0=1;var e=ww(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=e(function(a){var i=[];return a.charCodeAt(0)===46&&i.push(""),a.replace(t,function(o,u,c,s){i.push(c?s.replace(r,"$1"):u||o)}),i});return pu=n,pu}var vu,X0;function th(){if(X0)return vu;X0=1;function e(t,r){for(var n=-1,a=t==null?0:t.length,i=Array(a);++n<a;)i[n]=r(t[n],n,t);return i}return vu=e,vu}var yu,Y0;function Aw(){if(Y0)return yu;Y0=1;var e=ma(),t=th(),r=qe(),n=nn(),a=e?e.prototype:void 0,i=a?a.toString:void 0;function o(u){if(typeof u=="string")return u;if(r(u))return t(u,o)+"";if(n(u))return i?i.call(u):"";var c=u+"";return c=="0"&&1/u==-1/0?"-0":c}return yu=o,yu}var mu,J0;function fg(){if(J0)return mu;J0=1;var e=Aw();function t(r){return r==null?"":e(r)}return mu=t,mu}var gu,Q0;function hg(){if(Q0)return gu;Q0=1;var e=qe(),t=Xf(),r=Ow(),n=fg();function a(i,o){return e(i)?i:t(i,o)?[i]:r(n(i))}return gu=a,gu}var bu,ed;function Hi(){if(ed)return bu;ed=1;var e=nn();function t(r){if(typeof r=="string"||e(r))return r;var n=r+"";return n=="0"&&1/r==-1/0?"-0":n}return bu=t,bu}var xu,td;function rh(){if(td)return xu;td=1;var e=hg(),t=Hi();function r(n,a){a=e(a,n);for(var i=0,o=a.length;n!=null&&i<o;)n=n[t(a[i++])];return i&&i==o?n:void 0}return xu=r,xu}var wu,rd;function dg(){if(rd)return wu;rd=1;var e=rh();function t(r,n,a){var i=r==null?void 0:e(r,n);return i===void 0?a:i}return wu=t,wu}var _w=dg();const Je=se(_w);var Ou,nd;function Sw(){if(nd)return Ou;nd=1;function e(t){return t==null}return Ou=e,Ou}var Pw=Sw();const re=se(Pw);var Au,ad;function Ew(){if(ad)return Au;ad=1;var e=Tt(),t=qe(),r=Mt(),n="[object String]";function a(i){return typeof i=="string"||!t(i)&&r(i)&&e(i)==n}return Au=a,Au}var jw=Ew();const cr=se(jw);var Tw=Yf();const X=se(Tw);var Mw=Ht();const an=se(Mw);var _u={exports:{}},ae={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var id;function $w(){if(id)return ae;id=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),o=Symbol.for("react.context"),u=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),s=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),l=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),d=Symbol.for("react.offscreen"),y;y=Symbol.for("react.module.reference");function v(p){if(typeof p=="object"&&p!==null){var x=p.$$typeof;switch(x){case e:switch(p=p.type,p){case r:case a:case n:case s:case f:return p;default:switch(p=p&&p.$$typeof,p){case u:case o:case c:case h:case l:case i:return p;default:return x}}case t:return x}}}return ae.ContextConsumer=o,ae.ContextProvider=i,ae.Element=e,ae.ForwardRef=c,ae.Fragment=r,ae.Lazy=h,ae.Memo=l,ae.Portal=t,ae.Profiler=a,ae.StrictMode=n,ae.Suspense=s,ae.SuspenseList=f,ae.isAsyncMode=function(){return!1},ae.isConcurrentMode=function(){return!1},ae.isContextConsumer=function(p){return v(p)===o},ae.isContextProvider=function(p){return v(p)===i},ae.isElement=function(p){return typeof p=="object"&&p!==null&&p.$$typeof===e},ae.isForwardRef=function(p){return v(p)===c},ae.isFragment=function(p){return v(p)===r},ae.isLazy=function(p){return v(p)===h},ae.isMemo=function(p){return v(p)===l},ae.isPortal=function(p){return v(p)===t},ae.isProfiler=function(p){return v(p)===a},ae.isStrictMode=function(p){return v(p)===n},ae.isSuspense=function(p){return v(p)===s},ae.isSuspenseList=function(p){return v(p)===f},ae.isValidElementType=function(p){return typeof p=="string"||typeof p=="function"||p===r||p===a||p===n||p===s||p===f||p===d||typeof p=="object"&&p!==null&&(p.$$typeof===h||p.$$typeof===l||p.$$typeof===i||p.$$typeof===o||p.$$typeof===c||p.$$typeof===y||p.getModuleId!==void 0)},ae.typeOf=v,ae}var od;function Cw(){return od||(od=1,_u.exports=$w()),_u.exports}var Iw=Cw(),Su,ud;function pg(){if(ud)return Su;ud=1;var e=Tt(),t=Mt(),r="[object Number]";function n(a){return typeof a=="number"||t(a)&&e(a)==r}return Su=n,Su}var Pu,cd;function kw(){if(cd)return Pu;cd=1;var e=pg();function t(r){return e(r)&&r!=+r}return Pu=t,Pu}var Rw=kw();const ga=se(Rw);var Dw=pg();const Nw=se(Dw);var Ne=function(t){return t===0?0:t>0?1:-1},er=function(t){return cr(t)&&t.indexOf("%")===t.length-1},B=function(t){return Nw(t)&&!ga(t)},Ae=function(t){return B(t)||cr(t)},Lw=0,Fi=function(t){var r=++Lw;return"".concat(t||"").concat(r)},it=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!B(t)&&!cr(t))return n;var i;if(er(t)){var o=t.indexOf("%");i=r*parseFloat(t.slice(0,o))/100}else i=+t;return ga(i)&&(i=n),a&&i>r&&(i=r),i},kt=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},qw=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},a=0;a<r;a++)if(!n[t[a]])n[t[a]]=!0;else return!0;return!1},gt=function(t,r){return B(t)&&B(r)?function(n){return t+n*(r-t)}:function(){return r}};function Sl(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Je(n,t))===r})}var Bw=function(t,r){return B(t)&&B(r)?t-r:cr(t)&&cr(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function Er(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Pl(e){"@babel/helpers - typeof";return Pl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pl(e)}var Hw=["viewBox","children"],Fw=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],sd=["points","pathLength"],Eu={svg:Hw,polygon:sd,polyline:sd},nh=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Ba=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(g.isValidElement(t)&&(n=t.props),!an(n))return null;var a={};return Object.keys(n).forEach(function(i){nh.includes(i)&&(a[i]=r||function(o){return n[i](n,o)})}),a},Vw=function(t,r,n){return function(a){return t(r,n,a),null}},Bt=function(t,r,n){if(!an(t)||Pl(t)!=="object")return null;var a=null;return Object.keys(t).forEach(function(i){var o=t[i];nh.includes(i)&&typeof o=="function"&&(a||(a={}),a[i]=Vw(o,r,n))}),a},zw=["children"],Zw=["children"];function ld(e,t){if(e==null)return{};var r=Ww(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Ww(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var fd={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},Ot=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},hd=null,ju=null,ah=function e(t){if(t===hd&&Array.isArray(ju))return ju;var r=[];return g.Children.forEach(t,function(n){re(n)||(Iw.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),ju=r,hd=t,r};function Qe(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(a){return Ot(a)}):n=[Ot(t)],ah(e).forEach(function(a){var i=Je(a,"type.displayName")||Je(a,"type.name");n.indexOf(i)!==-1&&r.push(a)}),r}function Fe(e,t){var r=Qe(e,t);return r&&r[0]}var dd=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,a=r.height;return!(!B(n)||n<=0||!B(a)||a<=0)},Uw=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],Gw=function(t){return t&&t.type&&cr(t.type)&&Uw.indexOf(t.type)>=0},Kw=function(t,r,n,a){var i,o=(i=Eu==null?void 0:Eu[a])!==null&&i!==void 0?i:[];return r.startsWith("data-")||!X(t)&&(a&&o.includes(r)||Fw.includes(r))||n&&nh.includes(r)},K=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var a=t;if(g.isValidElement(t)&&(a=t.props),!an(a))return null;var i={};return Object.keys(a).forEach(function(o){var u;Kw((u=a)===null||u===void 0?void 0:u[o],o,r,n)&&(i[o]=a[o])}),i},El=function e(t,r){if(t===r)return!0;var n=g.Children.count(t);if(n!==g.Children.count(r))return!1;if(n===0)return!0;if(n===1)return pd(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var a=0;a<n;a++){var i=t[a],o=r[a];if(Array.isArray(i)||Array.isArray(o)){if(!e(i,o))return!1}else if(!pd(i,o))return!1}return!0},pd=function(t,r){if(re(t)&&re(r))return!0;if(!re(t)&&!re(r)){var n=t.props||{},a=n.children,i=ld(n,zw),o=r.props||{},u=o.children,c=ld(o,Zw);return a&&u?Er(i,c)&&El(a,u):!a&&!u?Er(i,c):!1}return!1},vd=function(t,r){var n=[],a={};return ah(t).forEach(function(i,o){if(Gw(i))n.push(i);else if(i){var u=Ot(i.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!a[u])){var l=s(i,u,o);n.push(l),a[u]=!0}}}),n},Xw=function(t){var r=t&&t.type;return r&&fd[r]?fd[r]:null},Yw=function(t,r){return ah(r).indexOf(t)},Jw=["children","width","height","viewBox","className","style","title","desc"];function jl(){return jl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jl.apply(this,arguments)}function Qw(e,t){if(e==null)return{};var r=eO(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function eO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Tl(e){var t=e.children,r=e.width,n=e.height,a=e.viewBox,i=e.className,o=e.style,u=e.title,c=e.desc,s=Qw(e,Jw),f=a||{width:r,height:n,x:0,y:0},l=Q("recharts-surface",i);return P.createElement("svg",jl({},K(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),P.createElement("title",null,u),P.createElement("desc",null,c),t)}var tO=["children","className"];function Ml(){return Ml=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ml.apply(this,arguments)}function rO(e,t){if(e==null)return{};var r=nO(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function nO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var ue=P.forwardRef(function(e,t){var r=e.children,n=e.className,a=rO(e,tO),i=Q("recharts-layer",n);return P.createElement("g",Ml({className:i},K(a,!0),{ref:t}),r)}),At=function(t,r){for(var n=arguments.length,a=new Array(n>2?n-2:0),i=2;i<n;i++)a[i-2]=arguments[i]},Tu,yd;function aO(){if(yd)return Tu;yd=1;function e(t,r,n){var a=-1,i=t.length;r<0&&(r=-r>i?0:i+r),n=n>i?i:n,n<0&&(n+=i),i=r>n?0:n-r>>>0,r>>>=0;for(var o=Array(i);++a<i;)o[a]=t[a+r];return o}return Tu=e,Tu}var Mu,md;function iO(){if(md)return Mu;md=1;var e=aO();function t(r,n,a){var i=r.length;return a=a===void 0?i:a,!n&&a>=i?r:e(r,n,a)}return Mu=t,Mu}var $u,gd;function vg(){if(gd)return $u;gd=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",a=t+r+n,i="\\ufe0e\\ufe0f",o="\\u200d",u=RegExp("["+o+e+a+i+"]");function c(s){return u.test(s)}return $u=c,$u}var Cu,bd;function oO(){if(bd)return Cu;bd=1;function e(t){return t.split("")}return Cu=e,Cu}var Iu,xd;function uO(){if(xd)return Iu;xd=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",a=t+r+n,i="\\ufe0e\\ufe0f",o="["+e+"]",u="["+a+"]",c="\\ud83c[\\udffb-\\udfff]",s="(?:"+u+"|"+c+")",f="[^"+e+"]",l="(?:\\ud83c[\\udde6-\\uddff]){2}",h="[\\ud800-\\udbff][\\udc00-\\udfff]",d="\\u200d",y=s+"?",v="["+i+"]?",p="(?:"+d+"(?:"+[f,l,h].join("|")+")"+v+y+")*",x=v+y+p,O="(?:"+[f+u+"?",u,l,h,o].join("|")+")",w=RegExp(c+"(?="+c+")|"+O+x,"g");function A(m){return m.match(w)||[]}return Iu=A,Iu}var ku,wd;function cO(){if(wd)return ku;wd=1;var e=oO(),t=vg(),r=uO();function n(a){return t(a)?r(a):e(a)}return ku=n,ku}var Ru,Od;function sO(){if(Od)return Ru;Od=1;var e=iO(),t=vg(),r=cO(),n=fg();function a(i){return function(o){o=n(o);var u=t(o)?r(o):void 0,c=u?u[0]:o.charAt(0),s=u?e(u,1).join(""):o.slice(1);return c[i]()+s}}return Ru=a,Ru}var Du,Ad;function lO(){if(Ad)return Du;Ad=1;var e=sO(),t=e("toUpperCase");return Du=t,Du}var fO=lO();const Vi=se(fO);function le(e){return function(){return e}}const yg=Math.cos,Ha=Math.sin,ut=Math.sqrt,Fa=Math.PI,zi=2*Fa,$l=Math.PI,Cl=2*$l,Yt=1e-6,hO=Cl-Yt;function mg(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function dO(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return mg;const r=10**t;return function(n){this._+=n[0];for(let a=1,i=n.length;a<i;++a)this._+=Math.round(arguments[a]*r)/r+n[a]}}class pO{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?mg:dO(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,a){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+a}`}bezierCurveTo(t,r,n,a,i,o){this._append`C${+t},${+r},${+n},${+a},${this._x1=+i},${this._y1=+o}`}arcTo(t,r,n,a,i){if(t=+t,r=+r,n=+n,a=+a,i=+i,i<0)throw new Error(`negative radius: ${i}`);let o=this._x1,u=this._y1,c=n-t,s=a-r,f=o-t,l=u-r,h=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(h>Yt)if(!(Math.abs(l*c-s*f)>Yt)||!i)this._append`L${this._x1=t},${this._y1=r}`;else{let d=n-o,y=a-u,v=c*c+s*s,p=d*d+y*y,x=Math.sqrt(v),O=Math.sqrt(h),w=i*Math.tan(($l-Math.acos((v+h-p)/(2*x*O)))/2),A=w/O,m=w/x;Math.abs(A-1)>Yt&&this._append`L${t+A*f},${r+A*l}`,this._append`A${i},${i},0,0,${+(l*d>f*y)},${this._x1=t+m*c},${this._y1=r+m*s}`}}arc(t,r,n,a,i,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(a),c=n*Math.sin(a),s=t+u,f=r+c,l=1^o,h=o?a-i:i-a;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>Yt||Math.abs(this._y1-f)>Yt)&&this._append`L${s},${f}`,n&&(h<0&&(h=h%Cl+Cl),h>hO?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:h>Yt&&this._append`A${n},${n},0,${+(h>=$l)},${l},${this._x1=t+n*Math.cos(i)},${this._y1=r+n*Math.sin(i)}`)}rect(t,r,n,a){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+a}h${-n}Z`}toString(){return this._}}function ih(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new pO(t)}function oh(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function gg(e){this._context=e}gg.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function Zi(e){return new gg(e)}function bg(e){return e[0]}function xg(e){return e[1]}function wg(e,t){var r=le(!0),n=null,a=Zi,i=null,o=ih(u);e=typeof e=="function"?e:e===void 0?bg:le(e),t=typeof t=="function"?t:t===void 0?xg:le(t);function u(c){var s,f=(c=oh(c)).length,l,h=!1,d;for(n==null&&(i=a(d=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===h&&((h=!h)?i.lineStart():i.lineEnd()),h&&i.point(+e(l,s,c),+t(l,s,c));if(d)return i=null,d+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:le(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:le(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:le(!!c),u):r},u.curve=function(c){return arguments.length?(a=c,n!=null&&(i=a(n)),u):a},u.context=function(c){return arguments.length?(c==null?n=i=null:i=a(n=c),u):n},u}function Ea(e,t,r){var n=null,a=le(!0),i=null,o=Zi,u=null,c=ih(s);e=typeof e=="function"?e:e===void 0?bg:le(+e),t=typeof t=="function"?t:le(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?xg:le(+r);function s(l){var h,d,y,v=(l=oh(l)).length,p,x=!1,O,w=new Array(v),A=new Array(v);for(i==null&&(u=o(O=c())),h=0;h<=v;++h){if(!(h<v&&a(p=l[h],h,l))===x)if(x=!x)d=h,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),y=h-1;y>=d;--y)u.point(w[y],A[y]);u.lineEnd(),u.areaEnd()}x&&(w[h]=+e(p,h,l),A[h]=+t(p,h,l),u.point(n?+n(p,h,l):w[h],r?+r(p,h,l):A[h]))}if(O)return u=null,O+""||null}function f(){return wg().defined(a).curve(o).context(i)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:le(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:le(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:le(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:le(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:le(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:le(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(a=typeof l=="function"?l:le(!!l),s):a},s.curve=function(l){return arguments.length?(o=l,i!=null&&(u=o(i)),s):o},s.context=function(l){return arguments.length?(l==null?i=u=null:u=o(i=l),s):i},s}class Og{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function vO(e){return new Og(e,!0)}function yO(e){return new Og(e,!1)}const uh={draw(e,t){const r=ut(t/Fa);e.moveTo(r,0),e.arc(0,0,r,0,zi)}},mO={draw(e,t){const r=ut(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Ag=ut(1/3),gO=Ag*2,bO={draw(e,t){const r=ut(t/gO),n=r*Ag;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},xO={draw(e,t){const r=ut(t),n=-r/2;e.rect(n,n,r,r)}},wO=.8908130915292852,_g=Ha(Fa/10)/Ha(7*Fa/10),OO=Ha(zi/10)*_g,AO=-yg(zi/10)*_g,_O={draw(e,t){const r=ut(t*wO),n=OO*r,a=AO*r;e.moveTo(0,-r),e.lineTo(n,a);for(let i=1;i<5;++i){const o=zi*i/5,u=yg(o),c=Ha(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*a,c*n+u*a)}e.closePath()}},Nu=ut(3),SO={draw(e,t){const r=-ut(t/(Nu*3));e.moveTo(0,r*2),e.lineTo(-Nu*r,-r),e.lineTo(Nu*r,-r),e.closePath()}},Ue=-.5,Ge=ut(3)/2,Il=1/ut(12),PO=(Il/2+1)*3,EO={draw(e,t){const r=ut(t/PO),n=r/2,a=r*Il,i=n,o=r*Il+r,u=-i,c=o;e.moveTo(n,a),e.lineTo(i,o),e.lineTo(u,c),e.lineTo(Ue*n-Ge*a,Ge*n+Ue*a),e.lineTo(Ue*i-Ge*o,Ge*i+Ue*o),e.lineTo(Ue*u-Ge*c,Ge*u+Ue*c),e.lineTo(Ue*n+Ge*a,Ue*a-Ge*n),e.lineTo(Ue*i+Ge*o,Ue*o-Ge*i),e.lineTo(Ue*u+Ge*c,Ue*c-Ge*u),e.closePath()}};function jO(e,t){let r=null,n=ih(a);e=typeof e=="function"?e:le(e||uh),t=typeof t=="function"?t:le(t===void 0?64:+t);function a(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return a.type=function(i){return arguments.length?(e=typeof i=="function"?i:le(i),a):e},a.size=function(i){return arguments.length?(t=typeof i=="function"?i:le(+i),a):t},a.context=function(i){return arguments.length?(r=i??null,a):r},a}function Va(){}function za(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function Sg(e){this._context=e}Sg.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:za(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:za(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function TO(e){return new Sg(e)}function Pg(e){this._context=e}Pg.prototype={areaStart:Va,areaEnd:Va,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:za(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function MO(e){return new Pg(e)}function Eg(e){this._context=e}Eg.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:za(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function $O(e){return new Eg(e)}function jg(e){this._context=e}jg.prototype={areaStart:Va,areaEnd:Va,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function CO(e){return new jg(e)}function _d(e){return e<0?-1:1}function Sd(e,t,r){var n=e._x1-e._x0,a=t-e._x1,i=(e._y1-e._y0)/(n||a<0&&-0),o=(r-e._y1)/(a||n<0&&-0),u=(i*a+o*n)/(n+a);return(_d(i)+_d(o))*Math.min(Math.abs(i),Math.abs(o),.5*Math.abs(u))||0}function Pd(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Lu(e,t,r){var n=e._x0,a=e._y0,i=e._x1,o=e._y1,u=(i-n)/3;e._context.bezierCurveTo(n+u,a+u*t,i-u,o-u*r,i,o)}function Za(e){this._context=e}Za.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Lu(this,this._t0,Pd(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Lu(this,Pd(this,r=Sd(this,e,t)),r);break;default:Lu(this,this._t0,r=Sd(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Tg(e){this._context=new Mg(e)}(Tg.prototype=Object.create(Za.prototype)).point=function(e,t){Za.prototype.point.call(this,t,e)};function Mg(e){this._context=e}Mg.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,a,i){this._context.bezierCurveTo(t,e,n,r,i,a)}};function IO(e){return new Za(e)}function kO(e){return new Tg(e)}function $g(e){this._context=e}$g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=Ed(e),a=Ed(t),i=0,o=1;o<r;++i,++o)this._context.bezierCurveTo(n[0][i],a[0][i],n[1][i],a[1][i],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function Ed(e){var t,r=e.length-1,n,a=new Array(r),i=new Array(r),o=new Array(r);for(a[0]=0,i[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)a[t]=1,i[t]=4,o[t]=4*e[t]+2*e[t+1];for(a[r-1]=2,i[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=a[t]/i[t-1],i[t]-=n,o[t]-=n*o[t-1];for(a[r-1]=o[r-1]/i[r-1],t=r-2;t>=0;--t)a[t]=(o[t]-a[t+1])/i[t];for(i[r-1]=(e[r]+a[r-1])/2,t=0;t<r-1;++t)i[t]=2*e[t+1]-a[t+1];return[a,i]}function RO(e){return new $g(e)}function Wi(e,t){this._context=e,this._t=t}Wi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function DO(e){return new Wi(e,.5)}function NO(e){return new Wi(e,0)}function LO(e){return new Wi(e,1)}function $r(e,t){if((o=e.length)>1)for(var r=1,n,a,i=e[t[0]],o,u=i.length;r<o;++r)for(a=i,i=e[t[r]],n=0;n<u;++n)i[n][1]+=i[n][0]=isNaN(a[n][1])?a[n][0]:a[n][1]}function kl(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function qO(e,t){return e[t]}function BO(e){const t=[];return t.key=e,t}function HO(){var e=le([]),t=kl,r=$r,n=qO;function a(i){var o=Array.from(e.apply(this,arguments),BO),u,c=o.length,s=-1,f;for(const l of i)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,i)]).data=l;for(u=0,f=oh(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return a.keys=function(i){return arguments.length?(e=typeof i=="function"?i:le(Array.from(i)),a):e},a.value=function(i){return arguments.length?(n=typeof i=="function"?i:le(+i),a):n},a.order=function(i){return arguments.length?(t=i==null?kl:typeof i=="function"?i:le(Array.from(i)),a):t},a.offset=function(i){return arguments.length?(r=i??$r,a):r},a}function FO(e,t){if((n=e.length)>0){for(var r,n,a=0,i=e[0].length,o;a<i;++a){for(o=r=0;r<n;++r)o+=e[r][a][1]||0;if(o)for(r=0;r<n;++r)e[r][a][1]/=o}$r(e,t)}}function VO(e,t){if((a=e.length)>0){for(var r=0,n=e[t[0]],a,i=n.length;r<i;++r){for(var o=0,u=0;o<a;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}$r(e,t)}}function zO(e,t){if(!(!((o=e.length)>0)||!((i=(a=e[t[0]]).length)>0))){for(var r=0,n=1,a,i,o;n<i;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,h=f[n-1][1]||0,d=(l-h)/2,y=0;y<u;++y){var v=e[t[y]],p=v[n][1]||0,x=v[n-1][1]||0;d+=p-x}c+=l,s+=d*l}a[n-1][1]+=a[n-1][0]=r,c&&(r-=s/c)}a[n-1][1]+=a[n-1][0]=r,$r(e,t)}}function Mn(e){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mn(e)}var ZO=["type","size","sizeType"];function Rl(){return Rl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Rl.apply(this,arguments)}function jd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Td(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jd(Object(r),!0).forEach(function(n){WO(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function WO(e,t,r){return t=UO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function UO(e){var t=GO(e,"string");return Mn(t)=="symbol"?t:t+""}function GO(e,t){if(Mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function KO(e,t){if(e==null)return{};var r=XO(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function XO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Cg={symbolCircle:uh,symbolCross:mO,symbolDiamond:bO,symbolSquare:xO,symbolStar:_O,symbolTriangle:SO,symbolWye:EO},YO=Math.PI/180,JO=function(t){var r="symbol".concat(Vi(t));return Cg[r]||uh},QO=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var a=18*YO;return 1.25*t*t*(Math.tan(a)-Math.tan(a*2)*Math.pow(Math.tan(a),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},e8=function(t,r){Cg["symbol".concat(Vi(t))]=r},ch=function(t){var r=t.type,n=r===void 0?"circle":r,a=t.size,i=a===void 0?64:a,o=t.sizeType,u=o===void 0?"area":o,c=KO(t,ZO),s=Td(Td({},c),{},{type:n,size:i,sizeType:u}),f=function(){var p=JO(n),x=jO().type(p).size(QO(i,u,n));return x()},l=s.className,h=s.cx,d=s.cy,y=K(s,!0);return h===+h&&d===+d&&i===+i?P.createElement("path",Rl({},y,{className:Q("recharts-symbols",l),transform:"translate(".concat(h,", ").concat(d,")"),d:f()})):null};ch.registerSymbol=e8;function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}function Dl(){return Dl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Dl.apply(this,arguments)}function Md(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function t8(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Md(Object(r),!0).forEach(function(n){$n(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Md(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function r8(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n8(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,kg(n.key),n)}}function a8(e,t,r){return t&&n8(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function i8(e,t,r){return t=Wa(t),o8(e,Ig()?Reflect.construct(t,r||[],Wa(e).constructor):t.apply(e,r))}function o8(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return u8(e)}function u8(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ig(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ig=function(){return!!e})()}function Wa(e){return Wa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Wa(e)}function c8(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Nl(e,t)}function Nl(e,t){return Nl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Nl(e,t)}function $n(e,t,r){return t=kg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kg(e){var t=s8(e,"string");return Cr(t)=="symbol"?t:t+""}function s8(e,t){if(Cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ke=32,sh=(function(e){function t(){return r8(this,t),i8(this,t,arguments)}return c8(t,e),a8(t,[{key:"renderIcon",value:function(n){var a=this.props.inactiveColor,i=Ke/2,o=Ke/6,u=Ke/3,c=n.inactive?a:n.color;if(n.type==="plainline")return P.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:i,x2:Ke,y2:i,className:"recharts-legend-icon"});if(n.type==="line")return P.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(i,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(i,`
            H`).concat(Ke,"M").concat(2*u,",").concat(i,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(i),className:"recharts-legend-icon"});if(n.type==="rect")return P.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(Ke/8,"h").concat(Ke,"v").concat(Ke*3/4,"h").concat(-Ke,"z"),className:"recharts-legend-icon"});if(P.isValidElement(n.legendIcon)){var s=t8({},n);return delete s.legendIcon,P.cloneElement(n.legendIcon,s)}return P.createElement(ch,{fill:c,cx:i,cy:i,size:Ke,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,a=this.props,i=a.payload,o=a.iconSize,u=a.layout,c=a.formatter,s=a.inactiveColor,f={x:0,y:0,width:Ke,height:Ke},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return i.map(function(d,y){var v=d.formatter||c,p=Q($n($n({"recharts-legend-item":!0},"legend-item-".concat(y),!0),"inactive",d.inactive));if(d.type==="none")return null;var x=X(d.value)?null:d.value;At(!X(d.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var O=d.inactive?s:d.color;return P.createElement("li",Dl({className:p,style:l,key:"legend-item-".concat(y)},Bt(n.props,d,y)),P.createElement(Tl,{width:o,height:o,viewBox:f,style:h},n.renderIcon(d)),P.createElement("span",{className:"recharts-legend-item-text",style:{color:O}},v?v(x,d,y):x))})}},{key:"render",value:function(){var n=this.props,a=n.payload,i=n.layout,o=n.align;if(!a||!a.length)return null;var u={padding:0,margin:0,textAlign:i==="horizontal"?o:"left"};return P.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])})(g.PureComponent);$n(sh,"displayName","Legend");$n(sh,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var qu,$d;function l8(){if($d)return qu;$d=1;var e=qi();function t(){this.__data__=new e,this.size=0}return qu=t,qu}var Bu,Cd;function f8(){if(Cd)return Bu;Cd=1;function e(t){var r=this.__data__,n=r.delete(t);return this.size=r.size,n}return Bu=e,Bu}var Hu,Id;function h8(){if(Id)return Hu;Id=1;function e(t){return this.__data__.get(t)}return Hu=e,Hu}var Fu,kd;function d8(){if(kd)return Fu;kd=1;function e(t){return this.__data__.has(t)}return Fu=e,Fu}var Vu,Rd;function p8(){if(Rd)return Vu;Rd=1;var e=qi(),t=Qf(),r=eh(),n=200;function a(i,o){var u=this.__data__;if(u instanceof e){var c=u.__data__;if(!t||c.length<n-1)return c.push([i,o]),this.size=++u.size,this;u=this.__data__=new r(c)}return u.set(i,o),this.size=u.size,this}return Vu=a,Vu}var zu,Dd;function Rg(){if(Dd)return zu;Dd=1;var e=qi(),t=l8(),r=f8(),n=h8(),a=d8(),i=p8();function o(u){var c=this.__data__=new e(u);this.size=c.size}return o.prototype.clear=t,o.prototype.delete=r,o.prototype.get=n,o.prototype.has=a,o.prototype.set=i,zu=o,zu}var Zu,Nd;function v8(){if(Nd)return Zu;Nd=1;var e="__lodash_hash_undefined__";function t(r){return this.__data__.set(r,e),this}return Zu=t,Zu}var Wu,Ld;function y8(){if(Ld)return Wu;Ld=1;function e(t){return this.__data__.has(t)}return Wu=e,Wu}var Uu,qd;function Dg(){if(qd)return Uu;qd=1;var e=eh(),t=v8(),r=y8();function n(a){var i=-1,o=a==null?0:a.length;for(this.__data__=new e;++i<o;)this.add(a[i])}return n.prototype.add=n.prototype.push=t,n.prototype.has=r,Uu=n,Uu}var Gu,Bd;function Ng(){if(Bd)return Gu;Bd=1;function e(t,r){for(var n=-1,a=t==null?0:t.length;++n<a;)if(r(t[n],n,t))return!0;return!1}return Gu=e,Gu}var Ku,Hd;function Lg(){if(Hd)return Ku;Hd=1;function e(t,r){return t.has(r)}return Ku=e,Ku}var Xu,Fd;function qg(){if(Fd)return Xu;Fd=1;var e=Dg(),t=Ng(),r=Lg(),n=1,a=2;function i(o,u,c,s,f,l){var h=c&n,d=o.length,y=u.length;if(d!=y&&!(h&&y>d))return!1;var v=l.get(o),p=l.get(u);if(v&&p)return v==u&&p==o;var x=-1,O=!0,w=c&a?new e:void 0;for(l.set(o,u),l.set(u,o);++x<d;){var A=o[x],m=u[x];if(s)var b=h?s(m,A,x,u,o,l):s(A,m,x,o,u,l);if(b!==void 0){if(b)continue;O=!1;break}if(w){if(!t(u,function(_,S){if(!r(w,S)&&(A===_||f(A,_,c,s,l)))return w.push(S)})){O=!1;break}}else if(!(A===m||f(A,m,c,s,l))){O=!1;break}}return l.delete(o),l.delete(u),O}return Xu=i,Xu}var Yu,Vd;function m8(){if(Vd)return Yu;Vd=1;var e=pt(),t=e.Uint8Array;return Yu=t,Yu}var Ju,zd;function g8(){if(zd)return Ju;zd=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(a,i){n[++r]=[i,a]}),n}return Ju=e,Ju}var Qu,Zd;function lh(){if(Zd)return Qu;Zd=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(a){n[++r]=a}),n}return Qu=e,Qu}var ec,Wd;function b8(){if(Wd)return ec;Wd=1;var e=ma(),t=m8(),r=Jf(),n=qg(),a=g8(),i=lh(),o=1,u=2,c="[object Boolean]",s="[object Date]",f="[object Error]",l="[object Map]",h="[object Number]",d="[object RegExp]",y="[object Set]",v="[object String]",p="[object Symbol]",x="[object ArrayBuffer]",O="[object DataView]",w=e?e.prototype:void 0,A=w?w.valueOf:void 0;function m(b,_,S,j,C,E,M){switch(S){case O:if(b.byteLength!=_.byteLength||b.byteOffset!=_.byteOffset)return!1;b=b.buffer,_=_.buffer;case x:return!(b.byteLength!=_.byteLength||!E(new t(b),new t(_)));case c:case s:case h:return r(+b,+_);case f:return b.name==_.name&&b.message==_.message;case d:case v:return b==_+"";case l:var $=a;case y:var k=j&o;if($||($=i),b.size!=_.size&&!k)return!1;var I=M.get(b);if(I)return I==_;j|=u,M.set(b,_);var D=n($(b),$(_),j,C,E,M);return M.delete(b),D;case p:if(A)return A.call(b)==A.call(_)}return!1}return ec=m,ec}var tc,Ud;function Bg(){if(Ud)return tc;Ud=1;function e(t,r){for(var n=-1,a=r.length,i=t.length;++n<a;)t[i+n]=r[n];return t}return tc=e,tc}var rc,Gd;function x8(){if(Gd)return rc;Gd=1;var e=Bg(),t=qe();function r(n,a,i){var o=a(n);return t(n)?o:e(o,i(n))}return rc=r,rc}var nc,Kd;function w8(){if(Kd)return nc;Kd=1;function e(t,r){for(var n=-1,a=t==null?0:t.length,i=0,o=[];++n<a;){var u=t[n];r(u,n,t)&&(o[i++]=u)}return o}return nc=e,nc}var ac,Xd;function O8(){if(Xd)return ac;Xd=1;function e(){return[]}return ac=e,ac}var ic,Yd;function A8(){if(Yd)return ic;Yd=1;var e=w8(),t=O8(),r=Object.prototype,n=r.propertyIsEnumerable,a=Object.getOwnPropertySymbols,i=a?function(o){return o==null?[]:(o=Object(o),e(a(o),function(u){return n.call(o,u)}))}:t;return ic=i,ic}var oc,Jd;function _8(){if(Jd)return oc;Jd=1;function e(t,r){for(var n=-1,a=Array(t);++n<t;)a[n]=r(n);return a}return oc=e,oc}var uc,Qd;function S8(){if(Qd)return uc;Qd=1;var e=Tt(),t=Mt(),r="[object Arguments]";function n(a){return t(a)&&e(a)==r}return uc=n,uc}var cc,ep;function fh(){if(ep)return cc;ep=1;var e=S8(),t=Mt(),r=Object.prototype,n=r.hasOwnProperty,a=r.propertyIsEnumerable,i=e((function(){return arguments})())?e:function(o){return t(o)&&n.call(o,"callee")&&!a.call(o,"callee")};return cc=i,cc}var wn={exports:{}},sc,tp;function P8(){if(tp)return sc;tp=1;function e(){return!1}return sc=e,sc}wn.exports;var rp;function Hg(){return rp||(rp=1,(function(e,t){var r=pt(),n=P8(),a=t&&!t.nodeType&&t,i=a&&!0&&e&&!e.nodeType&&e,o=i&&i.exports===a,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s})(wn,wn.exports)),wn.exports}var lc,np;function hh(){if(np)return lc;np=1;var e=9007199254740991,t=/^(?:0|[1-9]\d*)$/;function r(n,a){var i=typeof n;return a=a??e,!!a&&(i=="number"||i!="symbol"&&t.test(n))&&n>-1&&n%1==0&&n<a}return lc=r,lc}var fc,ap;function dh(){if(ap)return fc;ap=1;var e=9007199254740991;function t(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=e}return fc=t,fc}var hc,ip;function E8(){if(ip)return hc;ip=1;var e=Tt(),t=dh(),r=Mt(),n="[object Arguments]",a="[object Array]",i="[object Boolean]",o="[object Date]",u="[object Error]",c="[object Function]",s="[object Map]",f="[object Number]",l="[object Object]",h="[object RegExp]",d="[object Set]",y="[object String]",v="[object WeakMap]",p="[object ArrayBuffer]",x="[object DataView]",O="[object Float32Array]",w="[object Float64Array]",A="[object Int8Array]",m="[object Int16Array]",b="[object Int32Array]",_="[object Uint8Array]",S="[object Uint8ClampedArray]",j="[object Uint16Array]",C="[object Uint32Array]",E={};E[O]=E[w]=E[A]=E[m]=E[b]=E[_]=E[S]=E[j]=E[C]=!0,E[n]=E[a]=E[p]=E[i]=E[x]=E[o]=E[u]=E[c]=E[s]=E[f]=E[l]=E[h]=E[d]=E[y]=E[v]=!1;function M($){return r($)&&t($.length)&&!!E[e($)]}return hc=M,hc}var dc,op;function Fg(){if(op)return dc;op=1;function e(t){return function(r){return t(r)}}return dc=e,dc}var On={exports:{}};On.exports;var up;function j8(){return up||(up=1,(function(e,t){var r=cg(),n=t&&!t.nodeType&&t,a=n&&!0&&e&&!e.nodeType&&e,i=a&&a.exports===n,o=i&&r.process,u=(function(){try{var c=a&&a.require&&a.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}})();e.exports=u})(On,On.exports)),On.exports}var pc,cp;function Vg(){if(cp)return pc;cp=1;var e=E8(),t=Fg(),r=j8(),n=r&&r.isTypedArray,a=n?t(n):e;return pc=a,pc}var vc,sp;function T8(){if(sp)return vc;sp=1;var e=_8(),t=fh(),r=qe(),n=Hg(),a=hh(),i=Vg(),o=Object.prototype,u=o.hasOwnProperty;function c(s,f){var l=r(s),h=!l&&t(s),d=!l&&!h&&n(s),y=!l&&!h&&!d&&i(s),v=l||h||d||y,p=v?e(s.length,String):[],x=p.length;for(var O in s)(f||u.call(s,O))&&!(v&&(O=="length"||d&&(O=="offset"||O=="parent")||y&&(O=="buffer"||O=="byteLength"||O=="byteOffset")||a(O,x)))&&p.push(O);return p}return vc=c,vc}var yc,lp;function M8(){if(lp)return yc;lp=1;var e=Object.prototype;function t(r){var n=r&&r.constructor,a=typeof n=="function"&&n.prototype||e;return r===a}return yc=t,yc}var mc,fp;function zg(){if(fp)return mc;fp=1;function e(t,r){return function(n){return t(r(n))}}return mc=e,mc}var gc,hp;function $8(){if(hp)return gc;hp=1;var e=zg(),t=e(Object.keys,Object);return gc=t,gc}var bc,dp;function C8(){if(dp)return bc;dp=1;var e=M8(),t=$8(),r=Object.prototype,n=r.hasOwnProperty;function a(i){if(!e(i))return t(i);var o=[];for(var u in Object(i))n.call(i,u)&&u!="constructor"&&o.push(u);return o}return bc=a,bc}var xc,pp;function ba(){if(pp)return xc;pp=1;var e=Yf(),t=dh();function r(n){return n!=null&&t(n.length)&&!e(n)}return xc=r,xc}var wc,vp;function Ui(){if(vp)return wc;vp=1;var e=T8(),t=C8(),r=ba();function n(a){return r(a)?e(a):t(a)}return wc=n,wc}var Oc,yp;function I8(){if(yp)return Oc;yp=1;var e=x8(),t=A8(),r=Ui();function n(a){return e(a,r,t)}return Oc=n,Oc}var Ac,mp;function k8(){if(mp)return Ac;mp=1;var e=I8(),t=1,r=Object.prototype,n=r.hasOwnProperty;function a(i,o,u,c,s,f){var l=u&t,h=e(i),d=h.length,y=e(o),v=y.length;if(d!=v&&!l)return!1;for(var p=d;p--;){var x=h[p];if(!(l?x in o:n.call(o,x)))return!1}var O=f.get(i),w=f.get(o);if(O&&w)return O==o&&w==i;var A=!0;f.set(i,o),f.set(o,i);for(var m=l;++p<d;){x=h[p];var b=i[x],_=o[x];if(c)var S=l?c(_,b,x,o,i,f):c(b,_,x,i,o,f);if(!(S===void 0?b===_||s(b,_,u,c,f):S)){A=!1;break}m||(m=x=="constructor")}if(A&&!m){var j=i.constructor,C=o.constructor;j!=C&&"constructor"in i&&"constructor"in o&&!(typeof j=="function"&&j instanceof j&&typeof C=="function"&&C instanceof C)&&(A=!1)}return f.delete(i),f.delete(o),A}return Ac=a,Ac}var _c,gp;function R8(){if(gp)return _c;gp=1;var e=pr(),t=pt(),r=e(t,"DataView");return _c=r,_c}var Sc,bp;function D8(){if(bp)return Sc;bp=1;var e=pr(),t=pt(),r=e(t,"Promise");return Sc=r,Sc}var Pc,xp;function Zg(){if(xp)return Pc;xp=1;var e=pr(),t=pt(),r=e(t,"Set");return Pc=r,Pc}var Ec,wp;function N8(){if(wp)return Ec;wp=1;var e=pr(),t=pt(),r=e(t,"WeakMap");return Ec=r,Ec}var jc,Op;function L8(){if(Op)return jc;Op=1;var e=R8(),t=Qf(),r=D8(),n=Zg(),a=N8(),i=Tt(),o=sg(),u="[object Map]",c="[object Object]",s="[object Promise]",f="[object Set]",l="[object WeakMap]",h="[object DataView]",d=o(e),y=o(t),v=o(r),p=o(n),x=o(a),O=i;return(e&&O(new e(new ArrayBuffer(1)))!=h||t&&O(new t)!=u||r&&O(r.resolve())!=s||n&&O(new n)!=f||a&&O(new a)!=l)&&(O=function(w){var A=i(w),m=A==c?w.constructor:void 0,b=m?o(m):"";if(b)switch(b){case d:return h;case y:return u;case v:return s;case p:return f;case x:return l}return A}),jc=O,jc}var Tc,Ap;function q8(){if(Ap)return Tc;Ap=1;var e=Rg(),t=qg(),r=b8(),n=k8(),a=L8(),i=qe(),o=Hg(),u=Vg(),c=1,s="[object Arguments]",f="[object Array]",l="[object Object]",h=Object.prototype,d=h.hasOwnProperty;function y(v,p,x,O,w,A){var m=i(v),b=i(p),_=m?f:a(v),S=b?f:a(p);_=_==s?l:_,S=S==s?l:S;var j=_==l,C=S==l,E=_==S;if(E&&o(v)){if(!o(p))return!1;m=!0,j=!1}if(E&&!j)return A||(A=new e),m||u(v)?t(v,p,x,O,w,A):r(v,p,_,x,O,w,A);if(!(x&c)){var M=j&&d.call(v,"__wrapped__"),$=C&&d.call(p,"__wrapped__");if(M||$){var k=M?v.value():v,I=$?p.value():p;return A||(A=new e),w(k,I,x,O,A)}}return E?(A||(A=new e),n(v,p,x,O,w,A)):!1}return Tc=y,Tc}var Mc,_p;function ph(){if(_p)return Mc;_p=1;var e=q8(),t=Mt();function r(n,a,i,o,u){return n===a?!0:n==null||a==null||!t(n)&&!t(a)?n!==n&&a!==a:e(n,a,i,o,r,u)}return Mc=r,Mc}var $c,Sp;function B8(){if(Sp)return $c;Sp=1;var e=Rg(),t=ph(),r=1,n=2;function a(i,o,u,c){var s=u.length,f=s,l=!c;if(i==null)return!f;for(i=Object(i);s--;){var h=u[s];if(l&&h[2]?h[1]!==i[h[0]]:!(h[0]in i))return!1}for(;++s<f;){h=u[s];var d=h[0],y=i[d],v=h[1];if(l&&h[2]){if(y===void 0&&!(d in i))return!1}else{var p=new e;if(c)var x=c(y,v,d,i,o,p);if(!(x===void 0?t(v,y,r|n,c,p):x))return!1}}return!0}return $c=a,$c}var Cc,Pp;function Wg(){if(Pp)return Cc;Pp=1;var e=Ht();function t(r){return r===r&&!e(r)}return Cc=t,Cc}var Ic,Ep;function H8(){if(Ep)return Ic;Ep=1;var e=Wg(),t=Ui();function r(n){for(var a=t(n),i=a.length;i--;){var o=a[i],u=n[o];a[i]=[o,u,e(u)]}return a}return Ic=r,Ic}var kc,jp;function Ug(){if(jp)return kc;jp=1;function e(t,r){return function(n){return n==null?!1:n[t]===r&&(r!==void 0||t in Object(n))}}return kc=e,kc}var Rc,Tp;function F8(){if(Tp)return Rc;Tp=1;var e=B8(),t=H8(),r=Ug();function n(a){var i=t(a);return i.length==1&&i[0][2]?r(i[0][0],i[0][1]):function(o){return o===a||e(o,a,i)}}return Rc=n,Rc}var Dc,Mp;function V8(){if(Mp)return Dc;Mp=1;function e(t,r){return t!=null&&r in Object(t)}return Dc=e,Dc}var Nc,$p;function z8(){if($p)return Nc;$p=1;var e=hg(),t=fh(),r=qe(),n=hh(),a=dh(),i=Hi();function o(u,c,s){c=e(c,u);for(var f=-1,l=c.length,h=!1;++f<l;){var d=i(c[f]);if(!(h=u!=null&&s(u,d)))break;u=u[d]}return h||++f!=l?h:(l=u==null?0:u.length,!!l&&a(l)&&n(d,l)&&(r(u)||t(u)))}return Nc=o,Nc}var Lc,Cp;function Z8(){if(Cp)return Lc;Cp=1;var e=V8(),t=z8();function r(n,a){return n!=null&&t(n,a,e)}return Lc=r,Lc}var qc,Ip;function W8(){if(Ip)return qc;Ip=1;var e=ph(),t=dg(),r=Z8(),n=Xf(),a=Wg(),i=Ug(),o=Hi(),u=1,c=2;function s(f,l){return n(f)&&a(l)?i(o(f),l):function(h){var d=t(h,f);return d===void 0&&d===l?r(h,f):e(l,d,u|c)}}return qc=s,qc}var Bc,kp;function on(){if(kp)return Bc;kp=1;function e(t){return t}return Bc=e,Bc}var Hc,Rp;function U8(){if(Rp)return Hc;Rp=1;function e(t){return function(r){return r==null?void 0:r[t]}}return Hc=e,Hc}var Fc,Dp;function G8(){if(Dp)return Fc;Dp=1;var e=rh();function t(r){return function(n){return e(n,r)}}return Fc=t,Fc}var Vc,Np;function K8(){if(Np)return Vc;Np=1;var e=U8(),t=G8(),r=Xf(),n=Hi();function a(i){return r(i)?e(n(i)):t(i)}return Vc=a,Vc}var zc,Lp;function vt(){if(Lp)return zc;Lp=1;var e=F8(),t=W8(),r=on(),n=qe(),a=K8();function i(o){return typeof o=="function"?o:o==null?r:typeof o=="object"?n(o)?t(o[0],o[1]):e(o):a(o)}return zc=i,zc}var Zc,qp;function Gg(){if(qp)return Zc;qp=1;function e(t,r,n,a){for(var i=t.length,o=n+(a?1:-1);a?o--:++o<i;)if(r(t[o],o,t))return o;return-1}return Zc=e,Zc}var Wc,Bp;function X8(){if(Bp)return Wc;Bp=1;function e(t){return t!==t}return Wc=e,Wc}var Uc,Hp;function Y8(){if(Hp)return Uc;Hp=1;function e(t,r,n){for(var a=n-1,i=t.length;++a<i;)if(t[a]===r)return a;return-1}return Uc=e,Uc}var Gc,Fp;function J8(){if(Fp)return Gc;Fp=1;var e=Gg(),t=X8(),r=Y8();function n(a,i,o){return i===i?r(a,i,o):e(a,t,o)}return Gc=n,Gc}var Kc,Vp;function Q8(){if(Vp)return Kc;Vp=1;var e=J8();function t(r,n){var a=r==null?0:r.length;return!!a&&e(r,n,0)>-1}return Kc=t,Kc}var Xc,zp;function eA(){if(zp)return Xc;zp=1;function e(t,r,n){for(var a=-1,i=t==null?0:t.length;++a<i;)if(n(r,t[a]))return!0;return!1}return Xc=e,Xc}var Yc,Zp;function tA(){if(Zp)return Yc;Zp=1;function e(){}return Yc=e,Yc}var Jc,Wp;function rA(){if(Wp)return Jc;Wp=1;var e=Zg(),t=tA(),r=lh(),n=1/0,a=e&&1/r(new e([,-0]))[1]==n?function(i){return new e(i)}:t;return Jc=a,Jc}var Qc,Up;function nA(){if(Up)return Qc;Up=1;var e=Dg(),t=Q8(),r=eA(),n=Lg(),a=rA(),i=lh(),o=200;function u(c,s,f){var l=-1,h=t,d=c.length,y=!0,v=[],p=v;if(f)y=!1,h=r;else if(d>=o){var x=s?null:a(c);if(x)return i(x);y=!1,h=n,p=new e}else p=s?[]:v;e:for(;++l<d;){var O=c[l],w=s?s(O):O;if(O=f||O!==0?O:0,y&&w===w){for(var A=p.length;A--;)if(p[A]===w)continue e;s&&p.push(w),v.push(O)}else h(p,w,f)||(p!==v&&p.push(w),v.push(O))}return v}return Qc=u,Qc}var es,Gp;function aA(){if(Gp)return es;Gp=1;var e=vt(),t=nA();function r(n,a){return n&&n.length?t(n,e(a,2)):[]}return es=r,es}var iA=aA();const Kp=se(iA);function Kg(e,t,r){return t===!0?Kp(e,r):X(t)?Kp(e,t):e}function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}var oA=["ref"];function Xp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function yt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xp(Object(r),!0).forEach(function(n){Gi(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Yp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Yg(n.key),n)}}function cA(e,t,r){return t&&Yp(e.prototype,t),r&&Yp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function sA(e,t,r){return t=Ua(t),lA(e,Xg()?Reflect.construct(t,r||[],Ua(e).constructor):t.apply(e,r))}function lA(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fA(e)}function fA(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Xg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Xg=function(){return!!e})()}function Ua(e){return Ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ua(e)}function hA(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ll(e,t)}function Ll(e,t){return Ll=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Ll(e,t)}function Gi(e,t,r){return t=Yg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yg(e){var t=dA(e,"string");return Ir(t)=="symbol"?t:t+""}function dA(e,t){if(Ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function pA(e,t){if(e==null)return{};var r=vA(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function vA(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function yA(e){return e.value}function mA(e,t){if(P.isValidElement(e))return P.cloneElement(e,t);if(typeof e=="function")return P.createElement(e,t);t.ref;var r=pA(t,oA);return P.createElement(sh,r)}var Jp=1,jr=(function(e){function t(){var r;uA(this,t);for(var n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return r=sA(this,t,[].concat(a)),Gi(r,"lastBoundingBox",{width:-1,height:-1}),r}return hA(t,e),cA(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,a=this.getBBox();a?(Math.abs(a.width-this.lastBoundingBox.width)>Jp||Math.abs(a.height-this.lastBoundingBox.height)>Jp)&&(this.lastBoundingBox.width=a.width,this.lastBoundingBox.height=a.height,n&&n(a)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?yt({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var a=this.props,i=a.layout,o=a.align,u=a.verticalAlign,c=a.margin,s=a.chartWidth,f=a.chartHeight,l,h;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&i==="vertical"){var d=this.getBBoxSnapshot();l={left:((s||0)-d.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var y=this.getBBoxSnapshot();h={top:((f||0)-y.height)/2}}else h=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return yt(yt({},l),h)}},{key:"render",value:function(){var n=this,a=this.props,i=a.content,o=a.width,u=a.height,c=a.wrapperStyle,s=a.payloadUniqBy,f=a.payload,l=yt(yt({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return P.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(d){n.wrapperNode=d}},mA(i,yt(yt({},this.props),{},{payload:Kg(f,s,yA)})))}}],[{key:"getWithHeight",value:function(n,a){var i=yt(yt({},this.defaultProps),n.props),o=i.layout;return o==="vertical"&&B(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||a}:null}}])})(g.PureComponent);Gi(jr,"displayName","Legend");Gi(jr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var ts,Qp;function gA(){if(Qp)return ts;Qp=1;var e=ma(),t=fh(),r=qe(),n=e?e.isConcatSpreadable:void 0;function a(i){return r(i)||t(i)||!!(n&&i&&i[n])}return ts=a,ts}var rs,ev;function Jg(){if(ev)return rs;ev=1;var e=Bg(),t=gA();function r(n,a,i,o,u){var c=-1,s=n.length;for(i||(i=t),u||(u=[]);++c<s;){var f=n[c];a>0&&i(f)?a>1?r(f,a-1,i,o,u):e(u,f):o||(u[u.length]=f)}return u}return rs=r,rs}var ns,tv;function bA(){if(tv)return ns;tv=1;function e(t){return function(r,n,a){for(var i=-1,o=Object(r),u=a(r),c=u.length;c--;){var s=u[t?c:++i];if(n(o[s],s,o)===!1)break}return r}}return ns=e,ns}var as,rv;function xA(){if(rv)return as;rv=1;var e=bA(),t=e();return as=t,as}var is,nv;function Qg(){if(nv)return is;nv=1;var e=xA(),t=Ui();function r(n,a){return n&&e(n,a,t)}return is=r,is}var os,av;function wA(){if(av)return os;av=1;var e=ba();function t(r,n){return function(a,i){if(a==null)return a;if(!e(a))return r(a,i);for(var o=a.length,u=n?o:-1,c=Object(a);(n?u--:++u<o)&&i(c[u],u,c)!==!1;);return a}}return os=t,os}var us,iv;function vh(){if(iv)return us;iv=1;var e=Qg(),t=wA(),r=t(e);return us=r,us}var cs,ov;function eb(){if(ov)return cs;ov=1;var e=vh(),t=ba();function r(n,a){var i=-1,o=t(n)?Array(n.length):[];return e(n,function(u,c,s){o[++i]=a(u,c,s)}),o}return cs=r,cs}var ss,uv;function OA(){if(uv)return ss;uv=1;function e(t,r){var n=t.length;for(t.sort(r);n--;)t[n]=t[n].value;return t}return ss=e,ss}var ls,cv;function AA(){if(cv)return ls;cv=1;var e=nn();function t(r,n){if(r!==n){var a=r!==void 0,i=r===null,o=r===r,u=e(r),c=n!==void 0,s=n===null,f=n===n,l=e(n);if(!s&&!l&&!u&&r>n||u&&c&&f&&!s&&!l||i&&c&&f||!a&&f||!o)return 1;if(!i&&!u&&!l&&r<n||l&&a&&o&&!i&&!u||s&&a&&o||!c&&o||!f)return-1}return 0}return ls=t,ls}var fs,sv;function _A(){if(sv)return fs;sv=1;var e=AA();function t(r,n,a){for(var i=-1,o=r.criteria,u=n.criteria,c=o.length,s=a.length;++i<c;){var f=e(o[i],u[i]);if(f){if(i>=s)return f;var l=a[i];return f*(l=="desc"?-1:1)}}return r.index-n.index}return fs=t,fs}var hs,lv;function SA(){if(lv)return hs;lv=1;var e=th(),t=rh(),r=vt(),n=eb(),a=OA(),i=Fg(),o=_A(),u=on(),c=qe();function s(f,l,h){l.length?l=e(l,function(v){return c(v)?function(p){return t(p,v.length===1?v[0]:v)}:v}):l=[u];var d=-1;l=e(l,i(r));var y=n(f,function(v,p,x){var O=e(l,function(w){return w(v)});return{criteria:O,index:++d,value:v}});return a(y,function(v,p){return o(v,p,h)})}return hs=s,hs}var ds,fv;function PA(){if(fv)return ds;fv=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return ds=e,ds}var ps,hv;function EA(){if(hv)return ps;hv=1;var e=PA(),t=Math.max;function r(n,a,i){return a=t(a===void 0?n.length-1:a,0),function(){for(var o=arguments,u=-1,c=t(o.length-a,0),s=Array(c);++u<c;)s[u]=o[a+u];u=-1;for(var f=Array(a+1);++u<a;)f[u]=o[u];return f[a]=i(s),e(n,this,f)}}return ps=r,ps}var vs,dv;function jA(){if(dv)return vs;dv=1;function e(t){return function(){return t}}return vs=e,vs}var ys,pv;function tb(){if(pv)return ys;pv=1;var e=pr(),t=(function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch{}})();return ys=t,ys}var ms,vv;function TA(){if(vv)return ms;vv=1;var e=jA(),t=tb(),r=on(),n=t?function(a,i){return t(a,"toString",{configurable:!0,enumerable:!1,value:e(i),writable:!0})}:r;return ms=n,ms}var gs,yv;function MA(){if(yv)return gs;yv=1;var e=800,t=16,r=Date.now;function n(a){var i=0,o=0;return function(){var u=r(),c=t-(u-o);if(o=u,c>0){if(++i>=e)return arguments[0]}else i=0;return a.apply(void 0,arguments)}}return gs=n,gs}var bs,mv;function $A(){if(mv)return bs;mv=1;var e=TA(),t=MA(),r=t(e);return bs=r,bs}var xs,gv;function CA(){if(gv)return xs;gv=1;var e=on(),t=EA(),r=$A();function n(a,i){return r(t(a,i,e),a+"")}return xs=n,xs}var ws,bv;function Ki(){if(bv)return ws;bv=1;var e=Jf(),t=ba(),r=hh(),n=Ht();function a(i,o,u){if(!n(u))return!1;var c=typeof o;return(c=="number"?t(u)&&r(o,u.length):c=="string"&&o in u)?e(u[o],i):!1}return ws=a,ws}var Os,xv;function IA(){if(xv)return Os;xv=1;var e=Jg(),t=SA(),r=CA(),n=Ki(),a=r(function(i,o){if(i==null)return[];var u=o.length;return u>1&&n(i,o[0],o[1])?o=[]:u>2&&n(o[0],o[1],o[2])&&(o=[o[0]]),t(i,e(o,1),[])});return Os=a,Os}var kA=IA();const yh=se(kA);function Cn(e){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cn(e)}function ql(){return ql=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ql.apply(this,arguments)}function RA(e,t){return qA(e)||LA(e,t)||NA(e,t)||DA()}function DA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function NA(e,t){if(e){if(typeof e=="string")return wv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wv(e,t)}}function wv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function LA(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function qA(e){if(Array.isArray(e))return e}function Ov(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function As(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ov(Object(r),!0).forEach(function(n){BA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ov(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function BA(e,t,r){return t=HA(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function HA(e){var t=FA(e,"string");return Cn(t)=="symbol"?t:t+""}function FA(e,t){if(Cn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function VA(e){return Array.isArray(e)&&Ae(e[0])&&Ae(e[1])?e.join(" ~ "):e}var zA=function(t){var r=t.separator,n=r===void 0?" : ":r,a=t.contentStyle,i=a===void 0?{}:a,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,h=t.itemSorter,d=t.wrapperClassName,y=t.labelClassName,v=t.label,p=t.labelFormatter,x=t.accessibilityLayer,O=x===void 0?!1:x,w=function(){if(f&&f.length){var M={padding:0,margin:0},$=(h?yh(f,h):f).map(function(k,I){if(k.type==="none")return null;var D=As({display:"block",paddingTop:4,paddingBottom:4,color:k.color||"#000"},u),N=k.formatter||l||VA,q=k.value,H=k.name,Z=q,U=H;if(N&&Z!=null&&U!=null){var V=N(q,H,k,I,f);if(Array.isArray(V)){var G=RA(V,2);Z=G[0],U=G[1]}else Z=V}return P.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(I),style:D},Ae(U)?P.createElement("span",{className:"recharts-tooltip-item-name"},U):null,Ae(U)?P.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,P.createElement("span",{className:"recharts-tooltip-item-value"},Z),P.createElement("span",{className:"recharts-tooltip-item-unit"},k.unit||""))});return P.createElement("ul",{className:"recharts-tooltip-item-list",style:M},$)}return null},A=As({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},i),m=As({margin:0},s),b=!re(v),_=b?v:"",S=Q("recharts-default-tooltip",d),j=Q("recharts-tooltip-label",y);b&&p&&f!==void 0&&f!==null&&(_=p(v,f));var C=O?{role:"status","aria-live":"assertive"}:{};return P.createElement("div",ql({className:S,style:A},C),P.createElement("p",{className:j,style:m},P.isValidElement(_)?_:"".concat(_)),w())};function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}function ja(e,t,r){return t=ZA(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ZA(e){var t=WA(e,"string");return In(t)=="symbol"?t:t+""}function WA(e,t){if(In(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(In(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var hn="recharts-tooltip-wrapper",UA={visibility:"hidden"};function GA(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return Q(hn,ja(ja(ja(ja({},"".concat(hn,"-right"),B(r)&&t&&B(t.x)&&r>=t.x),"".concat(hn,"-left"),B(r)&&t&&B(t.x)&&r<t.x),"".concat(hn,"-bottom"),B(n)&&t&&B(t.y)&&n>=t.y),"".concat(hn,"-top"),B(n)&&t&&B(t.y)&&n<t.y))}function Av(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,a=e.offsetTopLeft,i=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(i&&B(i[n]))return i[n];var f=r[n]-u-a,l=r[n]+a;if(t[n])return o[n]?f:l;if(o[n]){var h=f,d=c[n];return h<d?Math.max(l,c[n]):Math.max(f,c[n])}var y=l+u,v=c[n]+s;return y>v?Math.max(f,c[n]):Math.max(l,c[n])}function KA(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function XA(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,a=e.position,i=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=Av({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:a,reverseDirection:i,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=Av({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:a,reverseDirection:i,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=KA({translateX:f,translateY:l,useTranslate3d:u})):s=UA,{cssProperties:s,cssClasses:GA({translateX:f,translateY:l,coordinate:r})}}function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function _v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Sv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_v(Object(r),!0).forEach(function(n){Hl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_v(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function YA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function JA(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,nb(n.key),n)}}function QA(e,t,r){return t&&JA(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function e_(e,t,r){return t=Ga(t),t_(e,rb()?Reflect.construct(t,r||[],Ga(e).constructor):t.apply(e,r))}function t_(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return r_(e)}function r_(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(rb=function(){return!!e})()}function Ga(e){return Ga=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ga(e)}function n_(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Bl(e,t)}function Bl(e,t){return Bl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Bl(e,t)}function Hl(e,t,r){return t=nb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nb(e){var t=a_(e,"string");return kr(t)=="symbol"?t:t+""}function a_(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Pv=1,i_=(function(e){function t(){var r;YA(this,t);for(var n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return r=e_(this,t,[].concat(a)),Hl(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Hl(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return n_(t,e),QA(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>Pv||Math.abs(n.height-this.state.lastBoundingBox.height)>Pv)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,a;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((a=this.props.coordinate)===null||a===void 0?void 0:a.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,a=this.props,i=a.active,o=a.allowEscapeViewBox,u=a.animationDuration,c=a.animationEasing,s=a.children,f=a.coordinate,l=a.hasPayload,h=a.isAnimationActive,d=a.offset,y=a.position,v=a.reverseDirection,p=a.useTranslate3d,x=a.viewBox,O=a.wrapperStyle,w=XA({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:d,position:y,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:p,viewBox:x}),A=w.cssClasses,m=w.cssProperties,b=Sv(Sv({transition:h&&i?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&i&&l?"visible":"hidden",position:"absolute",top:0,left:0},O);return P.createElement("div",{tabIndex:-1,className:A,style:b,ref:function(S){n.wrapperNode=S}},s)}}])})(g.PureComponent),o_=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},un={isSsr:o_()};function Rr(e){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rr(e)}function Ev(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function jv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ev(Object(r),!0).forEach(function(n){mh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ev(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function u_(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c_(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ib(n.key),n)}}function s_(e,t,r){return t&&c_(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function l_(e,t,r){return t=Ka(t),f_(e,ab()?Reflect.construct(t,r||[],Ka(e).constructor):t.apply(e,r))}function f_(e,t){if(t&&(Rr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return h_(e)}function h_(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ab(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ab=function(){return!!e})()}function Ka(e){return Ka=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ka(e)}function d_(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Fl(e,t)}function Fl(e,t){return Fl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Fl(e,t)}function mh(e,t,r){return t=ib(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ib(e){var t=p_(e,"string");return Rr(t)=="symbol"?t:t+""}function p_(e,t){if(Rr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function v_(e){return e.dataKey}function y_(e,t){return P.isValidElement(e)?P.cloneElement(e,t):typeof e=="function"?P.createElement(e,t):P.createElement(zA,t)}var st=(function(e){function t(){return u_(this,t),l_(this,t,arguments)}return d_(t,e),s_(t,[{key:"render",value:function(){var n=this,a=this.props,i=a.active,o=a.allowEscapeViewBox,u=a.animationDuration,c=a.animationEasing,s=a.content,f=a.coordinate,l=a.filterNull,h=a.isAnimationActive,d=a.offset,y=a.payload,v=a.payloadUniqBy,p=a.position,x=a.reverseDirection,O=a.useTranslate3d,w=a.viewBox,A=a.wrapperStyle,m=y??[];l&&m.length&&(m=Kg(y.filter(function(_){return _.value!=null&&(_.hide!==!0||n.props.includeHidden)}),v,v_));var b=m.length>0;return P.createElement(i_,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:h,active:i,coordinate:f,hasPayload:b,offset:d,position:p,reverseDirection:x,useTranslate3d:O,viewBox:w,wrapperStyle:A},y_(s,jv(jv({},this.props),{},{payload:m})))}}])})(g.PureComponent);mh(st,"displayName","Tooltip");mh(st,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!un.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var _s,Tv;function m_(){if(Tv)return _s;Tv=1;var e=pt(),t=function(){return e.Date.now()};return _s=t,_s}var Ss,Mv;function g_(){if(Mv)return Ss;Mv=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return Ss=t,Ss}var Ps,$v;function b_(){if($v)return Ps;$v=1;var e=g_(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return Ps=r,Ps}var Es,Cv;function ob(){if(Cv)return Es;Cv=1;var e=b_(),t=Ht(),r=nn(),n=NaN,a=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,o=/^0o[0-7]+$/i,u=parseInt;function c(s){if(typeof s=="number")return s;if(r(s))return n;if(t(s)){var f=typeof s.valueOf=="function"?s.valueOf():s;s=t(f)?f+"":f}if(typeof s!="string")return s===0?s:+s;s=e(s);var l=i.test(s);return l||o.test(s)?u(s.slice(2),l?2:8):a.test(s)?n:+s}return Es=c,Es}var js,Iv;function x_(){if(Iv)return js;Iv=1;var e=Ht(),t=m_(),r=ob(),n="Expected a function",a=Math.max,i=Math.min;function o(u,c,s){var f,l,h,d,y,v,p=0,x=!1,O=!1,w=!0;if(typeof u!="function")throw new TypeError(n);c=r(c)||0,e(s)&&(x=!!s.leading,O="maxWait"in s,h=O?a(r(s.maxWait)||0,c):h,w="trailing"in s?!!s.trailing:w);function A($){var k=f,I=l;return f=l=void 0,p=$,d=u.apply(I,k),d}function m($){return p=$,y=setTimeout(S,c),x?A($):d}function b($){var k=$-v,I=$-p,D=c-k;return O?i(D,h-I):D}function _($){var k=$-v,I=$-p;return v===void 0||k>=c||k<0||O&&I>=h}function S(){var $=t();if(_($))return j($);y=setTimeout(S,b($))}function j($){return y=void 0,w&&f?A($):(f=l=void 0,d)}function C(){y!==void 0&&clearTimeout(y),p=0,f=v=l=y=void 0}function E(){return y===void 0?d:j(t())}function M(){var $=t(),k=_($);if(f=arguments,l=this,v=$,k){if(y===void 0)return m(v);if(O)return clearTimeout(y),y=setTimeout(S,c),A(v)}return y===void 0&&(y=setTimeout(S,c)),d}return M.cancel=C,M.flush=E,M}return js=o,js}var Ts,kv;function w_(){if(kv)return Ts;kv=1;var e=x_(),t=Ht(),r="Expected a function";function n(a,i,o){var u=!0,c=!0;if(typeof a!="function")throw new TypeError(r);return t(o)&&(u="leading"in o?!!o.leading:u,c="trailing"in o?!!o.trailing:c),e(a,i,{leading:u,maxWait:i,trailing:c})}return Ts=n,Ts}var O_=w_();const ub=se(O_);function kn(e){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kn(e)}function Rv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Ta(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rv(Object(r),!0).forEach(function(n){A_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function A_(e,t,r){return t=__(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function __(e){var t=S_(e,"string");return kn(t)=="symbol"?t:t+""}function S_(e,t){if(kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function P_(e,t){return M_(e)||T_(e,t)||j_(e,t)||E_()}function E_(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function j_(e,t){if(e){if(typeof e=="string")return Dv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dv(e,t)}}function Dv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function T_(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function M_(e){if(Array.isArray(e))return e}var $_=g.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,a=n===void 0?{width:-1,height:-1}:n,i=e.width,o=i===void 0?"100%":i,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,h=e.maxHeight,d=e.children,y=e.debounce,v=y===void 0?0:y,p=e.id,x=e.className,O=e.onResize,w=e.style,A=w===void 0?{}:w,m=g.useRef(null),b=g.useRef();b.current=O,g.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),m.current},configurable:!0})});var _=g.useState({containerWidth:a.width,containerHeight:a.height}),S=P_(_,2),j=S[0],C=S[1],E=g.useCallback(function($,k){C(function(I){var D=Math.round($),N=Math.round(k);return I.containerWidth===D&&I.containerHeight===N?I:{containerWidth:D,containerHeight:N}})},[]);g.useEffect(function(){var $=function(H){var Z,U=H[0].contentRect,V=U.width,G=U.height;E(V,G),(Z=b.current)===null||Z===void 0||Z.call(b,V,G)};v>0&&($=ub($,v,{trailing:!0,leading:!1}));var k=new ResizeObserver($),I=m.current.getBoundingClientRect(),D=I.width,N=I.height;return E(D,N),k.observe(m.current),function(){k.disconnect()}},[E,v]);var M=g.useMemo(function(){var $=j.containerWidth,k=j.containerHeight;if($<0||k<0)return null;At(er(o)||er(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),At(!r||r>0,"The aspect(%s) must be greater than zero.",r);var I=er(o)?$:o,D=er(c)?k:c;r&&r>0&&(I?D=I/r:D&&(I=D*r),h&&D>h&&(D=h)),At(I>0||D>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,I,D,o,c,f,l,r);var N=!Array.isArray(d)&&Ot(d.type).endsWith("Chart");return P.Children.map(d,function(q){return P.isValidElement(q)?g.cloneElement(q,Ta({width:I,height:D},N?{style:Ta({height:"100%",width:"100%",maxHeight:D,maxWidth:I},q.props.style)}:{})):q})},[r,d,c,h,l,f,j,o]);return P.createElement("div",{id:p?"".concat(p):void 0,className:Q("recharts-responsive-container",x),style:Ta(Ta({},A),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:h}),ref:m},M)}),gh=function(t){return null};gh.displayName="Cell";function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}function Nv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Vl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Nv(Object(r),!0).forEach(function(n){C_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function C_(e,t,r){return t=I_(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function I_(e){var t=k_(e,"string");return Rn(t)=="symbol"?t:t+""}function k_(e,t){if(Rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var xr={widthCache:{},cacheCount:0},R_=2e3,D_={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Lv="recharts_measurement_span";function N_(e){var t=Vl({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var _n=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||un.isSsr)return{width:0,height:0};var n=N_(r),a=JSON.stringify({text:t,copyStyle:n});if(xr.widthCache[a])return xr.widthCache[a];try{var i=document.getElementById(Lv);i||(i=document.createElement("span"),i.setAttribute("id",Lv),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var o=Vl(Vl({},D_),n);Object.assign(i.style,o),i.textContent="".concat(t);var u=i.getBoundingClientRect(),c={width:u.width,height:u.height};return xr.widthCache[a]=c,++xr.cacheCount>R_&&(xr.cacheCount=0,xr.widthCache={}),c}catch{return{width:0,height:0}}},L_=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function Dn(e){"@babel/helpers - typeof";return Dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dn(e)}function Xa(e,t){return F_(e)||H_(e,t)||B_(e,t)||q_()}function q_(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function B_(e,t){if(e){if(typeof e=="string")return qv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qv(e,t)}}function qv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function H_(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function F_(e){if(Array.isArray(e))return e}function V_(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Bv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Z_(n.key),n)}}function z_(e,t,r){return t&&Bv(e.prototype,t),r&&Bv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Z_(e){var t=W_(e,"string");return Dn(t)=="symbol"?t:t+""}function W_(e,t){if(Dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Hv=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Fv=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,U_=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,G_=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,cb={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},K_=Object.keys(cb),Ar="NaN";function X_(e,t){return e*cb[t]}var Ma=(function(){function e(t,r){V_(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!U_.test(r)&&(this.num=NaN,this.unit=""),K_.includes(r)&&(this.num=X_(t,r),this.unit="px")}return z_(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,a=(n=G_.exec(r))!==null&&n!==void 0?n:[],i=Xa(a,3),o=i[1],u=i[2];return new e(parseFloat(o),u??"")}}])})();function sb(e){if(e.includes(Ar))return Ar;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=Hv.exec(t))!==null&&r!==void 0?r:[],a=Xa(n,4),i=a[1],o=a[2],u=a[3],c=Ma.parse(i??""),s=Ma.parse(u??""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return Ar;t=t.replace(Hv,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,h=(l=Fv.exec(t))!==null&&l!==void 0?l:[],d=Xa(h,4),y=d[1],v=d[2],p=d[3],x=Ma.parse(y??""),O=Ma.parse(p??""),w=v==="+"?x.add(O):x.subtract(O);if(w.isNaN())return Ar;t=t.replace(Fv,w.toString())}return t}var Vv=/\(([^()]*)\)/;function Y_(e){for(var t=e;t.includes("(");){var r=Vv.exec(t),n=Xa(r,2),a=n[1];t=t.replace(Vv,sb(a))}return t}function J_(e){var t=e.replace(/\s+/g,"");return t=Y_(t),t=sb(t),t}function Q_(e){try{return J_(e)}catch{return Ar}}function Ms(e){var t=Q_(e.slice(5,-1));return t===Ar?"":t}var e6=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],t6=["dx","dy","angle","className","breakAll"];function zl(){return zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zl.apply(this,arguments)}function zv(e,t){if(e==null)return{};var r=r6(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function r6(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Zv(e,t){return o6(e)||i6(e,t)||a6(e,t)||n6()}function n6(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function a6(e,t){if(e){if(typeof e=="string")return Wv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wv(e,t)}}function Wv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function i6(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function o6(e){if(Array.isArray(e))return e}var lb=/[ \f\n\r\t\v\u2028\u2029]+/,fb=function(t){var r=t.children,n=t.breakAll,a=t.style;try{var i=[];re(r)||(n?i=r.toString().split(""):i=r.toString().split(lb));var o=i.map(function(c){return{word:c,width:_n(c,a).width}}),u=n?0:_n(" ",a).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},u6=function(t,r,n,a,i){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=B(o),l=u,h=function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return I.reduce(function(D,N){var q=N.word,H=N.width,Z=D[D.length-1];if(Z&&(a==null||i||Z.width+H+n<Number(a)))Z.words.push(q),Z.width+=H+n;else{var U={words:[q],width:H};D.push(U)}return D},[])},d=h(r),y=function(I){return I.reduce(function(D,N){return D.width>N.width?D:N})};if(!f)return d;for(var v="…",p=function(I){var D=l.slice(0,I),N=fb({breakAll:s,style:c,children:D+v}).wordsWithComputedWidth,q=h(N),H=q.length>o||y(q).width>Number(a);return[H,q]},x=0,O=l.length-1,w=0,A;x<=O&&w<=l.length-1;){var m=Math.floor((x+O)/2),b=m-1,_=p(b),S=Zv(_,2),j=S[0],C=S[1],E=p(m),M=Zv(E,1),$=M[0];if(!j&&!$&&(x=m+1),j&&$&&(O=m-1),!j&&$){A=C;break}w++}return A||d},Uv=function(t){var r=re(t)?[]:t.toString().split(lb);return[{words:r}]},c6=function(t){var r=t.width,n=t.scaleToFit,a=t.children,i=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!un.isSsr){var c,s,f=fb({breakAll:o,children:a,style:i});if(f){var l=f.wordsWithComputedWidth,h=f.spaceWidth;c=l,s=h}else return Uv(a);return u6({breakAll:o,children:a,maxLines:u,style:i},c,s,r,n)}return Uv(a)},Gv="#808080",Dr=function(t){var r=t.x,n=r===void 0?0:r,a=t.y,i=a===void 0?0:a,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,h=t.textAnchor,d=h===void 0?"start":h,y=t.verticalAnchor,v=y===void 0?"end":y,p=t.fill,x=p===void 0?Gv:p,O=zv(t,e6),w=g.useMemo(function(){return c6({breakAll:O.breakAll,children:O.children,maxLines:O.maxLines,scaleToFit:l,style:O.style,width:O.width})},[O.breakAll,O.children,O.maxLines,l,O.style,O.width]),A=O.dx,m=O.dy,b=O.angle,_=O.className,S=O.breakAll,j=zv(O,t6);if(!Ae(n)||!Ae(i))return null;var C=n+(B(A)?A:0),E=i+(B(m)?m:0),M;switch(v){case"start":M=Ms("calc(".concat(s,")"));break;case"middle":M=Ms("calc(".concat((w.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:M=Ms("calc(".concat(w.length-1," * -").concat(u,")"));break}var $=[];if(l){var k=w[0].width,I=O.width;$.push("scale(".concat((B(I)?I/k:1)/k,")"))}return b&&$.push("rotate(".concat(b,", ").concat(C,", ").concat(E,")")),$.length&&(j.transform=$.join(" ")),P.createElement("text",zl({},K(j,!0),{x:C,y:E,className:Q("recharts-text",_),textAnchor:d,fill:x.includes("url")?Gv:x}),w.map(function(D,N){var q=D.words.join(S?"":" ");return P.createElement("tspan",{x:C,dy:N===0?M:u,key:"".concat(q,"-").concat(N)},q)}))};function Lt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function s6(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function bh(e){let t,r,n;e.length!==2?(t=Lt,r=(u,c)=>Lt(e(u),c),n=(u,c)=>e(u)-c):(t=e===Lt||e===s6?e:l6,r=e,n=e);function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=a(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:a,center:o,right:i}}function l6(){return 0}function hb(e){return e===null?NaN:+e}function*f6(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const h6=bh(Lt),xa=h6.right;bh(hb).center;class Kv extends Map{constructor(t,r=v6){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,a]of t)this.set(n,a)}get(t){return super.get(Xv(this,t))}has(t){return super.has(Xv(this,t))}set(t,r){return super.set(d6(this,t),r)}delete(t){return super.delete(p6(this,t))}}function Xv({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function d6({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function p6({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function v6(e){return e!==null&&typeof e=="object"?e.valueOf():e}function y6(e=Lt){if(e===Lt)return db;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function db(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const m6=Math.sqrt(50),g6=Math.sqrt(10),b6=Math.sqrt(2);function Ya(e,t,r){const n=(t-e)/Math.max(0,r),a=Math.floor(Math.log10(n)),i=n/Math.pow(10,a),o=i>=m6?10:i>=g6?5:i>=b6?2:1;let u,c,s;return a<0?(s=Math.pow(10,-a)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,a)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?Ya(e,t,r*2):[u,c,s]}function Zl(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[a,i,o]=n?Ya(t,e,r):Ya(e,t,r);if(!(i>=a))return[];const u=i-a+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(i-s)/-o;else for(let s=0;s<u;++s)c[s]=(i-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(a+s)/-o;else for(let s=0;s<u;++s)c[s]=(a+s)*o;return c}function Wl(e,t,r){return t=+t,e=+e,r=+r,Ya(e,t,r)[2]}function Ul(e,t,r){t=+t,e=+e,r=+r;const n=t<e,a=n?Wl(t,e,r):Wl(e,t,r);return(n?-1:1)*(a<0?1/-a:a)}function Yv(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function Jv(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function pb(e,t,r=0,n=1/0,a){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(a=a===void 0?db:y6(a);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),h=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),d=Math.max(r,Math.floor(t-s*l/c+h)),y=Math.min(n,Math.floor(t+(c-s)*l/c+h));pb(e,t,d,y,a)}const i=e[t];let o=r,u=n;for(dn(e,r,t),a(e[n],i)>0&&dn(e,r,n);o<u;){for(dn(e,o,u),++o,--u;a(e[o],i)<0;)++o;for(;a(e[u],i)>0;)--u}a(e[r],i)===0?dn(e,r,u):(++u,dn(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function dn(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function x6(e,t,r){if(e=Float64Array.from(f6(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return Jv(e);if(t>=1)return Yv(e);var n,a=(n-1)*t,i=Math.floor(a),o=Yv(pb(e,i).subarray(0,i+1)),u=Jv(e.subarray(i+1));return o+(u-o)*(a-i)}}function w6(e,t,r=hb){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,a=(n-1)*t,i=Math.floor(a),o=+r(e[i],i,e),u=+r(e[i+1],i+1,e);return o+(u-o)*(a-i)}}function O6(e,t,r){e=+e,t=+t,r=(a=arguments.length)<2?(t=e,e=0,1):a<3?1:+r;for(var n=-1,a=Math.max(0,Math.ceil((t-e)/r))|0,i=new Array(a);++n<a;)i[n]=e+n*r;return i}function tt(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function $t(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const Gl=Symbol("implicit");function xh(){var e=new Kv,t=[],r=[],n=Gl;function a(i){let o=e.get(i);if(o===void 0){if(n!==Gl)return n;e.set(i,o=t.push(i)-1)}return r[o%r.length]}return a.domain=function(i){if(!arguments.length)return t.slice();t=[],e=new Kv;for(const o of i)e.has(o)||e.set(o,t.push(o)-1);return a},a.range=function(i){return arguments.length?(r=Array.from(i),a):r.slice()},a.unknown=function(i){return arguments.length?(n=i,a):n},a.copy=function(){return xh(t,r).unknown(n)},tt.apply(a,arguments),a}function Nn(){var e=xh().unknown(void 0),t=e.domain,r=e.range,n=0,a=1,i,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var h=t().length,d=a<n,y=d?a:n,v=d?n:a;i=(v-y)/Math.max(1,h-c+s*2),u&&(i=Math.floor(i)),y+=(v-y-i*(h-c))*f,o=i*(1-c),u&&(y=Math.round(y),o=Math.round(o));var p=O6(h).map(function(x){return y+i*x});return r(d?p.reverse():p)}return e.domain=function(h){return arguments.length?(t(h),l()):t()},e.range=function(h){return arguments.length?([n,a]=h,n=+n,a=+a,l()):[n,a]},e.rangeRound=function(h){return[n,a]=h,n=+n,a=+a,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return i},e.round=function(h){return arguments.length?(u=!!h,l()):u},e.padding=function(h){return arguments.length?(c=Math.min(1,s=+h),l()):c},e.paddingInner=function(h){return arguments.length?(c=Math.min(1,h),l()):c},e.paddingOuter=function(h){return arguments.length?(s=+h,l()):s},e.align=function(h){return arguments.length?(f=Math.max(0,Math.min(1,h)),l()):f},e.copy=function(){return Nn(t(),[n,a]).round(u).paddingInner(c).paddingOuter(s).align(f)},tt.apply(l(),arguments)}function vb(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return vb(t())},e}function Sn(){return vb(Nn.apply(null,arguments).paddingInner(1))}function wh(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function yb(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function wa(){}var Ln=.7,Ja=1/Ln,Tr="\\s*([+-]?\\d+)\\s*",qn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ft="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",A6=/^#([0-9a-f]{3,8})$/,_6=new RegExp(`^rgb\\(${Tr},${Tr},${Tr}\\)$`),S6=new RegExp(`^rgb\\(${ft},${ft},${ft}\\)$`),P6=new RegExp(`^rgba\\(${Tr},${Tr},${Tr},${qn}\\)$`),E6=new RegExp(`^rgba\\(${ft},${ft},${ft},${qn}\\)$`),j6=new RegExp(`^hsl\\(${qn},${ft},${ft}\\)$`),T6=new RegExp(`^hsla\\(${qn},${ft},${ft},${qn}\\)$`),Qv={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};wh(wa,Bn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:ey,formatHex:ey,formatHex8:M6,formatHsl:$6,formatRgb:ty,toString:ty});function ey(){return this.rgb().formatHex()}function M6(){return this.rgb().formatHex8()}function $6(){return mb(this).formatHsl()}function ty(){return this.rgb().formatRgb()}function Bn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=A6.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?ry(t):r===3?new Le(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?$a(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?$a(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=_6.exec(e))?new Le(t[1],t[2],t[3],1):(t=S6.exec(e))?new Le(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=P6.exec(e))?$a(t[1],t[2],t[3],t[4]):(t=E6.exec(e))?$a(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=j6.exec(e))?iy(t[1],t[2]/100,t[3]/100,1):(t=T6.exec(e))?iy(t[1],t[2]/100,t[3]/100,t[4]):Qv.hasOwnProperty(e)?ry(Qv[e]):e==="transparent"?new Le(NaN,NaN,NaN,0):null}function ry(e){return new Le(e>>16&255,e>>8&255,e&255,1)}function $a(e,t,r,n){return n<=0&&(e=t=r=NaN),new Le(e,t,r,n)}function C6(e){return e instanceof wa||(e=Bn(e)),e?(e=e.rgb(),new Le(e.r,e.g,e.b,e.opacity)):new Le}function Kl(e,t,r,n){return arguments.length===1?C6(e):new Le(e,t,r,n??1)}function Le(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}wh(Le,Kl,yb(wa,{brighter(e){return e=e==null?Ja:Math.pow(Ja,e),new Le(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Ln:Math.pow(Ln,e),new Le(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Le(ir(this.r),ir(this.g),ir(this.b),Qa(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ny,formatHex:ny,formatHex8:I6,formatRgb:ay,toString:ay}));function ny(){return`#${tr(this.r)}${tr(this.g)}${tr(this.b)}`}function I6(){return`#${tr(this.r)}${tr(this.g)}${tr(this.b)}${tr((isNaN(this.opacity)?1:this.opacity)*255)}`}function ay(){const e=Qa(this.opacity);return`${e===1?"rgb(":"rgba("}${ir(this.r)}, ${ir(this.g)}, ${ir(this.b)}${e===1?")":`, ${e})`}`}function Qa(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function ir(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function tr(e){return e=ir(e),(e<16?"0":"")+e.toString(16)}function iy(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new at(e,t,r,n)}function mb(e){if(e instanceof at)return new at(e.h,e.s,e.l,e.opacity);if(e instanceof wa||(e=Bn(e)),!e)return new at;if(e instanceof at)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,a=Math.min(t,r,n),i=Math.max(t,r,n),o=NaN,u=i-a,c=(i+a)/2;return u?(t===i?o=(r-n)/u+(r<n)*6:r===i?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?i+a:2-i-a,o*=60):u=c>0&&c<1?0:o,new at(o,u,c,e.opacity)}function k6(e,t,r,n){return arguments.length===1?mb(e):new at(e,t,r,n??1)}function at(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}wh(at,k6,yb(wa,{brighter(e){return e=e==null?Ja:Math.pow(Ja,e),new at(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Ln:Math.pow(Ln,e),new at(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,a=2*r-n;return new Le($s(e>=240?e-240:e+120,a,n),$s(e,a,n),$s(e<120?e+240:e-120,a,n),this.opacity)},clamp(){return new at(oy(this.h),Ca(this.s),Ca(this.l),Qa(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Qa(this.opacity);return`${e===1?"hsl(":"hsla("}${oy(this.h)}, ${Ca(this.s)*100}%, ${Ca(this.l)*100}%${e===1?")":`, ${e})`}`}}));function oy(e){return e=(e||0)%360,e<0?e+360:e}function Ca(e){return Math.max(0,Math.min(1,e||0))}function $s(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const Oh=e=>()=>e;function R6(e,t){return function(r){return e+r*t}}function D6(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function N6(e){return(e=+e)==1?gb:function(t,r){return r-t?D6(t,r,e):Oh(isNaN(t)?r:t)}}function gb(e,t){var r=t-e;return r?R6(e,r):Oh(isNaN(e)?t:e)}const uy=(function e(t){var r=N6(t);function n(a,i){var o=r((a=Kl(a)).r,(i=Kl(i)).r),u=r(a.g,i.g),c=r(a.b,i.b),s=gb(a.opacity,i.opacity);return function(f){return a.r=o(f),a.g=u(f),a.b=c(f),a.opacity=s(f),a+""}}return n.gamma=e,n})(1);function L6(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),a;return function(i){for(a=0;a<r;++a)n[a]=e[a]*(1-i)+t[a]*i;return n}}function q6(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function B6(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,a=new Array(n),i=new Array(r),o;for(o=0;o<n;++o)a[o]=cn(e[o],t[o]);for(;o<r;++o)i[o]=t[o];return function(u){for(o=0;o<n;++o)i[o]=a[o](u);return i}}function H6(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function ei(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function F6(e,t){var r={},n={},a;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(a in t)a in e?r[a]=cn(e[a],t[a]):n[a]=t[a];return function(i){for(a in r)n[a]=r[a](i);return n}}var Xl=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Cs=new RegExp(Xl.source,"g");function V6(e){return function(){return e}}function z6(e){return function(t){return e(t)+""}}function Z6(e,t){var r=Xl.lastIndex=Cs.lastIndex=0,n,a,i,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=Xl.exec(e))&&(a=Cs.exec(t));)(i=a.index)>r&&(i=t.slice(r,i),u[o]?u[o]+=i:u[++o]=i),(n=n[0])===(a=a[0])?u[o]?u[o]+=a:u[++o]=a:(u[++o]=null,c.push({i:o,x:ei(n,a)})),r=Cs.lastIndex;return r<t.length&&(i=t.slice(r),u[o]?u[o]+=i:u[++o]=i),u.length<2?c[0]?z6(c[0].x):V6(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function cn(e,t){var r=typeof t,n;return t==null||r==="boolean"?Oh(t):(r==="number"?ei:r==="string"?(n=Bn(t))?(t=n,uy):Z6:t instanceof Bn?uy:t instanceof Date?H6:q6(t)?L6:Array.isArray(t)?B6:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?F6:ei)(e,t)}function Ah(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function W6(e,t){t===void 0&&(t=e,e=cn);for(var r=0,n=t.length-1,a=t[0],i=new Array(n<0?0:n);r<n;)i[r]=e(a,a=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return i[u](o-u)}}function U6(e){return function(){return e}}function ti(e){return+e}var cy=[0,1];function ke(e){return e}function Yl(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:U6(isNaN(t)?NaN:.5)}function G6(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function K6(e,t,r){var n=e[0],a=e[1],i=t[0],o=t[1];return a<n?(n=Yl(a,n),i=r(o,i)):(n=Yl(n,a),i=r(i,o)),function(u){return i(n(u))}}function X6(e,t,r){var n=Math.min(e.length,t.length)-1,a=new Array(n),i=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)a[o]=Yl(e[o],e[o+1]),i[o]=r(t[o],t[o+1]);return function(u){var c=xa(e,u,1,n)-1;return i[c](a[c](u))}}function Oa(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Xi(){var e=cy,t=cy,r=cn,n,a,i,o=ke,u,c,s;function f(){var h=Math.min(e.length,t.length);return o!==ke&&(o=G6(e[0],e[h-1])),u=h>2?X6:K6,c=s=null,l}function l(h){return h==null||isNaN(h=+h)?i:(c||(c=u(e.map(n),t,r)))(n(o(h)))}return l.invert=function(h){return o(a((s||(s=u(t,e.map(n),ei)))(h)))},l.domain=function(h){return arguments.length?(e=Array.from(h,ti),f()):e.slice()},l.range=function(h){return arguments.length?(t=Array.from(h),f()):t.slice()},l.rangeRound=function(h){return t=Array.from(h),r=Ah,f()},l.clamp=function(h){return arguments.length?(o=h?!0:ke,f()):o!==ke},l.interpolate=function(h){return arguments.length?(r=h,f()):r},l.unknown=function(h){return arguments.length?(i=h,l):i},function(h,d){return n=h,a=d,f()}}function _h(){return Xi()(ke,ke)}function Y6(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function ri(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Nr(e){return e=ri(Math.abs(e)),e?e[1]:NaN}function J6(e,t){return function(r,n){for(var a=r.length,i=[],o=0,u=e[0],c=0;a>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),i.push(r.substring(a-=u,a+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return i.reverse().join(t)}}function Q6(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var eS=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Hn(e){if(!(t=eS.exec(e)))throw new Error("invalid format: "+e);var t;return new Sh({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}Hn.prototype=Sh.prototype;function Sh(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}Sh.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function tS(e){e:for(var t=e.length,r=1,n=-1,a;r<t;++r)switch(e[r]){case".":n=a=r;break;case"0":n===0&&(n=r),a=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(a+1):e}var bb;function rS(e,t){var r=ri(e,t);if(!r)return e+"";var n=r[0],a=r[1],i=a-(bb=Math.max(-8,Math.min(8,Math.floor(a/3)))*3)+1,o=n.length;return i===o?n:i>o?n+new Array(i-o+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+new Array(1-i).join("0")+ri(e,Math.max(0,t+i-1))[0]}function sy(e,t){var r=ri(e,t);if(!r)return e+"";var n=r[0],a=r[1];return a<0?"0."+new Array(-a).join("0")+n:n.length>a+1?n.slice(0,a+1)+"."+n.slice(a+1):n+new Array(a-n.length+2).join("0")}const ly={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:Y6,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>sy(e*100,t),r:sy,s:rS,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function fy(e){return e}var hy=Array.prototype.map,dy=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function nS(e){var t=e.grouping===void 0||e.thousands===void 0?fy:J6(hy.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",a=e.decimal===void 0?".":e.decimal+"",i=e.numerals===void 0?fy:Q6(hy.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=Hn(l);var h=l.fill,d=l.align,y=l.sign,v=l.symbol,p=l.zero,x=l.width,O=l.comma,w=l.precision,A=l.trim,m=l.type;m==="n"?(O=!0,m="g"):ly[m]||(w===void 0&&(w=12),A=!0,m="g"),(p||h==="0"&&d==="=")&&(p=!0,h="0",d="=");var b=v==="$"?r:v==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",_=v==="$"?n:/[%p]/.test(m)?o:"",S=ly[m],j=/[defgprs%]/.test(m);w=w===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function C(E){var M=b,$=_,k,I,D;if(m==="c")$=S(E)+$,E="";else{E=+E;var N=E<0||1/E<0;if(E=isNaN(E)?c:S(Math.abs(E),w),A&&(E=tS(E)),N&&+E==0&&y!=="+"&&(N=!1),M=(N?y==="("?y:u:y==="-"||y==="("?"":y)+M,$=(m==="s"?dy[8+bb/3]:"")+$+(N&&y==="("?")":""),j){for(k=-1,I=E.length;++k<I;)if(D=E.charCodeAt(k),48>D||D>57){$=(D===46?a+E.slice(k+1):E.slice(k))+$,E=E.slice(0,k);break}}}O&&!p&&(E=t(E,1/0));var q=M.length+E.length+$.length,H=q<x?new Array(x-q+1).join(h):"";switch(O&&p&&(E=t(H+E,H.length?x-$.length:1/0),H=""),d){case"<":E=M+E+$+H;break;case"=":E=M+H+E+$;break;case"^":E=H.slice(0,q=H.length>>1)+M+E+$+H.slice(q);break;default:E=H+M+E+$;break}return i(E)}return C.toString=function(){return l+""},C}function f(l,h){var d=s((l=Hn(l),l.type="f",l)),y=Math.max(-8,Math.min(8,Math.floor(Nr(h)/3)))*3,v=Math.pow(10,-y),p=dy[8+y/3];return function(x){return d(v*x)+p}}return{format:s,formatPrefix:f}}var Ia,Ph,xb;aS({thousands:",",grouping:[3],currency:["$",""]});function aS(e){return Ia=nS(e),Ph=Ia.format,xb=Ia.formatPrefix,Ia}function iS(e){return Math.max(0,-Nr(Math.abs(e)))}function oS(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(Nr(t)/3)))*3-Nr(Math.abs(e)))}function uS(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Nr(t)-Nr(e))+1}function wb(e,t,r,n){var a=Ul(e,t,r),i;switch(n=Hn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(i=oS(a,o))&&(n.precision=i),xb(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(i=uS(a,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=i-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(i=iS(a))&&(n.precision=i-(n.type==="%")*2);break}}return Ph(n)}function Ft(e){var t=e.domain;return e.ticks=function(r){var n=t();return Zl(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var a=t();return wb(a[0],a[a.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),a=0,i=n.length-1,o=n[a],u=n[i],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=a,a=i,i=s);f-- >0;){if(s=Wl(o,u,r),s===c)return n[a]=o,n[i]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function ni(){var e=_h();return e.copy=function(){return Oa(e,ni())},tt.apply(e,arguments),Ft(e)}function Ob(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,ti),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return Ob(e).unknown(t)},e=arguments.length?Array.from(e,ti):[0,1],Ft(r)}function Ab(e,t){e=e.slice();var r=0,n=e.length-1,a=e[r],i=e[n],o;return i<a&&(o=r,r=n,n=o,o=a,a=i,i=o),e[r]=t.floor(a),e[n]=t.ceil(i),e}function py(e){return Math.log(e)}function vy(e){return Math.exp(e)}function cS(e){return-Math.log(-e)}function sS(e){return-Math.exp(-e)}function lS(e){return isFinite(e)?+("1e"+e):e<0?0:e}function fS(e){return e===10?lS:e===Math.E?Math.exp:t=>Math.pow(e,t)}function hS(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function yy(e){return(t,r)=>-e(-t,r)}function Eh(e){const t=e(py,vy),r=t.domain;let n=10,a,i;function o(){return a=hS(n),i=fS(n),r()[0]<0?(a=yy(a),i=yy(i),e(cS,sS)):e(py,vy),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let h=a(s),d=a(f),y,v;const p=u==null?10:+u;let x=[];if(!(n%1)&&d-h<p){if(h=Math.floor(h),d=Math.ceil(d),s>0){for(;h<=d;++h)for(y=1;y<n;++y)if(v=h<0?y/i(-h):y*i(h),!(v<s)){if(v>f)break;x.push(v)}}else for(;h<=d;++h)for(y=n-1;y>=1;--y)if(v=h>0?y/i(-h):y*i(h),!(v<s)){if(v>f)break;x.push(v)}x.length*2<p&&(x=Zl(s,f,p))}else x=Zl(h,d,Math.min(d-h,p)).map(i);return l?x.reverse():x},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=Hn(c)).precision==null&&(c.trim=!0),c=Ph(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/i(Math.round(a(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(Ab(r(),{floor:u=>i(Math.floor(a(u))),ceil:u=>i(Math.ceil(a(u)))})),t}function _b(){const e=Eh(Xi()).domain([1,10]);return e.copy=()=>Oa(e,_b()).base(e.base()),tt.apply(e,arguments),e}function my(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function gy(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function jh(e){var t=1,r=e(my(t),gy(t));return r.constant=function(n){return arguments.length?e(my(t=+n),gy(t)):t},Ft(r)}function Sb(){var e=jh(Xi());return e.copy=function(){return Oa(e,Sb()).constant(e.constant())},tt.apply(e,arguments)}function by(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function dS(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function pS(e){return e<0?-e*e:e*e}function Th(e){var t=e(ke,ke),r=1;function n(){return r===1?e(ke,ke):r===.5?e(dS,pS):e(by(r),by(1/r))}return t.exponent=function(a){return arguments.length?(r=+a,n()):r},Ft(t)}function Mh(){var e=Th(Xi());return e.copy=function(){return Oa(e,Mh()).exponent(e.exponent())},tt.apply(e,arguments),e}function vS(){return Mh.apply(null,arguments).exponent(.5)}function xy(e){return Math.sign(e)*e*e}function yS(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function Pb(){var e=_h(),t=[0,1],r=!1,n;function a(i){var o=yS(e(i));return isNaN(o)?n:r?Math.round(o):o}return a.invert=function(i){return e.invert(xy(i))},a.domain=function(i){return arguments.length?(e.domain(i),a):e.domain()},a.range=function(i){return arguments.length?(e.range((t=Array.from(i,ti)).map(xy)),a):t.slice()},a.rangeRound=function(i){return a.range(i).round(!0)},a.round=function(i){return arguments.length?(r=!!i,a):r},a.clamp=function(i){return arguments.length?(e.clamp(i),a):e.clamp()},a.unknown=function(i){return arguments.length?(n=i,a):n},a.copy=function(){return Pb(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},tt.apply(a,arguments),Ft(a)}function Eb(){var e=[],t=[],r=[],n;function a(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=w6(e,o/u);return i}function i(o){return o==null||isNaN(o=+o)?n:t[xa(r,o)]}return i.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},i.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Lt),a()},i.range=function(o){return arguments.length?(t=Array.from(o),a()):t.slice()},i.unknown=function(o){return arguments.length?(n=o,i):n},i.quantiles=function(){return r.slice()},i.copy=function(){return Eb().domain(e).range(t).unknown(n)},tt.apply(i,arguments)}function jb(){var e=0,t=1,r=1,n=[.5],a=[0,1],i;function o(c){return c!=null&&c<=c?a[xa(n,c,0,r)]:i}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(a=Array.from(c)).length-1,u()):a.slice()},o.invertExtent=function(c){var s=a.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(i=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return jb().domain([e,t]).range(a).unknown(i)},tt.apply(Ft(o),arguments)}function Tb(){var e=[.5],t=[0,1],r,n=1;function a(i){return i!=null&&i<=i?t[xa(e,i,0,n)]:r}return a.domain=function(i){return arguments.length?(e=Array.from(i),n=Math.min(e.length,t.length-1),a):e.slice()},a.range=function(i){return arguments.length?(t=Array.from(i),n=Math.min(e.length,t.length-1),a):t.slice()},a.invertExtent=function(i){var o=t.indexOf(i);return[e[o-1],e[o]]},a.unknown=function(i){return arguments.length?(r=i,a):r},a.copy=function(){return Tb().domain(e).range(t).unknown(r)},tt.apply(a,arguments)}const Is=new Date,ks=new Date;function _e(e,t,r,n){function a(i){return e(i=arguments.length===0?new Date:new Date(+i)),i}return a.floor=i=>(e(i=new Date(+i)),i),a.ceil=i=>(e(i=new Date(i-1)),t(i,1),e(i),i),a.round=i=>{const o=a(i),u=a.ceil(i);return i-o<u-i?o:u},a.offset=(i,o)=>(t(i=new Date(+i),o==null?1:Math.floor(o)),i),a.range=(i,o,u)=>{const c=[];if(i=a.ceil(i),u=u==null?1:Math.floor(u),!(i<o)||!(u>0))return c;let s;do c.push(s=new Date(+i)),t(i,u),e(i);while(s<i&&i<o);return c},a.filter=i=>_e(o=>{if(o>=o)for(;e(o),!i(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!i(o););else for(;--u>=0;)for(;t(o,1),!i(o););}),r&&(a.count=(i,o)=>(Is.setTime(+i),ks.setTime(+o),e(Is),e(ks),Math.floor(r(Is,ks))),a.every=i=>(i=Math.floor(i),!isFinite(i)||!(i>0)?null:i>1?a.filter(n?o=>n(o)%i===0:o=>a.count(0,o)%i===0):a)),a}const ai=_e(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);ai.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?_e(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):ai);ai.range;const bt=1e3,Ye=bt*60,xt=Ye*60,St=xt*24,$h=St*7,wy=St*30,Rs=St*365,rr=_e(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*bt)},(e,t)=>(t-e)/bt,e=>e.getUTCSeconds());rr.range;const Ch=_e(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*bt)},(e,t)=>{e.setTime(+e+t*Ye)},(e,t)=>(t-e)/Ye,e=>e.getMinutes());Ch.range;const Ih=_e(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Ye)},(e,t)=>(t-e)/Ye,e=>e.getUTCMinutes());Ih.range;const kh=_e(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*bt-e.getMinutes()*Ye)},(e,t)=>{e.setTime(+e+t*xt)},(e,t)=>(t-e)/xt,e=>e.getHours());kh.range;const Rh=_e(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*xt)},(e,t)=>(t-e)/xt,e=>e.getUTCHours());Rh.range;const Aa=_e(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Ye)/St,e=>e.getDate()-1);Aa.range;const Yi=_e(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/St,e=>e.getUTCDate()-1);Yi.range;const Mb=_e(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/St,e=>Math.floor(e/St));Mb.range;function vr(e){return _e(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Ye)/$h)}const Ji=vr(0),ii=vr(1),mS=vr(2),gS=vr(3),Lr=vr(4),bS=vr(5),xS=vr(6);Ji.range;ii.range;mS.range;gS.range;Lr.range;bS.range;xS.range;function yr(e){return _e(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/$h)}const Qi=yr(0),oi=yr(1),wS=yr(2),OS=yr(3),qr=yr(4),AS=yr(5),_S=yr(6);Qi.range;oi.range;wS.range;OS.range;qr.range;AS.range;_S.range;const Dh=_e(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Dh.range;const Nh=_e(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Nh.range;const Pt=_e(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());Pt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:_e(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});Pt.range;const Et=_e(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());Et.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:_e(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});Et.range;function $b(e,t,r,n,a,i){const o=[[rr,1,bt],[rr,5,5*bt],[rr,15,15*bt],[rr,30,30*bt],[i,1,Ye],[i,5,5*Ye],[i,15,15*Ye],[i,30,30*Ye],[a,1,xt],[a,3,3*xt],[a,6,6*xt],[a,12,12*xt],[n,1,St],[n,2,2*St],[r,1,$h],[t,1,wy],[t,3,3*wy],[e,1,Rs]];function u(s,f,l){const h=f<s;h&&([s,f]=[f,s]);const d=l&&typeof l.range=="function"?l:c(s,f,l),y=d?d.range(s,+f+1):[];return h?y.reverse():y}function c(s,f,l){const h=Math.abs(f-s)/l,d=bh(([,,p])=>p).right(o,h);if(d===o.length)return e.every(Ul(s/Rs,f/Rs,l));if(d===0)return ai.every(Math.max(Ul(s,f,l),1));const[y,v]=o[h/o[d-1][2]<o[d][2]/h?d-1:d];return y.every(v)}return[u,c]}const[SS,PS]=$b(Et,Nh,Qi,Mb,Rh,Ih),[ES,jS]=$b(Pt,Dh,Ji,Aa,kh,Ch);function Ds(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Ns(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function pn(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function TS(e){var t=e.dateTime,r=e.date,n=e.time,a=e.periods,i=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=vn(a),f=yn(a),l=vn(i),h=yn(i),d=vn(o),y=yn(o),v=vn(u),p=yn(u),x=vn(c),O=yn(c),w={a:N,A:q,b:H,B:Z,c:null,d:Ey,e:Ey,f:JS,g:c4,G:l4,H:KS,I:XS,j:YS,L:Cb,m:QS,M:e4,p:U,q:V,Q:My,s:$y,S:t4,u:r4,U:n4,V:a4,w:i4,W:o4,x:null,X:null,y:u4,Y:s4,Z:f4,"%":Ty},A={a:G,A:fe,b:me,B:Be,c:null,d:jy,e:jy,f:v4,g:S4,G:E4,H:h4,I:d4,j:p4,L:kb,m:y4,M:m4,p:Zt,q:Re,Q:My,s:$y,S:g4,u:b4,U:x4,V:w4,w:O4,W:A4,x:null,X:null,y:_4,Y:P4,Z:j4,"%":Ty},m={a:C,A:E,b:M,B:$,c:k,d:Sy,e:Sy,f:ZS,g:_y,G:Ay,H:Py,I:Py,j:HS,L:zS,m:BS,M:FS,p:j,q:qS,Q:US,s:GS,S:VS,u:kS,U:RS,V:DS,w:IS,W:NS,x:I,X:D,y:_y,Y:Ay,Z:LS,"%":WS};w.x=b(r,w),w.X=b(n,w),w.c=b(t,w),A.x=b(r,A),A.X=b(n,A),A.c=b(t,A);function b(F,Y){return function(J){var L=[],ve=-1,ee=0,xe=F.length,we,De,Ct;for(J instanceof Date||(J=new Date(+J));++ve<xe;)F.charCodeAt(ve)===37&&(L.push(F.slice(ee,ve)),(De=Oy[we=F.charAt(++ve)])!=null?we=F.charAt(++ve):De=we==="e"?" ":"0",(Ct=Y[we])&&(we=Ct(J,De)),L.push(we),ee=ve+1);return L.push(F.slice(ee,ve)),L.join("")}}function _(F,Y){return function(J){var L=pn(1900,void 0,1),ve=S(L,F,J+="",0),ee,xe;if(ve!=J.length)return null;if("Q"in L)return new Date(L.Q);if("s"in L)return new Date(L.s*1e3+("L"in L?L.L:0));if(Y&&!("Z"in L)&&(L.Z=0),"p"in L&&(L.H=L.H%12+L.p*12),L.m===void 0&&(L.m="q"in L?L.q:0),"V"in L){if(L.V<1||L.V>53)return null;"w"in L||(L.w=1),"Z"in L?(ee=Ns(pn(L.y,0,1)),xe=ee.getUTCDay(),ee=xe>4||xe===0?oi.ceil(ee):oi(ee),ee=Yi.offset(ee,(L.V-1)*7),L.y=ee.getUTCFullYear(),L.m=ee.getUTCMonth(),L.d=ee.getUTCDate()+(L.w+6)%7):(ee=Ds(pn(L.y,0,1)),xe=ee.getDay(),ee=xe>4||xe===0?ii.ceil(ee):ii(ee),ee=Aa.offset(ee,(L.V-1)*7),L.y=ee.getFullYear(),L.m=ee.getMonth(),L.d=ee.getDate()+(L.w+6)%7)}else("W"in L||"U"in L)&&("w"in L||(L.w="u"in L?L.u%7:"W"in L?1:0),xe="Z"in L?Ns(pn(L.y,0,1)).getUTCDay():Ds(pn(L.y,0,1)).getDay(),L.m=0,L.d="W"in L?(L.w+6)%7+L.W*7-(xe+5)%7:L.w+L.U*7-(xe+6)%7);return"Z"in L?(L.H+=L.Z/100|0,L.M+=L.Z%100,Ns(L)):Ds(L)}}function S(F,Y,J,L){for(var ve=0,ee=Y.length,xe=J.length,we,De;ve<ee;){if(L>=xe)return-1;if(we=Y.charCodeAt(ve++),we===37){if(we=Y.charAt(ve++),De=m[we in Oy?Y.charAt(ve++):we],!De||(L=De(F,J,L))<0)return-1}else if(we!=J.charCodeAt(L++))return-1}return L}function j(F,Y,J){var L=s.exec(Y.slice(J));return L?(F.p=f.get(L[0].toLowerCase()),J+L[0].length):-1}function C(F,Y,J){var L=d.exec(Y.slice(J));return L?(F.w=y.get(L[0].toLowerCase()),J+L[0].length):-1}function E(F,Y,J){var L=l.exec(Y.slice(J));return L?(F.w=h.get(L[0].toLowerCase()),J+L[0].length):-1}function M(F,Y,J){var L=x.exec(Y.slice(J));return L?(F.m=O.get(L[0].toLowerCase()),J+L[0].length):-1}function $(F,Y,J){var L=v.exec(Y.slice(J));return L?(F.m=p.get(L[0].toLowerCase()),J+L[0].length):-1}function k(F,Y,J){return S(F,t,Y,J)}function I(F,Y,J){return S(F,r,Y,J)}function D(F,Y,J){return S(F,n,Y,J)}function N(F){return o[F.getDay()]}function q(F){return i[F.getDay()]}function H(F){return c[F.getMonth()]}function Z(F){return u[F.getMonth()]}function U(F){return a[+(F.getHours()>=12)]}function V(F){return 1+~~(F.getMonth()/3)}function G(F){return o[F.getUTCDay()]}function fe(F){return i[F.getUTCDay()]}function me(F){return c[F.getUTCMonth()]}function Be(F){return u[F.getUTCMonth()]}function Zt(F){return a[+(F.getUTCHours()>=12)]}function Re(F){return 1+~~(F.getUTCMonth()/3)}return{format:function(F){var Y=b(F+="",w);return Y.toString=function(){return F},Y},parse:function(F){var Y=_(F+="",!1);return Y.toString=function(){return F},Y},utcFormat:function(F){var Y=b(F+="",A);return Y.toString=function(){return F},Y},utcParse:function(F){var Y=_(F+="",!0);return Y.toString=function(){return F},Y}}}var Oy={"-":"",_:" ",0:"0"},Ee=/^\s*\d+/,MS=/^%/,$S=/[\\^$*+?|[\]().{}]/g;function te(e,t,r){var n=e<0?"-":"",a=(n?-e:e)+"",i=a.length;return n+(i<r?new Array(r-i+1).join(t)+a:a)}function CS(e){return e.replace($S,"\\$&")}function vn(e){return new RegExp("^(?:"+e.map(CS).join("|")+")","i")}function yn(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function IS(e,t,r){var n=Ee.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function kS(e,t,r){var n=Ee.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function RS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function DS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function NS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function Ay(e,t,r){var n=Ee.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function _y(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function LS(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function qS(e,t,r){var n=Ee.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function BS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Sy(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function HS(e,t,r){var n=Ee.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Py(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function FS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function VS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function zS(e,t,r){var n=Ee.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function ZS(e,t,r){var n=Ee.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function WS(e,t,r){var n=MS.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function US(e,t,r){var n=Ee.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function GS(e,t,r){var n=Ee.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function Ey(e,t){return te(e.getDate(),t,2)}function KS(e,t){return te(e.getHours(),t,2)}function XS(e,t){return te(e.getHours()%12||12,t,2)}function YS(e,t){return te(1+Aa.count(Pt(e),e),t,3)}function Cb(e,t){return te(e.getMilliseconds(),t,3)}function JS(e,t){return Cb(e,t)+"000"}function QS(e,t){return te(e.getMonth()+1,t,2)}function e4(e,t){return te(e.getMinutes(),t,2)}function t4(e,t){return te(e.getSeconds(),t,2)}function r4(e){var t=e.getDay();return t===0?7:t}function n4(e,t){return te(Ji.count(Pt(e)-1,e),t,2)}function Ib(e){var t=e.getDay();return t>=4||t===0?Lr(e):Lr.ceil(e)}function a4(e,t){return e=Ib(e),te(Lr.count(Pt(e),e)+(Pt(e).getDay()===4),t,2)}function i4(e){return e.getDay()}function o4(e,t){return te(ii.count(Pt(e)-1,e),t,2)}function u4(e,t){return te(e.getFullYear()%100,t,2)}function c4(e,t){return e=Ib(e),te(e.getFullYear()%100,t,2)}function s4(e,t){return te(e.getFullYear()%1e4,t,4)}function l4(e,t){var r=e.getDay();return e=r>=4||r===0?Lr(e):Lr.ceil(e),te(e.getFullYear()%1e4,t,4)}function f4(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+te(t/60|0,"0",2)+te(t%60,"0",2)}function jy(e,t){return te(e.getUTCDate(),t,2)}function h4(e,t){return te(e.getUTCHours(),t,2)}function d4(e,t){return te(e.getUTCHours()%12||12,t,2)}function p4(e,t){return te(1+Yi.count(Et(e),e),t,3)}function kb(e,t){return te(e.getUTCMilliseconds(),t,3)}function v4(e,t){return kb(e,t)+"000"}function y4(e,t){return te(e.getUTCMonth()+1,t,2)}function m4(e,t){return te(e.getUTCMinutes(),t,2)}function g4(e,t){return te(e.getUTCSeconds(),t,2)}function b4(e){var t=e.getUTCDay();return t===0?7:t}function x4(e,t){return te(Qi.count(Et(e)-1,e),t,2)}function Rb(e){var t=e.getUTCDay();return t>=4||t===0?qr(e):qr.ceil(e)}function w4(e,t){return e=Rb(e),te(qr.count(Et(e),e)+(Et(e).getUTCDay()===4),t,2)}function O4(e){return e.getUTCDay()}function A4(e,t){return te(oi.count(Et(e)-1,e),t,2)}function _4(e,t){return te(e.getUTCFullYear()%100,t,2)}function S4(e,t){return e=Rb(e),te(e.getUTCFullYear()%100,t,2)}function P4(e,t){return te(e.getUTCFullYear()%1e4,t,4)}function E4(e,t){var r=e.getUTCDay();return e=r>=4||r===0?qr(e):qr.ceil(e),te(e.getUTCFullYear()%1e4,t,4)}function j4(){return"+0000"}function Ty(){return"%"}function My(e){return+e}function $y(e){return Math.floor(+e/1e3)}var wr,Db,Nb;T4({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function T4(e){return wr=TS(e),Db=wr.format,wr.parse,Nb=wr.utcFormat,wr.utcParse,wr}function M4(e){return new Date(e)}function $4(e){return e instanceof Date?+e:+new Date(+e)}function Lh(e,t,r,n,a,i,o,u,c,s){var f=_h(),l=f.invert,h=f.domain,d=s(".%L"),y=s(":%S"),v=s("%I:%M"),p=s("%I %p"),x=s("%a %d"),O=s("%b %d"),w=s("%B"),A=s("%Y");function m(b){return(c(b)<b?d:u(b)<b?y:o(b)<b?v:i(b)<b?p:n(b)<b?a(b)<b?x:O:r(b)<b?w:A)(b)}return f.invert=function(b){return new Date(l(b))},f.domain=function(b){return arguments.length?h(Array.from(b,$4)):h().map(M4)},f.ticks=function(b){var _=h();return e(_[0],_[_.length-1],b??10)},f.tickFormat=function(b,_){return _==null?m:s(_)},f.nice=function(b){var _=h();return(!b||typeof b.range!="function")&&(b=t(_[0],_[_.length-1],b??10)),b?h(Ab(_,b)):f},f.copy=function(){return Oa(f,Lh(e,t,r,n,a,i,o,u,c,s))},f}function C4(){return tt.apply(Lh(ES,jS,Pt,Dh,Ji,Aa,kh,Ch,rr,Db).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function I4(){return tt.apply(Lh(SS,PS,Et,Nh,Qi,Yi,Rh,Ih,rr,Nb).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function eo(){var e=0,t=1,r,n,a,i,o=ke,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(a===0?.5:(l=(i(l)-r)*a,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=i(e=+e),n=i(t=+t),a=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(h){var d,y;return arguments.length?([d,y]=h,o=l(d,y),s):[o(0),o(1)]}}return s.range=f(cn),s.rangeRound=f(Ah),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return i=l,r=l(e),n=l(t),a=r===n?0:1/(n-r),s}}function Vt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function Lb(){var e=Ft(eo()(ke));return e.copy=function(){return Vt(e,Lb())},$t.apply(e,arguments)}function qb(){var e=Eh(eo()).domain([1,10]);return e.copy=function(){return Vt(e,qb()).base(e.base())},$t.apply(e,arguments)}function Bb(){var e=jh(eo());return e.copy=function(){return Vt(e,Bb()).constant(e.constant())},$t.apply(e,arguments)}function qh(){var e=Th(eo());return e.copy=function(){return Vt(e,qh()).exponent(e.exponent())},$t.apply(e,arguments)}function k4(){return qh.apply(null,arguments).exponent(.5)}function Hb(){var e=[],t=ke;function r(n){if(n!=null&&!isNaN(n=+n))return t((xa(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let a of n)a!=null&&!isNaN(a=+a)&&e.push(a);return e.sort(Lt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,a)=>t(a/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(a,i)=>x6(e,i/n))},r.copy=function(){return Hb(t).domain(e)},$t.apply(r,arguments)}function to(){var e=0,t=.5,r=1,n=1,a,i,o,u,c,s=ke,f,l=!1,h;function d(v){return isNaN(v=+v)?h:(v=.5+((v=+f(v))-i)*(n*v<n*i?u:c),s(l?Math.max(0,Math.min(1,v)):v))}d.domain=function(v){return arguments.length?([e,t,r]=v,a=f(e=+e),i=f(t=+t),o=f(r=+r),u=a===i?0:.5/(i-a),c=i===o?0:.5/(o-i),n=i<a?-1:1,d):[e,t,r]},d.clamp=function(v){return arguments.length?(l=!!v,d):l},d.interpolator=function(v){return arguments.length?(s=v,d):s};function y(v){return function(p){var x,O,w;return arguments.length?([x,O,w]=p,s=W6(v,[x,O,w]),d):[s(0),s(.5),s(1)]}}return d.range=y(cn),d.rangeRound=y(Ah),d.unknown=function(v){return arguments.length?(h=v,d):h},function(v){return f=v,a=v(e),i=v(t),o=v(r),u=a===i?0:.5/(i-a),c=i===o?0:.5/(o-i),n=i<a?-1:1,d}}function Fb(){var e=Ft(to()(ke));return e.copy=function(){return Vt(e,Fb())},$t.apply(e,arguments)}function Vb(){var e=Eh(to()).domain([.1,1,10]);return e.copy=function(){return Vt(e,Vb()).base(e.base())},$t.apply(e,arguments)}function zb(){var e=jh(to());return e.copy=function(){return Vt(e,zb()).constant(e.constant())},$t.apply(e,arguments)}function Bh(){var e=Th(to());return e.copy=function(){return Vt(e,Bh()).exponent(e.exponent())},$t.apply(e,arguments)}function R4(){return Bh.apply(null,arguments).exponent(.5)}const Cy=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Nn,scaleDiverging:Fb,scaleDivergingLog:Vb,scaleDivergingPow:Bh,scaleDivergingSqrt:R4,scaleDivergingSymlog:zb,scaleIdentity:Ob,scaleImplicit:Gl,scaleLinear:ni,scaleLog:_b,scaleOrdinal:xh,scalePoint:Sn,scalePow:Mh,scaleQuantile:Eb,scaleQuantize:jb,scaleRadial:Pb,scaleSequential:Lb,scaleSequentialLog:qb,scaleSequentialPow:qh,scaleSequentialQuantile:Hb,scaleSequentialSqrt:k4,scaleSequentialSymlog:Bb,scaleSqrt:vS,scaleSymlog:Sb,scaleThreshold:Tb,scaleTime:C4,scaleUtc:I4,tickFormat:wb},Symbol.toStringTag,{value:"Module"}));var Ls,Iy;function ro(){if(Iy)return Ls;Iy=1;var e=nn();function t(r,n,a){for(var i=-1,o=r.length;++i<o;){var u=r[i],c=n(u);if(c!=null&&(s===void 0?c===c&&!e(c):a(c,s)))var s=c,f=u}return f}return Ls=t,Ls}var qs,ky;function Zb(){if(ky)return qs;ky=1;function e(t,r){return t>r}return qs=e,qs}var Bs,Ry;function D4(){if(Ry)return Bs;Ry=1;var e=ro(),t=Zb(),r=on();function n(a){return a&&a.length?e(a,r,t):void 0}return Bs=n,Bs}var N4=D4();const no=se(N4);var Hs,Dy;function Wb(){if(Dy)return Hs;Dy=1;function e(t,r){return t<r}return Hs=e,Hs}var Fs,Ny;function L4(){if(Ny)return Fs;Ny=1;var e=ro(),t=Wb(),r=on();function n(a){return a&&a.length?e(a,r,t):void 0}return Fs=n,Fs}var q4=L4();const ao=se(q4);var Vs,Ly;function B4(){if(Ly)return Vs;Ly=1;var e=th(),t=vt(),r=eb(),n=qe();function a(i,o){var u=n(i)?e:r;return u(i,t(o,3))}return Vs=a,Vs}var zs,qy;function H4(){if(qy)return zs;qy=1;var e=Jg(),t=B4();function r(n,a){return e(t(n,a),1)}return zs=r,zs}var F4=H4();const V4=se(F4);var Zs,By;function z4(){if(By)return Zs;By=1;var e=ph();function t(r,n){return e(r,n)}return Zs=t,Zs}var Z4=z4();const io=se(Z4);var sn=1e9,W4={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Fh,de=!0,et="[DecimalError] ",or=et+"Invalid argument: ",Hh=et+"Exponent out of range: ",ln=Math.floor,Jt=Math.pow,U4=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ze,Pe=1e7,he=7,Ub=9007199254740991,ui=ln(Ub/he),z={};z.absoluteValue=z.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};z.comparedTo=z.cmp=function(e){var t,r,n,a,i=this;if(e=new i.constructor(e),i.s!==e.s)return i.s||-e.s;if(i.e!==e.e)return i.e>e.e^i.s<0?1:-1;for(n=i.d.length,a=e.d.length,t=0,r=n<a?n:a;t<r;++t)if(i.d[t]!==e.d[t])return i.d[t]>e.d[t]^i.s<0?1:-1;return n===a?0:n>a^i.s<0?1:-1};z.decimalPlaces=z.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*he;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};z.dividedBy=z.div=function(e){return _t(this,new this.constructor(e))};z.dividedToIntegerBy=z.idiv=function(e){var t=this,r=t.constructor;return ce(_t(t,new r(e),0,1),r.precision)};z.equals=z.eq=function(e){return!this.cmp(e)};z.exponent=function(){return be(this)};z.greaterThan=z.gt=function(e){return this.cmp(e)>0};z.greaterThanOrEqualTo=z.gte=function(e){return this.cmp(e)>=0};z.isInteger=z.isint=function(){return this.e>this.d.length-2};z.isNegative=z.isneg=function(){return this.s<0};z.isPositive=z.ispos=function(){return this.s>0};z.isZero=function(){return this.s===0};z.lessThan=z.lt=function(e){return this.cmp(e)<0};z.lessThanOrEqualTo=z.lte=function(e){return this.cmp(e)<1};z.logarithm=z.log=function(e){var t,r=this,n=r.constructor,a=n.precision,i=a+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(ze))throw Error(et+"NaN");if(r.s<1)throw Error(et+(r.s?"NaN":"-Infinity"));return r.eq(ze)?new n(0):(de=!1,t=_t(Fn(r,i),Fn(e,i),i),de=!0,ce(t,a))};z.minus=z.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Xb(t,e):Gb(t,(e.s=-e.s,e))};z.modulo=z.mod=function(e){var t,r=this,n=r.constructor,a=n.precision;if(e=new n(e),!e.s)throw Error(et+"NaN");return r.s?(de=!1,t=_t(r,e,0,1).times(e),de=!0,r.minus(t)):ce(new n(r),a)};z.naturalExponential=z.exp=function(){return Kb(this)};z.naturalLogarithm=z.ln=function(){return Fn(this)};z.negated=z.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};z.plus=z.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Gb(t,e):Xb(t,(e.s=-e.s,e))};z.precision=z.sd=function(e){var t,r,n,a=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(or+e);if(t=be(a)+1,n=a.d.length-1,r=n*he+1,n=a.d[n],n){for(;n%10==0;n/=10)r--;for(n=a.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};z.squareRoot=z.sqrt=function(){var e,t,r,n,a,i,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(et+"NaN")}for(e=be(u),de=!1,a=Math.sqrt(+u),a==0||a==1/0?(t=lt(u.d),(t.length+e)%2==0&&(t+="0"),a=Math.sqrt(t),e=ln((e+1)/2)-(e<0||e%2),a==1/0?t="5e"+e:(t=a.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(a.toString()),r=c.precision,a=o=r+3;;)if(i=n,n=i.plus(_t(u,i,o+2)).times(.5),lt(i.d).slice(0,o)===(t=lt(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),a==o&&t=="4999"){if(ce(i,r+1,0),i.times(i).eq(u)){n=i;break}}else if(t!="9999")break;o+=4}return de=!0,ce(n,r)};z.times=z.mul=function(e){var t,r,n,a,i,o,u,c,s,f=this,l=f.constructor,h=f.d,d=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=h.length,s=d.length,c<s&&(i=h,h=d,d=i,o=c,c=s,s=o),i=[],o=c+s,n=o;n--;)i.push(0);for(n=s;--n>=0;){for(t=0,a=c+n;a>n;)u=i[a]+d[n]*h[a-n-1]+t,i[a--]=u%Pe|0,t=u/Pe|0;i[a]=(i[a]+t)%Pe|0}for(;!i[--o];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,de?ce(e,l.precision):e};z.toDecimalPlaces=z.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(dt(e,0,sn),t===void 0?t=n.rounding:dt(t,0,8),ce(r,e+be(r)+1,t))};z.toExponential=function(e,t){var r,n=this,a=n.constructor;return e===void 0?r=sr(n,!0):(dt(e,0,sn),t===void 0?t=a.rounding:dt(t,0,8),n=ce(new a(n),e+1,t),r=sr(n,!0,e+1)),r};z.toFixed=function(e,t){var r,n,a=this,i=a.constructor;return e===void 0?sr(a):(dt(e,0,sn),t===void 0?t=i.rounding:dt(t,0,8),n=ce(new i(a),e+be(a)+1,t),r=sr(n.abs(),!1,e+be(n)+1),a.isneg()&&!a.isZero()?"-"+r:r)};z.toInteger=z.toint=function(){var e=this,t=e.constructor;return ce(new t(e),be(e)+1,t.rounding)};z.toNumber=function(){return+this};z.toPower=z.pow=function(e){var t,r,n,a,i,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(ze);if(u=new c(u),!u.s){if(e.s<1)throw Error(et+"Infinity");return u}if(u.eq(ze))return u;if(n=c.precision,e.eq(ze))return ce(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,i=u.s,o){if((r=f<0?-f:f)<=Ub){for(a=new c(ze),t=Math.ceil(n/he+4),de=!1;r%2&&(a=a.times(u),Fy(a.d,t)),r=ln(r/2),r!==0;)u=u.times(u),Fy(u.d,t);return de=!0,e.s<0?new c(ze).div(a):ce(a,n)}}else if(i<0)throw Error(et+"NaN");return i=i<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,de=!1,a=e.times(Fn(u,n+s)),de=!0,a=Kb(a),a.s=i,a};z.toPrecision=function(e,t){var r,n,a=this,i=a.constructor;return e===void 0?(r=be(a),n=sr(a,r<=i.toExpNeg||r>=i.toExpPos)):(dt(e,1,sn),t===void 0?t=i.rounding:dt(t,0,8),a=ce(new i(a),e,t),r=be(a),n=sr(a,e<=r||r<=i.toExpNeg,e)),n};z.toSignificantDigits=z.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(dt(e,1,sn),t===void 0?t=n.rounding:dt(t,0,8)),ce(new n(r),e,t)};z.toString=z.valueOf=z.val=z.toJSON=z[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=be(e),r=e.constructor;return sr(e,t<=r.toExpNeg||t>=r.toExpPos)};function Gb(e,t){var r,n,a,i,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),de?ce(t,l):t;if(c=e.d,s=t.d,o=e.e,a=t.e,c=c.slice(),i=o-a,i){for(i<0?(n=c,i=-i,u=s.length):(n=s,a=o,u=c.length),o=Math.ceil(l/he),u=o>u?o+1:u+1,i>u&&(i=u,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for(u=c.length,i=s.length,u-i<0&&(i=u,n=s,s=c,c=n),r=0;i;)r=(c[--i]=c[i]+s[i]+r)/Pe|0,c[i]%=Pe;for(r&&(c.unshift(r),++a),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=a,de?ce(t,l):t}function dt(e,t,r){if(e!==~~e||e<t||e>r)throw Error(or+e)}function lt(e){var t,r,n,a=e.length-1,i="",o=e[0];if(a>0){for(i+=o,t=1;t<a;t++)n=e[t]+"",r=he-n.length,r&&(i+=It(r)),i+=n;o=e[t],n=o+"",r=he-n.length,r&&(i+=It(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return i+o}var _t=(function(){function e(n,a){var i,o=0,u=n.length;for(n=n.slice();u--;)i=n[u]*a+o,n[u]=i%Pe|0,o=i/Pe|0;return o&&n.unshift(o),n}function t(n,a,i,o){var u,c;if(i!=o)c=i>o?1:-1;else for(u=c=0;u<i;u++)if(n[u]!=a[u]){c=n[u]>a[u]?1:-1;break}return c}function r(n,a,i){for(var o=0;i--;)n[i]-=o,o=n[i]<a[i]?1:0,n[i]=o*Pe+n[i]-a[i];for(;!n[0]&&n.length>1;)n.shift()}return function(n,a,i,o){var u,c,s,f,l,h,d,y,v,p,x,O,w,A,m,b,_,S,j=n.constructor,C=n.s==a.s?1:-1,E=n.d,M=a.d;if(!n.s)return new j(n);if(!a.s)throw Error(et+"Division by zero");for(c=n.e-a.e,_=M.length,m=E.length,d=new j(C),y=d.d=[],s=0;M[s]==(E[s]||0);)++s;if(M[s]>(E[s]||0)&&--c,i==null?O=i=j.precision:o?O=i+(be(n)-be(a))+1:O=i,O<0)return new j(0);if(O=O/he+2|0,s=0,_==1)for(f=0,M=M[0],O++;(s<m||f)&&O--;s++)w=f*Pe+(E[s]||0),y[s]=w/M|0,f=w%M|0;else{for(f=Pe/(M[0]+1)|0,f>1&&(M=e(M,f),E=e(E,f),_=M.length,m=E.length),A=_,v=E.slice(0,_),p=v.length;p<_;)v[p++]=0;S=M.slice(),S.unshift(0),b=M[0],M[1]>=Pe/2&&++b;do f=0,u=t(M,v,_,p),u<0?(x=v[0],_!=p&&(x=x*Pe+(v[1]||0)),f=x/b|0,f>1?(f>=Pe&&(f=Pe-1),l=e(M,f),h=l.length,p=v.length,u=t(l,v,h,p),u==1&&(f--,r(l,_<h?S:M,h))):(f==0&&(u=f=1),l=M.slice()),h=l.length,h<p&&l.unshift(0),r(v,l,p),u==-1&&(p=v.length,u=t(M,v,_,p),u<1&&(f++,r(v,_<p?S:M,p))),p=v.length):u===0&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[p++]=E[A]||0:(v=[E[A]],p=1);while((A++<m||v[0]!==void 0)&&O--)}return y[0]||y.shift(),d.e=c,ce(d,o?i+be(d)+1:i)}})();function Kb(e,t){var r,n,a,i,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(be(e)>16)throw Error(Hh+be(e));if(!e.s)return new f(ze);for(de=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(Jt(2,s))/Math.LN10*2+5|0,u+=n,r=a=i=new f(ze),f.precision=u;;){if(a=ce(a.times(e),u),r=r.times(++c),o=i.plus(_t(a,r,u)),lt(o.d).slice(0,u)===lt(i.d).slice(0,u)){for(;s--;)i=ce(i.times(i),u);return f.precision=l,t==null?(de=!0,ce(i,l)):i}i=o}}function be(e){for(var t=e.e*he,r=e.d[0];r>=10;r/=10)t++;return t}function Ws(e,t,r){if(t>e.LN10.sd())throw de=!0,r&&(e.precision=r),Error(et+"LN10 precision limit exceeded");return ce(new e(e.LN10),t)}function It(e){for(var t="";e--;)t+="0";return t}function Fn(e,t){var r,n,a,i,o,u,c,s,f,l=1,h=10,d=e,y=d.d,v=d.constructor,p=v.precision;if(d.s<1)throw Error(et+(d.s?"NaN":"-Infinity"));if(d.eq(ze))return new v(0);if(t==null?(de=!1,s=p):s=t,d.eq(10))return t==null&&(de=!0),Ws(v,s);if(s+=h,v.precision=s,r=lt(y),n=r.charAt(0),i=be(d),Math.abs(i)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)d=d.times(e),r=lt(d.d),n=r.charAt(0),l++;i=be(d),n>1?(d=new v("0."+r),i++):d=new v(n+"."+r.slice(1))}else return c=Ws(v,s+2,p).times(i+""),d=Fn(new v(n+"."+r.slice(1)),s-h).plus(c),v.precision=p,t==null?(de=!0,ce(d,p)):d;for(u=o=d=_t(d.minus(ze),d.plus(ze),s),f=ce(d.times(d),s),a=3;;){if(o=ce(o.times(f),s),c=u.plus(_t(o,new v(a),s)),lt(c.d).slice(0,s)===lt(u.d).slice(0,s))return u=u.times(2),i!==0&&(u=u.plus(Ws(v,s+2,p).times(i+""))),u=_t(u,new v(l),s),v.precision=p,t==null?(de=!0,ce(u,p)):u;u=c,a+=2}}function Hy(e,t){var r,n,a;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(a=t.length;t.charCodeAt(a-1)===48;)--a;if(t=t.slice(n,a),t){if(a-=n,r=r-n-1,e.e=ln(r/he),e.d=[],n=(r+1)%he,r<0&&(n+=he),n<a){for(n&&e.d.push(+t.slice(0,n)),a-=he;n<a;)e.d.push(+t.slice(n,n+=he));t=t.slice(n),n=he-t.length}else n-=a;for(;n--;)t+="0";if(e.d.push(+t),de&&(e.e>ui||e.e<-ui))throw Error(Hh+r)}else e.s=0,e.e=0,e.d=[0];return e}function ce(e,t,r){var n,a,i,o,u,c,s,f,l=e.d;for(o=1,i=l[0];i>=10;i/=10)o++;if(n=t-o,n<0)n+=he,a=t,s=l[f=0];else{if(f=Math.ceil((n+1)/he),i=l.length,f>=i)return e;for(s=i=l[f],o=1;i>=10;i/=10)o++;n%=he,a=n-he+o}if(r!==void 0&&(i=Jt(10,o-a-1),u=s/i%10|0,c=t<0||l[f+1]!==void 0||s%i,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?a>0?s/Jt(10,o-a):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(i=be(e),l.length=1,t=t-i-1,l[0]=Jt(10,(he-t%he)%he),e.e=ln(-t/he)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,i=1,f--):(l.length=f+1,i=Jt(10,he-n),l[f]=a>0?(s/Jt(10,o-a)%Jt(10,a)|0)*i:0),c)for(;;)if(f==0){(l[0]+=i)==Pe&&(l[0]=1,++e.e);break}else{if(l[f]+=i,l[f]!=Pe)break;l[f--]=0,i=1}for(n=l.length;l[--n]===0;)l.pop();if(de&&(e.e>ui||e.e<-ui))throw Error(Hh+be(e));return e}function Xb(e,t){var r,n,a,i,o,u,c,s,f,l,h=e.constructor,d=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),de?ce(t,d):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),a=Math.max(Math.ceil(d/he),u)+2,o>a&&(o=a,r.length=1),r.reverse(),a=o;a--;)r.push(0);r.reverse()}else{for(a=c.length,u=l.length,f=a<u,f&&(u=a),a=0;a<u;a++)if(c[a]!=l[a]){f=c[a]<l[a];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,a=l.length-u;a>0;--a)c[u++]=0;for(a=l.length;a>o;){if(c[--a]<l[a]){for(i=a;i&&c[--i]===0;)c[i]=Pe-1;--c[i],c[a]+=Pe}c[a]-=l[a]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,de?ce(t,d):t):new h(0)}function sr(e,t,r){var n,a=be(e),i=lt(e.d),o=i.length;return t?(r&&(n=r-o)>0?i=i.charAt(0)+"."+i.slice(1)+It(n):o>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(a<0?"e":"e+")+a):a<0?(i="0."+It(-a-1)+i,r&&(n=r-o)>0&&(i+=It(n))):a>=o?(i+=It(a+1-o),r&&(n=r-a-1)>0&&(i=i+"."+It(n))):((n=a+1)<o&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-o)>0&&(a+1===o&&(i+="."),i+=It(n))),e.s<0?"-"+i:i}function Fy(e,t){if(e.length>t)return e.length=t,!0}function Yb(e){var t,r,n;function a(i){var o=this;if(!(o instanceof a))return new a(i);if(o.constructor=a,i instanceof a){o.s=i.s,o.e=i.e,o.d=(i=i.d)?i.slice():i;return}if(typeof i=="number"){if(i*0!==0)throw Error(or+i);if(i>0)o.s=1;else if(i<0)i=-i,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(i===~~i&&i<1e7){o.e=0,o.d=[i];return}return Hy(o,i.toString())}else if(typeof i!="string")throw Error(or+i);if(i.charCodeAt(0)===45?(i=i.slice(1),o.s=-1):o.s=1,U4.test(i))Hy(o,i);else throw Error(or+i)}if(a.prototype=z,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=Yb,a.config=a.set=G4,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return a.config(e),a}function G4(e){if(!e||typeof e!="object")throw Error(et+"Object expected");var t,r,n,a=["precision",1,sn,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<a.length;t+=3)if((n=e[r=a[t]])!==void 0)if(ln(n)===n&&n>=a[t+1]&&n<=a[t+2])this[r]=n;else throw Error(or+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(or+r+": "+n);return this}var Fh=Yb(W4);ze=new Fh(1);const oe=Fh;function K4(e){return Q4(e)||J4(e)||Y4(e)||X4()}function X4(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Y4(e,t){if(e){if(typeof e=="string")return Jl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Jl(e,t)}}function J4(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function Q4(e){if(Array.isArray(e))return Jl(e)}function Jl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var eP=function(t){return t},Jb={},Qb=function(t){return t===Jb},Vy=function(t){return function r(){return arguments.length===0||arguments.length===1&&Qb(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},tP=function e(t,r){return t===1?r:Vy(function(){for(var n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];var o=a.filter(function(u){return u!==Jb}).length;return o>=t?r.apply(void 0,a):e(t-o,Vy(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=a.map(function(l){return Qb(l)?c.shift():l});return r.apply(void 0,K4(f).concat(c))}))})},oo=function(t){return tP(t.length,t)},Ql=function(t,r){for(var n=[],a=t;a<r;++a)n[a-t]=a;return n},rP=oo(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),nP=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return eP;var a=r.reverse(),i=a[0],o=a.slice(1);return function(){return o.reduce(function(u,c){return c(u)},i.apply(void 0,arguments))}},ef=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},e2=function(t){var r=null,n=null;return function(){for(var a=arguments.length,i=new Array(a),o=0;o<a;o++)i[o]=arguments[o];return r&&i.every(function(u,c){return u===r[c]})||(r=i,n=t.apply(void 0,i)),n}};function aP(e){var t;return e===0?t=1:t=Math.floor(new oe(e).abs().log(10).toNumber())+1,t}function iP(e,t,r){for(var n=new oe(e),a=0,i=[];n.lt(t)&&a<1e5;)i.push(n.toNumber()),n=n.add(r),a++;return i}var oP=oo(function(e,t,r){var n=+e,a=+t;return n+r*(a-n)}),uP=oo(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),cP=oo(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const uo={rangeStep:iP,getDigitCount:aP,interpolateNumber:oP,uninterpolateNumber:uP,uninterpolateTruncation:cP};function tf(e){return fP(e)||lP(e)||t2(e)||sP()}function sP(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function lP(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function fP(e){if(Array.isArray(e))return rf(e)}function Vn(e,t){return pP(e)||dP(e,t)||t2(e,t)||hP()}function hP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function t2(e,t){if(e){if(typeof e=="string")return rf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rf(e,t)}}function rf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function dP(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,a=!1,i=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){a=!0,i=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(a)throw i}}return r}}function pP(e){if(Array.isArray(e))return e}function r2(e){var t=Vn(e,2),r=t[0],n=t[1],a=r,i=n;return r>n&&(a=n,i=r),[a,i]}function n2(e,t,r){if(e.lte(0))return new oe(0);var n=uo.getDigitCount(e.toNumber()),a=new oe(10).pow(n),i=e.div(a),o=n!==1?.05:.1,u=new oe(Math.ceil(i.div(o).toNumber())).add(r).mul(o),c=u.mul(a);return t?c:new oe(Math.ceil(c))}function vP(e,t,r){var n=1,a=new oe(e);if(!a.isint()&&r){var i=Math.abs(e);i<1?(n=new oe(10).pow(uo.getDigitCount(e)-1),a=new oe(Math.floor(a.div(n).toNumber())).mul(n)):i>1&&(a=new oe(Math.floor(e)))}else e===0?a=new oe(Math.floor((t-1)/2)):r||(a=new oe(Math.floor(e)));var o=Math.floor((t-1)/2),u=nP(rP(function(c){return a.add(new oe(c-o).mul(n)).toNumber()}),Ql);return u(0,t)}function a2(e,t,r,n){var a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new oe(0),tickMin:new oe(0),tickMax:new oe(0)};var i=n2(new oe(t).sub(e).div(r-1),n,a),o;e<=0&&t>=0?o=new oe(0):(o=new oe(e).add(t).div(2),o=o.sub(new oe(o).mod(i)));var u=Math.ceil(o.sub(e).div(i).toNumber()),c=Math.ceil(new oe(t).sub(o).div(i).toNumber()),s=u+c+1;return s>r?a2(e,t,r,n,a+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:i,tickMin:o.sub(new oe(u).mul(i)),tickMax:o.add(new oe(c).mul(i))})}function yP(e){var t=Vn(e,2),r=t[0],n=t[1],a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(a,2),u=r2([r,n]),c=Vn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(tf(Ql(0,a-1).map(function(){return 1/0}))):[].concat(tf(Ql(0,a-1).map(function(){return-1/0})),[f]);return r>n?ef(l):l}if(s===f)return vP(s,a,i);var h=a2(s,f,o,i),d=h.step,y=h.tickMin,v=h.tickMax,p=uo.rangeStep(y,v.add(new oe(.1).mul(d)),d);return r>n?ef(p):p}function mP(e,t){var r=Vn(e,2),n=r[0],a=r[1],i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=r2([n,a]),u=Vn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,a];if(c===s)return[c];var f=Math.max(t,2),l=n2(new oe(s).sub(c).div(f-1),i,0),h=[].concat(tf(uo.rangeStep(new oe(c),new oe(s).sub(new oe(.99).mul(l)),l)),[s]);return n>a?ef(h):h}var gP=e2(yP),bP=e2(mP),xP="Invariant failed";function lr(e,t){throw new Error(xP)}var wP=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Br(e){"@babel/helpers - typeof";return Br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Br(e)}function ci(){return ci=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ci.apply(this,arguments)}function OP(e,t){return PP(e)||SP(e,t)||_P(e,t)||AP()}function AP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _P(e,t){if(e){if(typeof e=="string")return zy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zy(e,t)}}function zy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function SP(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function PP(e){if(Array.isArray(e))return e}function EP(e,t){if(e==null)return{};var r=jP(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function jP(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function TP(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function MP(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u2(n.key),n)}}function $P(e,t,r){return t&&MP(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function CP(e,t,r){return t=si(t),IP(e,i2()?Reflect.construct(t,r||[],si(e).constructor):t.apply(e,r))}function IP(e,t){if(t&&(Br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return kP(e)}function kP(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i2(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(i2=function(){return!!e})()}function si(e){return si=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},si(e)}function RP(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nf(e,t)}function nf(e,t){return nf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},nf(e,t)}function o2(e,t,r){return t=u2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u2(e){var t=DP(e,"string");return Br(t)=="symbol"?t:t+""}function DP(e,t){if(Br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var co=(function(e){function t(){return TP(this,t),CP(this,t,arguments)}return RP(t,e),$P(t,[{key:"render",value:function(){var n=this.props,a=n.offset,i=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,h=EP(n,wP),d=K(h,!1);this.props.direction==="x"&&f.type!=="number"&&lr();var y=c.map(function(v){var p=s(v,u),x=p.x,O=p.y,w=p.value,A=p.errorVal;if(!A)return null;var m=[],b,_;if(Array.isArray(A)){var S=OP(A,2);b=S[0],_=S[1]}else b=_=A;if(i==="vertical"){var j=f.scale,C=O+a,E=C+o,M=C-o,$=j(w-b),k=j(w+_);m.push({x1:k,y1:E,x2:k,y2:M}),m.push({x1:$,y1:C,x2:k,y2:C}),m.push({x1:$,y1:E,x2:$,y2:M})}else if(i==="horizontal"){var I=l.scale,D=x+a,N=D-o,q=D+o,H=I(w-b),Z=I(w+_);m.push({x1:N,y1:Z,x2:q,y2:Z}),m.push({x1:D,y1:H,x2:D,y2:Z}),m.push({x1:N,y1:H,x2:q,y2:H})}return P.createElement(ue,ci({className:"recharts-errorBar",key:"bar-".concat(m.map(function(U){return"".concat(U.x1,"-").concat(U.x2,"-").concat(U.y1,"-").concat(U.y2)}))},d),m.map(function(U){return P.createElement("line",ci({},U,{key:"line-".concat(U.x1,"-").concat(U.x2,"-").concat(U.y1,"-").concat(U.y2)}))}))});return P.createElement(ue,{className:"recharts-errorBars"},y)}}])})(P.Component);o2(co,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});o2(co,"displayName","ErrorBar");function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function Zy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Gt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zy(Object(r),!0).forEach(function(n){NP(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function NP(e,t,r){return t=LP(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function LP(e){var t=qP(e,"string");return zn(t)=="symbol"?t:t+""}function qP(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var c2=function(t){var r=t.children,n=t.formattedGraphicalItems,a=t.legendWidth,i=t.legendContent,o=Fe(r,jr);if(!o)return null;var u=jr.defaultProps,c=u!==void 0?Gt(Gt({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:i==="children"?s=(n||[]).reduce(function(f,l){var h=l.item,d=l.props,y=d.sectors||d.data||[];return f.concat(y.map(function(v){return{type:o.props.iconType||h.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):s=(n||[]).map(function(f){var l=f.item,h=l.type.defaultProps,d=h!==void 0?Gt(Gt({},h),l.props):{},y=d.dataKey,v=d.name,p=d.legendType,x=d.hide;return{inactive:x,dataKey:y,type:c.iconType||p||"square",color:Vh(l),value:v||y,payload:d}}),Gt(Gt(Gt({},c),jr.getWithHeight(o,a)),{},{payload:s,item:o})};function Zn(e){"@babel/helpers - typeof";return Zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zn(e)}function Wy(e){return VP(e)||FP(e)||HP(e)||BP()}function BP(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function HP(e,t){if(e){if(typeof e=="string")return af(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return af(e,t)}}function FP(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function VP(e){if(Array.isArray(e))return af(e)}function af(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Uy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function ye(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Uy(Object(r),!0).forEach(function(n){Mr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Uy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Mr(e,t,r){return t=zP(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zP(e){var t=ZP(e,"string");return Zn(t)=="symbol"?t:t+""}function ZP(e,t){if(Zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ot(e,t,r){return re(e)||re(t)?r:Ae(t)?Je(e,t,r):X(t)?t(e):r}function Pn(e,t,r,n){var a=V4(e,function(u){return ot(u,t)});if(r==="number"){var i=a.filter(function(u){return B(u)||parseFloat(u)});return i.length?[ao(i),no(i)]:[1/0,-1/0]}var o=n?a.filter(function(u){return!re(u)}):a;return o.map(function(u){return Ae(u)||u instanceof Date?u:""})}var WP=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],a=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(i&&i.axisType==="angleAxis"&&Math.abs(Math.abs(i.range[1]-i.range[0])-360)<=1e-6)for(var c=i.range,s=0;s<u;s++){var f=s>0?a[s-1].coordinate:a[u-1].coordinate,l=a[s].coordinate,h=s>=u-1?a[0].coordinate:a[s+1].coordinate,d=void 0;if(Ne(l-f)!==Ne(h-l)){var y=[];if(Ne(h-l)===Ne(c[1]-c[0])){d=h;var v=l+c[1]-c[0];y[0]=Math.min(v,(v+f)/2),y[1]=Math.max(v,(v+f)/2)}else{d=f;var p=h+c[1]-c[0];y[0]=Math.min(l,(p+l)/2),y[1]=Math.max(l,(p+l)/2)}var x=[Math.min(l,(d+l)/2),Math.max(l,(d+l)/2)];if(t>x[0]&&t<=x[1]||t>=y[0]&&t<=y[1]){o=a[s].index;break}}else{var O=Math.min(f,h),w=Math.max(f,h);if(t>(O+l)/2&&t<=(w+l)/2){o=a[s].index;break}}}else for(var A=0;A<u;A++)if(A===0&&t<=(n[A].coordinate+n[A+1].coordinate)/2||A>0&&A<u-1&&t>(n[A].coordinate+n[A-1].coordinate)/2&&t<=(n[A].coordinate+n[A+1].coordinate)/2||A===u-1&&t>(n[A].coordinate+n[A-1].coordinate)/2){o=n[A].index;break}return o},Vh=function(t){var r,n=t,a=n.type.displayName,i=(r=t.type)!==null&&r!==void 0&&r.defaultProps?ye(ye({},t.type.defaultProps),t.props):t.props,o=i.stroke,u=i.fill,c;switch(a){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},UP=function(t){var r=t.barSize,n=t.totalSize,a=t.stackGroups,i=a===void 0?{}:a;if(!i)return{};for(var o={},u=Object.keys(i),c=0,s=u.length;c<s;c++)for(var f=i[u[c]].stackGroups,l=Object.keys(f),h=0,d=l.length;h<d;h++){var y=f[l[h]],v=y.items,p=y.cateAxisId,x=v.filter(function(_){return Ot(_.type).indexOf("Bar")>=0});if(x&&x.length){var O=x[0].type.defaultProps,w=O!==void 0?ye(ye({},O),x[0].props):x[0].props,A=w.barSize,m=w[p];o[m]||(o[m]=[]);var b=re(A)?r:A;o[m].push({item:x[0],stackList:x.slice(1),barSize:re(b)?void 0:it(b,n,0)})}}return o},GP=function(t){var r=t.barGap,n=t.barCategoryGap,a=t.bandSize,i=t.sizeList,o=i===void 0?[]:i,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=it(r,a,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var h=!1,d=a/c,y=o.reduce(function(A,m){return A+m.barSize||0},0);y+=(c-1)*s,y>=a&&(y-=(c-1)*s,s=0),y>=a&&d>0&&(h=!0,d*=.9,y=c*d);var v=(a-y)/2>>0,p={offset:v-s,size:0};f=o.reduce(function(A,m){var b={item:m.item,position:{offset:p.offset+p.size+s,size:h?d:m.barSize}},_=[].concat(Wy(A),[b]);return p=_[_.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(S){_.push({item:S,position:p})}),_},l)}else{var x=it(n,a,0,!0);a-2*x-(c-1)*s<=0&&(s=0);var O=(a-2*x-(c-1)*s)/c;O>1&&(O>>=0);var w=u===+u?Math.min(O,u):O;f=o.reduce(function(A,m,b){var _=[].concat(Wy(A),[{item:m.item,position:{offset:x+(O+s)*b+(O-w)/2,size:w}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(S){_.push({item:S,position:_[_.length-1].position})}),_},l)}return f},KP=function(t,r,n,a){var i=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=c2({children:i,legendWidth:c});if(s){var f=a||{},l=f.width,h=f.height,d=s.align,y=s.verticalAlign,v=s.layout;if((v==="vertical"||v==="horizontal"&&y==="middle")&&d!=="center"&&B(t[d]))return ye(ye({},t),{},Mr({},d,t[d]+(l||0)));if((v==="horizontal"||v==="vertical"&&d==="center")&&y!=="middle"&&B(t[y]))return ye(ye({},t),{},Mr({},y,t[y]+(h||0)))}return t},XP=function(t,r,n){return re(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},s2=function(t,r,n,a,i){var o=r.props.children,u=Qe(o,co).filter(function(s){return XP(a,i,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=ot(f,n);if(re(l))return s;var h=Array.isArray(l)?[ao(l),no(l)]:[l,l],d=c.reduce(function(y,v){var p=ot(f,v,0),x=h[0]-Math.abs(Array.isArray(p)?p[0]:p),O=h[1]+Math.abs(Array.isArray(p)?p[1]:p);return[Math.min(x,y[0]),Math.max(O,y[1])]},[1/0,-1/0]);return[Math.min(d[0],s[0]),Math.max(d[1],s[1])]},[1/0,-1/0])}return null},YP=function(t,r,n,a,i){var o=r.map(function(u){return s2(t,u,n,i,a)}).filter(function(u){return!re(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},l2=function(t,r,n,a,i){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&s2(t,c,s,a)||Pn(t,s,n,i)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},f2=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},h2=function(t,r,n,a){if(a)return t.map(function(c){return c.coordinate});var i,o,u=t.map(function(c){return c.coordinate===r&&(i=!0),c.coordinate===n&&(o=!0),c.coordinate});return i||u.push(r),o||u.push(n),u},wt=function(t,r,n){if(!t)return null;var a=t.scale,i=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?a.bandwidth()/2:2,s=(r||n)&&o==="category"&&a.bandwidth?a.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Ne(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var h=i?i.indexOf(l):l;return{coordinate:a(h)+s,value:l,offset:s}});return f.filter(function(l){return!ga(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,h){return{coordinate:a(l)+s,value:l,index:h,offset:s}}):a.ticks&&!n?a.ticks(t.tickCount).map(function(l){return{coordinate:a(l)+s,value:l,offset:s}}):a.domain().map(function(l,h){return{coordinate:a(l)+s,value:i?i[l]:l,index:h,offset:s}})},Us=new WeakMap,ka=function(t,r){if(typeof r!="function")return t;Us.has(t)||Us.set(t,new WeakMap);var n=Us.get(t);if(n.has(r))return n.get(r);var a=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,a),a},d2=function(t,r,n){var a=t.scale,i=t.type,o=t.layout,u=t.axisType;if(a==="auto")return o==="radial"&&u==="radiusAxis"?{scale:Nn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:ni(),realScaleType:"linear"}:i==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:Sn(),realScaleType:"point"}:i==="category"?{scale:Nn(),realScaleType:"band"}:{scale:ni(),realScaleType:"linear"};if(cr(a)){var c="scale".concat(Vi(a));return{scale:(Cy[c]||Sn)(),realScaleType:Cy[c]?c:"point"}}return X(a)?{scale:a}:{scale:Sn(),realScaleType:"point"}},Gy=1e-4,p2=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,a=t.range(),i=Math.min(a[0],a[1])-Gy,o=Math.max(a[0],a[1])+Gy,u=t(r[0]),c=t(r[n-1]);(u<i||u>o||c<i||c>o)&&t.domain([r[0],r[n-1]])}},v2=function(t,r){if(!t)return null;for(var n=0,a=t.length;n<a;n++)if(t[n].item===r)return t[n].position;return null},y2=function(t,r){if(!r||r.length!==2||!B(r[0])||!B(r[1]))return t;var n=Math.min(r[0],r[1]),a=Math.max(r[0],r[1]),i=[t[0],t[1]];return(!B(t[0])||t[0]<n)&&(i[0]=n),(!B(t[1])||t[1]>a)&&(i[1]=a),i[0]>a&&(i[0]=a),i[1]<n&&(i[1]=n),i},JP=function(t){var r=t.length;if(!(r<=0))for(var n=0,a=t[0].length;n<a;++n)for(var i=0,o=0,u=0;u<r;++u){var c=ga(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=i,t[u][n][1]=i+c,i=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},QP=function(t){var r=t.length;if(!(r<=0))for(var n=0,a=t[0].length;n<a;++n)for(var i=0,o=0;o<r;++o){var u=ga(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=i,t[o][n][1]=i+u,i=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},eE={sign:JP,expand:FO,none:$r,silhouette:VO,wiggle:zO,positive:QP},tE=function(t,r,n){var a=r.map(function(u){return u.props.dataKey}),i=eE[n],o=HO().keys(a).value(function(u,c){return+ot(u,c,0)}).order(kl).offset(i);return o(t)},rE=function(t,r,n,a,i,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,h){var d,y=(d=h.type)!==null&&d!==void 0&&d.defaultProps?ye(ye({},h.type.defaultProps),h.props):h.props,v=y.stackId,p=y.hide;if(p)return l;var x=y[n],O=l[x]||{hasStack:!1,stackGroups:{}};if(Ae(v)){var w=O.stackGroups[v]||{numericAxisId:n,cateAxisId:a,items:[]};w.items.push(h),O.hasStack=!0,O.stackGroups[v]=w}else O.stackGroups[Fi("_stackId_")]={numericAxisId:n,cateAxisId:a,items:[h]};return ye(ye({},l),{},Mr({},x,O))},c),f={};return Object.keys(s).reduce(function(l,h){var d=s[h];if(d.hasStack){var y={};d.stackGroups=Object.keys(d.stackGroups).reduce(function(v,p){var x=d.stackGroups[p];return ye(ye({},v),{},Mr({},p,{numericAxisId:n,cateAxisId:a,items:x.items,stackedData:tE(t,x.items,i)}))},y)}return ye(ye({},l),{},Mr({},h,d))},f)},m2=function(t,r){var n=r.realScaleType,a=r.type,i=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(i&&a==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=gP(s,i,u);return t.domain([ao(f),no(f)]),{niceTicks:f}}if(i&&a==="number"){var l=t.domain(),h=bP(l,i,u);return{niceTicks:h}}return null},li=function(t){var r=t.axis,n=t.ticks,a=t.offset,i=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+a:null;var c=ot(o,r.dataKey,r.domain[u]);return re(c)?null:r.scale(c)-i/2+a},g2=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var a=Math.min(n[0],n[1]),i=Math.max(n[0],n[1]);return a<=0&&i>=0?0:i<0?i:a}return n[0]},nE=function(t,r){var n,a=(n=t.type)!==null&&n!==void 0&&n.defaultProps?ye(ye({},t.type.defaultProps),t.props):t.props,i=a.stackId;if(Ae(i)){var o=r[i];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},aE=function(t){return t.reduce(function(r,n){return[ao(n.concat([r[0]]).filter(B)),no(n.concat([r[1]]).filter(B))]},[1/0,-1/0])},b2=function(t,r,n){return Object.keys(t).reduce(function(a,i){var o=t[i],u=o.stackedData,c=u.reduce(function(s,f){var l=aE(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],a[0]),Math.max(c[1],a[1])]},[1/0,-1/0]).map(function(a){return a===1/0||a===-1/0?0:a})},Ky=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Xy=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,of=function(t,r,n){if(X(t))return t(r,n);if(!Array.isArray(t))return r;var a=[];if(B(t[0]))a[0]=n?t[0]:Math.min(t[0],r[0]);else if(Ky.test(t[0])){var i=+Ky.exec(t[0])[1];a[0]=r[0]-i}else X(t[0])?a[0]=t[0](r[0]):a[0]=r[0];if(B(t[1]))a[1]=n?t[1]:Math.max(t[1],r[1]);else if(Xy.test(t[1])){var o=+Xy.exec(t[1])[1];a[1]=r[1]+o}else X(t[1])?a[1]=t[1](r[1]):a[1]=r[1];return a},fi=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var a=t.scale.bandwidth();if(!n||a>0)return a}if(t&&r&&r.length>=2){for(var i=yh(r,function(l){return l.coordinate}),o=1/0,u=1,c=i.length;u<c;u++){var s=i[u],f=i[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},Yy=function(t,r,n){return!t||!t.length||io(t,Je(n,"type.defaultProps.domain"))?r:t},zh=function(t,r){var n=t.type.defaultProps?ye(ye({},t.type.defaultProps),t.props):t.props,a=n.dataKey,i=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return ye(ye({},K(t,!1)),{},{dataKey:a,unit:o,formatter:u,name:i||a,color:Vh(t),value:ot(r,a),type:c,payload:r,chartType:s,hide:f})};function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}function Jy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function mt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Jy(Object(r),!0).forEach(function(n){x2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function x2(e,t,r){return t=iE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iE(e){var t=oE(e,"string");return Wn(t)=="symbol"?t:t+""}function oE(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function uE(e,t){return fE(e)||lE(e,t)||sE(e,t)||cE()}function cE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sE(e,t){if(e){if(typeof e=="string")return Qy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qy(e,t)}}function Qy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function lE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function fE(e){if(Array.isArray(e))return e}var hi=Math.PI/180,hE=function(t){return t*180/Math.PI},pe=function(t,r,n,a){return{x:t+Math.cos(-hi*a)*n,y:r+Math.sin(-hi*a)*n}},dE=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},pE=function(t,r,n,a,i){var o=t.width,u=t.height,c=t.startAngle,s=t.endAngle,f=it(t.cx,o,o/2),l=it(t.cy,u,u/2),h=dE(o,u,n),d=it(t.innerRadius,h,0),y=it(t.outerRadius,h,h*.8),v=Object.keys(r);return v.reduce(function(p,x){var O=r[x],w=O.domain,A=O.reversed,m;if(re(O.range))a==="angleAxis"?m=[c,s]:a==="radiusAxis"&&(m=[d,y]),A&&(m=[m[1],m[0]]);else{m=O.range;var b=m,_=uE(b,2);c=_[0],s=_[1]}var S=d2(O,i),j=S.realScaleType,C=S.scale;C.domain(w).range(m),p2(C);var E=m2(C,mt(mt({},O),{},{realScaleType:j})),M=mt(mt(mt({},O),E),{},{range:m,radius:y,realScaleType:j,scale:C,cx:f,cy:l,innerRadius:d,outerRadius:y,startAngle:c,endAngle:s});return mt(mt({},p),{},x2({},x,M))},{})},vE=function(t,r){var n=t.x,a=t.y,i=r.x,o=r.y;return Math.sqrt(Math.pow(n-i,2)+Math.pow(a-o,2))},yE=function(t,r){var n=t.x,a=t.y,i=r.cx,o=r.cy,u=vE({x:n,y:a},{x:i,y:o});if(u<=0)return{radius:u};var c=(n-i)/u,s=Math.acos(c);return a>o&&(s=2*Math.PI-s),{radius:u,angle:hE(s),angleInRadian:s}},mE=function(t){var r=t.startAngle,n=t.endAngle,a=Math.floor(r/360),i=Math.floor(n/360),o=Math.min(a,i);return{startAngle:r-o*360,endAngle:n-o*360}},gE=function(t,r){var n=r.startAngle,a=r.endAngle,i=Math.floor(n/360),o=Math.floor(a/360),u=Math.min(i,o);return t+u*360},e1=function(t,r){var n=t.x,a=t.y,i=yE({x:n,y:a},r),o=i.radius,u=i.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=mE(r),l=f.startAngle,h=f.endAngle,d=u,y;if(l<=h){for(;d>h;)d-=360;for(;d<l;)d+=360;y=d>=l&&d<=h}else{for(;d>l;)d-=360;for(;d<h;)d+=360;y=d>=h&&d<=l}return y?mt(mt({},r),{},{radius:o,angle:gE(d,r)}):null},w2=function(t){return!g.isValidElement(t)&&!X(t)&&typeof t!="boolean"?t.className:""};function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}var bE=["offset"];function xE(e){return _E(e)||AE(e)||OE(e)||wE()}function wE(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function OE(e,t){if(e){if(typeof e=="string")return uf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uf(e,t)}}function AE(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function _E(e){if(Array.isArray(e))return uf(e)}function uf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function SE(e,t){if(e==null)return{};var r=PE(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function PE(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function t1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Oe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?t1(Object(r),!0).forEach(function(n){EE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function EE(e,t,r){return t=jE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jE(e){var t=TE(e,"string");return Un(t)=="symbol"?t:t+""}function TE(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Gn(){return Gn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Gn.apply(this,arguments)}var ME=function(t){var r=t.value,n=t.formatter,a=re(t.children)?r:t.children;return X(n)?n(a):a},$E=function(t,r){var n=Ne(r-t),a=Math.min(Math.abs(r-t),360);return n*a},CE=function(t,r,n){var a=t.position,i=t.viewBox,o=t.offset,u=t.className,c=i,s=c.cx,f=c.cy,l=c.innerRadius,h=c.outerRadius,d=c.startAngle,y=c.endAngle,v=c.clockWise,p=(l+h)/2,x=$E(d,y),O=x>=0?1:-1,w,A;a==="insideStart"?(w=d+O*o,A=v):a==="insideEnd"?(w=y-O*o,A=!v):a==="end"&&(w=y+O*o,A=v),A=x<=0?A:!A;var m=pe(s,f,p,w),b=pe(s,f,p,w+(A?1:-1)*359),_="M".concat(m.x,",").concat(m.y,`
    A`).concat(p,",").concat(p,",0,1,").concat(A?0:1,`,
    `).concat(b.x,",").concat(b.y),S=re(t.id)?Fi("recharts-radial-line-"):t.id;return P.createElement("text",Gn({},n,{dominantBaseline:"central",className:Q("recharts-radial-bar-label",u)}),P.createElement("defs",null,P.createElement("path",{id:S,d:_})),P.createElement("textPath",{xlinkHref:"#".concat(S)},r))},IE=function(t){var r=t.viewBox,n=t.offset,a=t.position,i=r,o=i.cx,u=i.cy,c=i.innerRadius,s=i.outerRadius,f=i.startAngle,l=i.endAngle,h=(f+l)/2;if(a==="outside"){var d=pe(o,u,s+n,h),y=d.x,v=d.y;return{x:y,y:v,textAnchor:y>=o?"start":"end",verticalAnchor:"middle"}}if(a==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(a==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(a==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var p=(c+s)/2,x=pe(o,u,p,h),O=x.x,w=x.y;return{x:O,y:w,textAnchor:"middle",verticalAnchor:"middle"}},kE=function(t){var r=t.viewBox,n=t.parentViewBox,a=t.offset,i=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,h=l*a,d=l>0?"end":"start",y=l>0?"start":"end",v=s>=0?1:-1,p=v*a,x=v>0?"end":"start",O=v>0?"start":"end";if(i==="top"){var w={x:u+s/2,y:c-l*a,textAnchor:"middle",verticalAnchor:d};return Oe(Oe({},w),n?{height:Math.max(c-n.y,0),width:s}:{})}if(i==="bottom"){var A={x:u+s/2,y:c+f+h,textAnchor:"middle",verticalAnchor:y};return Oe(Oe({},A),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(i==="left"){var m={x:u-p,y:c+f/2,textAnchor:x,verticalAnchor:"middle"};return Oe(Oe({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(i==="right"){var b={x:u+s+p,y:c+f/2,textAnchor:O,verticalAnchor:"middle"};return Oe(Oe({},b),n?{width:Math.max(n.x+n.width-b.x,0),height:f}:{})}var _=n?{width:s,height:f}:{};return i==="insideLeft"?Oe({x:u+p,y:c+f/2,textAnchor:O,verticalAnchor:"middle"},_):i==="insideRight"?Oe({x:u+s-p,y:c+f/2,textAnchor:x,verticalAnchor:"middle"},_):i==="insideTop"?Oe({x:u+s/2,y:c+h,textAnchor:"middle",verticalAnchor:y},_):i==="insideBottom"?Oe({x:u+s/2,y:c+f-h,textAnchor:"middle",verticalAnchor:d},_):i==="insideTopLeft"?Oe({x:u+p,y:c+h,textAnchor:O,verticalAnchor:y},_):i==="insideTopRight"?Oe({x:u+s-p,y:c+h,textAnchor:x,verticalAnchor:y},_):i==="insideBottomLeft"?Oe({x:u+p,y:c+f-h,textAnchor:O,verticalAnchor:d},_):i==="insideBottomRight"?Oe({x:u+s-p,y:c+f-h,textAnchor:x,verticalAnchor:d},_):an(i)&&(B(i.x)||er(i.x))&&(B(i.y)||er(i.y))?Oe({x:u+it(i.x,s),y:c+it(i.y,f),textAnchor:"end",verticalAnchor:"end"},_):Oe({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},_)},RE=function(t){return"cx"in t&&B(t.cx)};function Te(e){var t=e.offset,r=t===void 0?5:t,n=SE(e,bE),a=Oe({offset:r},n),i=a.viewBox,o=a.position,u=a.value,c=a.children,s=a.content,f=a.className,l=f===void 0?"":f,h=a.textBreakAll;if(!i||re(u)&&re(c)&&!g.isValidElement(s)&&!X(s))return null;if(g.isValidElement(s))return g.cloneElement(s,a);var d;if(X(s)){if(d=g.createElement(s,a),g.isValidElement(d))return d}else d=ME(a);var y=RE(i),v=K(a,!0);if(y&&(o==="insideStart"||o==="insideEnd"||o==="end"))return CE(a,d,v);var p=y?IE(a):kE(a);return P.createElement(Dr,Gn({className:Q("recharts-label",l)},v,p,{breakAll:h}),d)}Te.displayName="Label";var O2=function(t){var r=t.cx,n=t.cy,a=t.angle,i=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,h=t.y,d=t.top,y=t.left,v=t.width,p=t.height,x=t.clockWise,O=t.labelViewBox;if(O)return O;if(B(v)&&B(p)){if(B(l)&&B(h))return{x:l,y:h,width:v,height:p};if(B(d)&&B(y))return{x:d,y,width:v,height:p}}return B(l)&&B(h)?{x:l,y:h,width:0,height:0}:B(r)&&B(n)?{cx:r,cy:n,startAngle:i||a||0,endAngle:o||a||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:x}:t.viewBox?t.viewBox:{}},DE=function(t,r){return t?t===!0?P.createElement(Te,{key:"label-implicit",viewBox:r}):Ae(t)?P.createElement(Te,{key:"label-implicit",viewBox:r,value:t}):g.isValidElement(t)?t.type===Te?g.cloneElement(t,{key:"label-implicit",viewBox:r}):P.createElement(Te,{key:"label-implicit",content:t,viewBox:r}):X(t)?P.createElement(Te,{key:"label-implicit",content:t,viewBox:r}):an(t)?P.createElement(Te,Gn({viewBox:r},t,{key:"label-implicit"})):null:null},NE=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var a=t.children,i=O2(t),o=Qe(a,Te).map(function(c,s){return g.cloneElement(c,{viewBox:r||i,key:"label-".concat(s)})});if(!n)return o;var u=DE(t.label,r||i);return[u].concat(xE(o))};Te.parseViewBox=O2;Te.renderCallByParent=NE;var Gs,r1;function LE(){if(r1)return Gs;r1=1;function e(t){var r=t==null?0:t.length;return r?t[r-1]:void 0}return Gs=e,Gs}var qE=LE();const BE=se(qE);function Kn(e){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(e)}var HE=["valueAccessor"],FE=["data","dataKey","clockWise","id","textBreakAll"];function VE(e){return UE(e)||WE(e)||ZE(e)||zE()}function zE(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ZE(e,t){if(e){if(typeof e=="string")return cf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cf(e,t)}}function WE(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function UE(e){if(Array.isArray(e))return cf(e)}function cf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function di(){return di=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},di.apply(this,arguments)}function n1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function a1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?n1(Object(r),!0).forEach(function(n){GE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function GE(e,t,r){return t=KE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function KE(e){var t=XE(e,"string");return Kn(t)=="symbol"?t:t+""}function XE(e,t){if(Kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function i1(e,t){if(e==null)return{};var r=YE(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function YE(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var JE=function(t){return Array.isArray(t.value)?BE(t.value):t.value};function qt(e){var t=e.valueAccessor,r=t===void 0?JE:t,n=i1(e,HE),a=n.data,i=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=i1(n,FE);return!a||!a.length?null:P.createElement(ue,{className:"recharts-label-list"},a.map(function(f,l){var h=re(i)?r(f,l):ot(f&&f.payload,i),d=re(u)?{}:{id:"".concat(u,"-").concat(l)};return P.createElement(Te,di({},K(f,!0),s,d,{parentViewBox:f.parentViewBox,value:h,textBreakAll:c,viewBox:Te.parseViewBox(re(o)?f:a1(a1({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}qt.displayName="LabelList";function QE(e,t){return e?e===!0?P.createElement(qt,{key:"labelList-implicit",data:t}):P.isValidElement(e)||X(e)?P.createElement(qt,{key:"labelList-implicit",data:t,content:e}):an(e)?P.createElement(qt,di({data:t},e,{key:"labelList-implicit"})):null:null}function ej(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,a=Qe(n,qt).map(function(o,u){return g.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return a;var i=QE(e.label,t);return[i].concat(VE(a))}qt.renderCallByParent=ej;function Xn(e){"@babel/helpers - typeof";return Xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xn(e)}function sf(){return sf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},sf.apply(this,arguments)}function o1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function u1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?o1(Object(r),!0).forEach(function(n){tj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function tj(e,t,r){return t=rj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rj(e){var t=nj(e,"string");return Xn(t)=="symbol"?t:t+""}function nj(e,t){if(Xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var aj=function(t,r){var n=Ne(r-t),a=Math.min(Math.abs(r-t),359.999);return n*a},Ra=function(t){var r=t.cx,n=t.cy,a=t.radius,i=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+a,l=Math.asin(c/f)/hi,h=s?i:i+o*l,d=pe(r,n,f,h),y=pe(r,n,a,h),v=s?i-o*l:i,p=pe(r,n,f*Math.cos(l*hi),v);return{center:d,circleTangency:y,lineTangency:p,theta:l}},A2=function(t){var r=t.cx,n=t.cy,a=t.innerRadius,i=t.outerRadius,o=t.startAngle,u=t.endAngle,c=aj(o,u),s=o+c,f=pe(r,n,i,o),l=pe(r,n,i,s),h="M ".concat(f.x,",").concat(f.y,`
    A `).concat(i,",").concat(i,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(a>0){var d=pe(r,n,a,o),y=pe(r,n,a,s);h+="L ".concat(y.x,",").concat(y.y,`
            A `).concat(a,",").concat(a,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},ij=function(t){var r=t.cx,n=t.cy,a=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=Ne(f-s),h=Ra({cx:r,cy:n,radius:i,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),d=h.circleTangency,y=h.lineTangency,v=h.theta,p=Ra({cx:r,cy:n,radius:i,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),x=p.circleTangency,O=p.lineTangency,w=p.theta,A=c?Math.abs(s-f):Math.abs(s-f)-v-w;if(A<0)return u?"M ".concat(y.x,",").concat(y.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):A2({cx:r,cy:n,innerRadius:a,outerRadius:i,startAngle:s,endAngle:f});var m="M ".concat(y.x,",").concat(y.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(d.x,",").concat(d.y,`
    A`).concat(i,",").concat(i,",0,").concat(+(A>180),",").concat(+(l<0),",").concat(x.x,",").concat(x.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(O.x,",").concat(O.y,`
  `);if(a>0){var b=Ra({cx:r,cy:n,radius:a,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),_=b.circleTangency,S=b.lineTangency,j=b.theta,C=Ra({cx:r,cy:n,radius:a,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),E=C.circleTangency,M=C.lineTangency,$=C.theta,k=c?Math.abs(s-f):Math.abs(s-f)-j-$;if(k<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(M.x,",").concat(M.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(E.x,",").concat(E.y,`
      A`).concat(a,",").concat(a,",0,").concat(+(k>180),",").concat(+(l>0),",").concat(_.x,",").concat(_.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(S.x,",").concat(S.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},oj={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},_2=function(t){var r=u1(u1({},oj),t),n=r.cx,a=r.cy,i=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,h=r.className;if(o<i||f===l)return null;var d=Q("recharts-sector",h),y=o-i,v=it(u,y,0,!0),p;return v>0&&Math.abs(f-l)<360?p=ij({cx:n,cy:a,innerRadius:i,outerRadius:o,cornerRadius:Math.min(v,y/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):p=A2({cx:n,cy:a,innerRadius:i,outerRadius:o,startAngle:f,endAngle:l}),P.createElement("path",sf({},K(r,!0),{className:d,d:p,role:"img"}))};function Yn(e){"@babel/helpers - typeof";return Yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yn(e)}function lf(){return lf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lf.apply(this,arguments)}function c1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function s1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?c1(Object(r),!0).forEach(function(n){uj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uj(e,t,r){return t=cj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cj(e){var t=sj(e,"string");return Yn(t)=="symbol"?t:t+""}function sj(e,t){if(Yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var l1={curveBasisClosed:MO,curveBasisOpen:$O,curveBasis:TO,curveBumpX:vO,curveBumpY:yO,curveLinearClosed:CO,curveLinear:Zi,curveMonotoneX:IO,curveMonotoneY:kO,curveNatural:RO,curveStep:DO,curveStepAfter:LO,curveStepBefore:NO},Da=function(t){return t.x===+t.x&&t.y===+t.y},mn=function(t){return t.x},gn=function(t){return t.y},lj=function(t,r){if(X(t))return t;var n="curve".concat(Vi(t));return(n==="curveMonotone"||n==="curveBump")&&r?l1["".concat(n).concat(r==="vertical"?"Y":"X")]:l1[n]||Zi},fj=function(t){var r=t.type,n=r===void 0?"linear":r,a=t.points,i=a===void 0?[]:a,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=lj(n,u),l=s?i.filter(function(v){return Da(v)}):i,h;if(Array.isArray(o)){var d=s?o.filter(function(v){return Da(v)}):o,y=l.map(function(v,p){return s1(s1({},v),{},{base:d[p]})});return u==="vertical"?h=Ea().y(gn).x1(mn).x0(function(v){return v.base.x}):h=Ea().x(mn).y1(gn).y0(function(v){return v.base.y}),h.defined(Da).curve(f),h(y)}return u==="vertical"&&B(o)?h=Ea().y(gn).x1(mn).x0(o):B(o)?h=Ea().x(mn).y1(gn).y0(o):h=wg().x(mn).y(gn),h.defined(Da).curve(f),h(l)},f1=function(t){var r=t.className,n=t.points,a=t.path,i=t.pathRef;if((!n||!n.length)&&!a)return null;var o=n&&n.length?fj(t):a;return P.createElement("path",lf({},K(t,!1),Ba(t),{className:Q("recharts-curve",r),d:o,ref:i}))},hj=Object.getOwnPropertyNames,dj=Object.getOwnPropertySymbols,pj=Object.prototype.hasOwnProperty;function h1(e,t){return function(n,a,i){return e(n,a,i)&&t(n,a,i)}}function Na(e){return function(r,n,a){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,a);var i=a.cache,o=i.get(r),u=i.get(n);if(o&&u)return o===n&&u===r;i.set(r,n),i.set(n,r);var c=e(r,n,a);return i.delete(r),i.delete(n),c}}function d1(e){return hj(e).concat(dj(e))}var vj=Object.hasOwn||(function(e,t){return pj.call(e,t)});function mr(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var yj="__v",mj="__o",gj="_owner",p1=Object.getOwnPropertyDescriptor,v1=Object.keys;function bj(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function xj(e,t){return mr(e.getTime(),t.getTime())}function wj(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function Oj(e,t){return e===t}function y1(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var a=new Array(n),i=e.entries(),o,u,c=0;(o=i.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(a[l]){l++;continue}var h=o.value,d=u.value;if(r.equals(h[0],d[0],c,l,e,t,r)&&r.equals(h[1],d[1],h[0],d[0],e,t,r)){f=a[l]=!0;break}l++}if(!f)return!1;c++}return!0}var Aj=mr;function _j(e,t,r){var n=v1(e),a=n.length;if(v1(t).length!==a)return!1;for(;a-- >0;)if(!S2(e,t,r,n[a]))return!1;return!0}function bn(e,t,r){var n=d1(e),a=n.length;if(d1(t).length!==a)return!1;for(var i,o,u;a-- >0;)if(i=n[a],!S2(e,t,r,i)||(o=p1(e,i),u=p1(t,i),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function Sj(e,t){return mr(e.valueOf(),t.valueOf())}function Pj(e,t){return e.source===t.source&&e.flags===t.flags}function m1(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var a=new Array(n),i=e.values(),o,u;(o=i.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!a[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=a[f]=!0;break}f++}if(!s)return!1}return!0}function Ej(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function jj(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function S2(e,t,r,n){return(n===gj||n===mj||n===yj)&&(e.$$typeof||t.$$typeof)?!0:vj(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var Tj="[object Arguments]",Mj="[object Boolean]",$j="[object Date]",Cj="[object Error]",Ij="[object Map]",kj="[object Number]",Rj="[object Object]",Dj="[object RegExp]",Nj="[object Set]",Lj="[object String]",qj="[object URL]",Bj=Array.isArray,g1=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,b1=Object.assign,Hj=Object.prototype.toString.call.bind(Object.prototype.toString);function Fj(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,a=e.areFunctionsEqual,i=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,h=e.areUrlsEqual;return function(y,v,p){if(y===v)return!0;if(y==null||v==null)return!1;var x=typeof y;if(x!==typeof v)return!1;if(x!=="object")return x==="number"?o(y,v,p):x==="function"?a(y,v,p):!1;var O=y.constructor;if(O!==v.constructor)return!1;if(O===Object)return u(y,v,p);if(Bj(y))return t(y,v,p);if(g1!=null&&g1(y))return l(y,v,p);if(O===Date)return r(y,v,p);if(O===RegExp)return s(y,v,p);if(O===Map)return i(y,v,p);if(O===Set)return f(y,v,p);var w=Hj(y);return w===$j?r(y,v,p):w===Dj?s(y,v,p):w===Ij?i(y,v,p):w===Nj?f(y,v,p):w===Rj?typeof y.then!="function"&&typeof v.then!="function"&&u(y,v,p):w===qj?h(y,v,p):w===Cj?n(y,v,p):w===Tj?u(y,v,p):w===Mj||w===kj||w===Lj?c(y,v,p):!1}}function Vj(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,a={areArraysEqual:n?bn:bj,areDatesEqual:xj,areErrorsEqual:wj,areFunctionsEqual:Oj,areMapsEqual:n?h1(y1,bn):y1,areNumbersEqual:Aj,areObjectsEqual:n?bn:_j,arePrimitiveWrappersEqual:Sj,areRegExpsEqual:Pj,areSetsEqual:n?h1(m1,bn):m1,areTypedArraysEqual:n?bn:Ej,areUrlsEqual:jj};if(r&&(a=b1({},a,r(a))),t){var i=Na(a.areArraysEqual),o=Na(a.areMapsEqual),u=Na(a.areObjectsEqual),c=Na(a.areSetsEqual);a=b1({},a,{areArraysEqual:i,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return a}function zj(e){return function(t,r,n,a,i,o,u){return e(t,r,u)}}function Zj(e){var t=e.circular,r=e.comparator,n=e.createState,a=e.equals,i=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,h=l===void 0?t?new WeakMap:void 0:l,d=f.meta;return r(c,s,{cache:h,equals:a,meta:d,strict:i})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:a,meta:void 0,strict:i})};var o={cache:void 0,equals:a,meta:void 0,strict:i};return function(c,s){return r(c,s,o)}}var Wj=zt();zt({strict:!0});zt({circular:!0});zt({circular:!0,strict:!0});zt({createInternalComparator:function(){return mr}});zt({strict:!0,createInternalComparator:function(){return mr}});zt({circular:!0,createInternalComparator:function(){return mr}});zt({circular:!0,createInternalComparator:function(){return mr},strict:!0});function zt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,a=e.createState,i=e.strict,o=i===void 0?!1:i,u=Vj(e),c=Fj(u),s=n?n(c):zj(c);return Zj({circular:r,comparator:c,createState:a,equals:s,strict:o})}function Uj(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function x1(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function a(i){r<0&&(r=i),i-r>t?(e(i),r=-1):Uj(a)};requestAnimationFrame(n)}function ff(e){"@babel/helpers - typeof";return ff=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ff(e)}function Gj(e){return Jj(e)||Yj(e)||Xj(e)||Kj()}function Kj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Xj(e,t){if(e){if(typeof e=="string")return w1(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return w1(e,t)}}function w1(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Yj(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Jj(e){if(Array.isArray(e))return e}function Qj(){var e={},t=function(){return null},r=!1,n=function a(i){if(!r){if(Array.isArray(i)){if(!i.length)return;var o=i,u=Gj(o),c=u[0],s=u.slice(1);if(typeof c=="number"){x1(a.bind(null,s),c);return}a(c),x1(a.bind(null,s));return}ff(i)==="object"&&(e=i,t(e)),typeof i=="function"&&i()}};return{stop:function(){r=!0},start:function(i){r=!1,n(i)},subscribe:function(i){return t=i,function(){t=function(){return null}}}}}function Jn(e){"@babel/helpers - typeof";return Jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jn(e)}function O1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function A1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?O1(Object(r),!0).forEach(function(n){P2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function P2(e,t,r){return t=eT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eT(e){var t=tT(e,"string");return Jn(t)==="symbol"?t:String(t)}function tT(e,t){if(Jn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var rT=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,a){return n.filter(function(i){return a.includes(i)})})},nT=function(t){return t},aT=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},En=function(t,r){return Object.keys(r).reduce(function(n,a){return A1(A1({},n),{},P2({},a,t(a,r[a])))},{})},_1=function(t,r,n){return t.map(function(a){return"".concat(aT(a)," ").concat(r,"ms ").concat(n)}).join(",")};function iT(e,t){return cT(e)||uT(e,t)||E2(e,t)||oT()}function oT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function cT(e){if(Array.isArray(e))return e}function sT(e){return hT(e)||fT(e)||E2(e)||lT()}function lT(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function E2(e,t){if(e){if(typeof e=="string")return hf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hf(e,t)}}function fT(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function hT(e){if(Array.isArray(e))return hf(e)}function hf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var pi=1e-4,j2=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},T2=function(t,r){return t.map(function(n,a){return n*Math.pow(r,a)}).reduce(function(n,a){return n+a})},S1=function(t,r){return function(n){var a=j2(t,r);return T2(a,n)}},dT=function(t,r){return function(n){var a=j2(t,r),i=[].concat(sT(a.map(function(o,u){return o*u}).slice(1)),[0]);return T2(i,n)}},P1=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var a=r[0],i=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":a=0,i=0,o=1,u=1;break;case"ease":a=.25,i=.1,o=.25,u=1;break;case"ease-in":a=.42,i=0,o=1,u=1;break;case"ease-out":a=.42,i=0,o=.58,u=1;break;case"ease-in-out":a=0,i=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(p){return parseFloat(p)}),f=iT(s,4);a=f[0],i=f[1],o=f[2],u=f[3]}}}var l=S1(a,o),h=S1(i,u),d=dT(a,o),y=function(x){return x>1?1:x<0?0:x},v=function(x){for(var O=x>1?1:x,w=O,A=0;A<8;++A){var m=l(w)-O,b=d(w);if(Math.abs(m-O)<pi||b<pi)return h(w);w=y(w-m/b)}return h(w)};return v.isStepper=!1,v},pT=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,a=t.damping,i=a===void 0?8:a,o=t.dt,u=o===void 0?17:o,c=function(f,l,h){var d=-(f-l)*n,y=h*i,v=h+(d-y)*u/1e3,p=h*u/1e3+f;return Math.abs(p-l)<pi&&Math.abs(v)<pi?[l,0]:[p,v]};return c.isStepper=!0,c.dt=u,c},vT=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var a=r[0];if(typeof a=="string")switch(a){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return P1(a);case"spring":return pT();default:if(a.split("(")[0]==="cubic-bezier")return P1(a)}return typeof a=="function"?a:null};function Qn(e){"@babel/helpers - typeof";return Qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qn(e)}function E1(e){return gT(e)||mT(e)||M2(e)||yT()}function yT(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mT(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function gT(e){if(Array.isArray(e))return pf(e)}function j1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?j1(Object(r),!0).forEach(function(n){df(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):j1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function df(e,t,r){return t=bT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bT(e){var t=xT(e,"string");return Qn(t)==="symbol"?t:String(t)}function xT(e,t){if(Qn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function wT(e,t){return _T(e)||AT(e,t)||M2(e,t)||OT()}function OT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function M2(e,t){if(e){if(typeof e=="string")return pf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pf(e,t)}}function pf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function AT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function _T(e){if(Array.isArray(e))return e}var vi=function(t,r,n){return t+(r-t)*n},vf=function(t){var r=t.from,n=t.to;return r!==n},ST=function e(t,r,n){var a=En(function(i,o){if(vf(o)){var u=t(o.from,o.to,o.velocity),c=wT(u,2),s=c[0],f=c[1];return je(je({},o),{},{from:s,velocity:f})}return o},r);return n<1?En(function(i,o){return vf(o)?je(je({},o),{},{velocity:vi(o.velocity,a[i].velocity,n),from:vi(o.from,a[i].from,n)}):o},r):e(t,a,n-1)};const PT=(function(e,t,r,n,a){var i=rT(e,t),o=i.reduce(function(p,x){return je(je({},p),{},df({},x,[e[x],t[x]]))},{}),u=i.reduce(function(p,x){return je(je({},p),{},df({},x,{from:e[x],velocity:0,to:t[x]}))},{}),c=-1,s,f,l=function(){return null},h=function(){return En(function(x,O){return O.from},u)},d=function(){return!Object.values(u).filter(vf).length},y=function(x){s||(s=x);var O=x-s,w=O/r.dt;u=ST(r,u,w),a(je(je(je({},e),t),h())),s=x,d()||(c=requestAnimationFrame(l))},v=function(x){f||(f=x);var O=(x-f)/n,w=En(function(m,b){return vi.apply(void 0,E1(b).concat([r(O)]))},o);if(a(je(je(je({},e),t),w)),O<1)c=requestAnimationFrame(l);else{var A=En(function(m,b){return vi.apply(void 0,E1(b).concat([r(1)]))},o);a(je(je(je({},e),t),A))}};return l=r.isStepper?y:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}});function Hr(e){"@babel/helpers - typeof";return Hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hr(e)}var ET=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function jT(e,t){if(e==null)return{};var r=TT(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function TT(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}function Ks(e){return IT(e)||CT(e)||$T(e)||MT()}function MT(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $T(e,t){if(e){if(typeof e=="string")return yf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yf(e,t)}}function CT(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function IT(e){if(Array.isArray(e))return yf(e)}function yf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function T1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function rt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?T1(Object(r),!0).forEach(function(n){An(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function An(e,t,r){return t=$2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function RT(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,$2(n.key),n)}}function DT(e,t,r){return t&&RT(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function $2(e){var t=NT(e,"string");return Hr(t)==="symbol"?t:String(t)}function NT(e,t){if(Hr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function LT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mf(e,t)}function mf(e,t){return mf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},mf(e,t)}function qT(e){var t=BT();return function(){var n=yi(e),a;if(t){var i=yi(this).constructor;a=Reflect.construct(n,arguments,i)}else a=n.apply(this,arguments);return gf(this,a)}}function gf(e,t){if(t&&(Hr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return bf(e)}function bf(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function BT(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function yi(e){return yi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},yi(e)}var jt=(function(e){LT(r,e);var t=qT(r);function r(n,a){var i;kT(this,r),i=t.call(this,n,a);var o=i.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,h=o.children,d=o.duration;if(i.handleStyleChange=i.handleStyleChange.bind(bf(i)),i.changeStyle=i.changeStyle.bind(bf(i)),!u||d<=0)return i.state={style:{}},typeof h=="function"&&(i.state={style:f}),gf(i);if(l&&l.length)i.state={style:l[0].style};else if(s){if(typeof h=="function")return i.state={style:s},gf(i);i.state={style:c?An({},c,s):s}}else i.state={style:{}};return i}return DT(r,[{key:"componentDidMount",value:function(){var a=this.props,i=a.isActive,o=a.canBegin;this.mounted=!0,!(!i||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(a){var i=this.props,o=i.isActive,u=i.canBegin,c=i.attributeName,s=i.shouldReAnimate,f=i.to,l=i.from,h=this.state.style;if(u){if(!o){var d={style:c?An({},c,f):f};this.state&&h&&(c&&h[c]!==f||!c&&h!==f)&&this.setState(d);return}if(!(Wj(a.to,f)&&a.canBegin&&a.isActive)){var y=!a.canBegin||!a.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=y||s?l:a.to;if(this.state&&h){var p={style:c?An({},c,v):v};(c&&h[c]!==v||!c&&h!==v)&&this.setState(p)}this.runAnimation(rt(rt({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var a=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),a&&a()}},{key:"handleStyleChange",value:function(a){this.changeStyle(a)}},{key:"changeStyle",value:function(a){this.mounted&&this.setState({style:a})}},{key:"runJSAnimation",value:function(a){var i=this,o=a.from,u=a.to,c=a.duration,s=a.easing,f=a.begin,l=a.onAnimationEnd,h=a.onAnimationStart,d=PT(o,u,vT(s),c,this.changeStyle),y=function(){i.stopJSAnimation=d()};this.manager.start([h,f,y,c,l])}},{key:"runStepAnimation",value:function(a){var i=this,o=a.steps,u=a.begin,c=a.onAnimationStart,s=o[0],f=s.style,l=s.duration,h=l===void 0?0:l,d=function(v,p,x){if(x===0)return v;var O=p.duration,w=p.easing,A=w===void 0?"ease":w,m=p.style,b=p.properties,_=p.onAnimationEnd,S=x>0?o[x-1]:p,j=b||Object.keys(m);if(typeof A=="function"||A==="spring")return[].concat(Ks(v),[i.runJSAnimation.bind(i,{from:S.style,to:m,duration:O,easing:A}),O]);var C=_1(j,O,A),E=rt(rt(rt({},S.style),m),{},{transition:C});return[].concat(Ks(v),[E,O,_]).filter(nT)};return this.manager.start([c].concat(Ks(o.reduce(d,[f,Math.max(h,u)])),[a.onAnimationEnd]))}},{key:"runAnimation",value:function(a){this.manager||(this.manager=Qj());var i=a.begin,o=a.duration,u=a.attributeName,c=a.to,s=a.easing,f=a.onAnimationStart,l=a.onAnimationEnd,h=a.steps,d=a.children,y=this.manager;if(this.unSubscribe=y.subscribe(this.handleStyleChange),typeof s=="function"||typeof d=="function"||s==="spring"){this.runJSAnimation(a);return}if(h.length>1){this.runStepAnimation(a);return}var v=u?An({},u,c):c,p=_1(Object.keys(v),o,s);y.start([f,i,rt(rt({},v),{},{transition:p}),o,l])}},{key:"render",value:function(){var a=this.props,i=a.children;a.begin;var o=a.duration;a.attributeName,a.easing;var u=a.isActive;a.steps,a.from,a.to,a.canBegin,a.onAnimationEnd,a.shouldReAnimate,a.onAnimationReStart;var c=jT(a,ET),s=g.Children.count(i),f=this.state.style;if(typeof i=="function")return i(f);if(!u||s===0||o<=0)return i;var l=function(d){var y=d.props,v=y.style,p=v===void 0?{}:v,x=y.className,O=g.cloneElement(d,rt(rt({},c),{},{style:rt(rt({},p),f),className:x}));return O};return s===1?l(g.Children.only(i)):P.createElement("div",null,g.Children.map(i,function(h){return l(h)}))}}]),r})(g.PureComponent);jt.displayName="Animate";jt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};jt.propTypes={from:ne.oneOfType([ne.object,ne.string]),to:ne.oneOfType([ne.object,ne.string]),attributeName:ne.string,duration:ne.number,begin:ne.number,easing:ne.oneOfType([ne.string,ne.func]),steps:ne.arrayOf(ne.shape({duration:ne.number.isRequired,style:ne.object.isRequired,easing:ne.oneOfType([ne.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ne.func]),properties:ne.arrayOf("string"),onAnimationEnd:ne.func})),children:ne.oneOfType([ne.node,ne.func]),isActive:ne.bool,canBegin:ne.bool,onAnimationEnd:ne.func,shouldReAnimate:ne.bool,onAnimationStart:ne.func,onAnimationReStart:ne.func};function ea(e){"@babel/helpers - typeof";return ea=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ea(e)}function mi(){return mi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},mi.apply(this,arguments)}function HT(e,t){return ZT(e)||zT(e,t)||VT(e,t)||FT()}function FT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function VT(e,t){if(e){if(typeof e=="string")return M1(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return M1(e,t)}}function M1(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function zT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function ZT(e){if(Array.isArray(e))return e}function $1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function C1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$1(Object(r),!0).forEach(function(n){WT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function WT(e,t,r){return t=UT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function UT(e){var t=GT(e,"string");return ea(t)=="symbol"?t:t+""}function GT(e,t){if(ea(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ea(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var I1=function(t,r,n,a,i){var o=Math.min(Math.abs(n)/2,Math.abs(a)/2),u=a>=0?1:-1,c=n>=0?1:-1,s=a>=0&&n>=0||a<0&&n<0?1:0,f;if(o>0&&i instanceof Array){for(var l=[0,0,0,0],h=0,d=4;h<d;h++)l[h]=i[h]>o?o:i[h];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+a-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+a)),f+="L ".concat(t+c*l[3],",").concat(r+a),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+a-u*l[3])),f+="Z"}else if(o>0&&i===+i&&i>0){var y=Math.min(o,i);f="M ".concat(t,",").concat(r+u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+c*y,",").concat(r,`
            L `).concat(t+n-c*y,",").concat(r,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*y,`
            L `).concat(t+n,",").concat(r+a-u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n-c*y,",").concat(r+a,`
            L `).concat(t+c*y,",").concat(r+a,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t,",").concat(r+a-u*y," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(a," h ").concat(-n," Z");return f},KT=function(t,r){if(!t||!r)return!1;var n=t.x,a=t.y,i=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(i,i+u),f=Math.max(i,i+u),l=Math.min(o,o+c),h=Math.max(o,o+c);return n>=s&&n<=f&&a>=l&&a<=h}return!1},XT={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Zh=function(t){var r=C1(C1({},XT),t),n=g.useRef(),a=g.useState(-1),i=HT(a,2),o=i[0],u=i[1];g.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var A=n.current.getTotalLength();A&&u(A)}catch{}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,h=r.radius,d=r.className,y=r.animationEasing,v=r.animationDuration,p=r.animationBegin,x=r.isAnimationActive,O=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var w=Q("recharts-rectangle",d);return O?P.createElement(jt,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:v,animationEasing:y,isActive:O},function(A){var m=A.width,b=A.height,_=A.x,S=A.y;return P.createElement(jt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:v,isActive:x,easing:y},P.createElement("path",mi({},K(r,!0),{className:w,d:I1(_,S,m,b,h),ref:n})))}):P.createElement("path",mi({},K(r,!0),{className:w,d:I1(c,s,f,l,h)}))},YT=["points","className","baseLinePoints","connectNulls"];function _r(){return _r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},_r.apply(this,arguments)}function JT(e,t){if(e==null)return{};var r=QT(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function QT(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function k1(e){return nM(e)||rM(e)||tM(e)||eM()}function eM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function tM(e,t){if(e){if(typeof e=="string")return xf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xf(e,t)}}function rM(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function nM(e){if(Array.isArray(e))return xf(e)}function xf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var R1=function(t){return t&&t.x===+t.x&&t.y===+t.y},aM=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return t.forEach(function(n){R1(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),R1(t[0])&&r[r.length-1].push(t[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},jn=function(t,r){var n=aM(t);r&&(n=[n.reduce(function(i,o){return[].concat(k1(i),k1(o))},[])]);var a=n.map(function(i){return i.reduce(function(o,u,c){return"".concat(o).concat(c===0?"M":"L").concat(u.x,",").concat(u.y)},"")}).join("");return n.length===1?"".concat(a,"Z"):a},iM=function(t,r,n){var a=jn(t,n);return"".concat(a.slice(-1)==="Z"?a.slice(0,-1):a,"L").concat(jn(r.reverse(),n).slice(1))},oM=function(t){var r=t.points,n=t.className,a=t.baseLinePoints,i=t.connectNulls,o=JT(t,YT);if(!r||!r.length)return null;var u=Q("recharts-polygon",n);if(a&&a.length){var c=o.stroke&&o.stroke!=="none",s=iM(r,a,i);return P.createElement("g",{className:u},P.createElement("path",_r({},K(o,!0),{fill:s.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:s})),c?P.createElement("path",_r({},K(o,!0),{fill:"none",d:jn(r,i)})):null,c?P.createElement("path",_r({},K(o,!0),{fill:"none",d:jn(a,i)})):null)}var f=jn(r,i);return P.createElement("path",_r({},K(o,!0),{fill:f.slice(-1)==="Z"?o.fill:"none",className:u,d:f}))};function wf(){return wf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},wf.apply(this,arguments)}var Wh=function(t){var r=t.cx,n=t.cy,a=t.r,i=t.className,o=Q("recharts-dot",i);return r===+r&&n===+n&&a===+a?P.createElement("circle",wf({},K(t,!1),Ba(t),{className:o,cx:r,cy:n,r:a})):null};function ta(e){"@babel/helpers - typeof";return ta=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ta(e)}var uM=["x","y","top","left","width","height","className"];function Of(){return Of=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Of.apply(this,arguments)}function D1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function cM(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?D1(Object(r),!0).forEach(function(n){sM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):D1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sM(e,t,r){return t=lM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lM(e){var t=fM(e,"string");return ta(t)=="symbol"?t:t+""}function fM(e,t){if(ta(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ta(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function hM(e,t){if(e==null)return{};var r=dM(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function dM(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var pM=function(t,r,n,a,i,o){return"M".concat(t,",").concat(i,"v").concat(a,"M").concat(o,",").concat(r,"h").concat(n)},vM=function(t){var r=t.x,n=r===void 0?0:r,a=t.y,i=a===void 0?0:a,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,h=t.height,d=h===void 0?0:h,y=t.className,v=hM(t,uM),p=cM({x:n,y:i,top:u,left:s,width:l,height:d},v);return!B(n)||!B(i)||!B(l)||!B(d)||!B(u)||!B(s)?null:P.createElement("path",Of({},K(p,!0),{className:Q("recharts-cross",y),d:pM(n,i,l,d,u,s)}))},Xs,N1;function yM(){if(N1)return Xs;N1=1;var e=ro(),t=Zb(),r=vt();function n(a,i){return a&&a.length?e(a,r(i,2),t):void 0}return Xs=n,Xs}var mM=yM();const gM=se(mM);var Ys,L1;function bM(){if(L1)return Ys;L1=1;var e=ro(),t=vt(),r=Wb();function n(a,i){return a&&a.length?e(a,t(i,2),r):void 0}return Ys=n,Ys}var xM=bM();const wM=se(xM);var OM=["cx","cy","angle","ticks","axisLine"],AM=["ticks","tick","angle","tickFormatter","stroke"];function Fr(e){"@babel/helpers - typeof";return Fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(e)}function Tn(){return Tn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Tn.apply(this,arguments)}function q1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Kt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?q1(Object(r),!0).forEach(function(n){so(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):q1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function B1(e,t){if(e==null)return{};var r=_M(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function _M(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function SM(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function H1(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,I2(n.key),n)}}function PM(e,t,r){return t&&H1(e.prototype,t),r&&H1(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function EM(e,t,r){return t=gi(t),jM(e,C2()?Reflect.construct(t,r||[],gi(e).constructor):t.apply(e,r))}function jM(e,t){if(t&&(Fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return TM(e)}function TM(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function C2(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(C2=function(){return!!e})()}function gi(e){return gi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},gi(e)}function MM(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Af(e,t)}function Af(e,t){return Af=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Af(e,t)}function so(e,t,r){return t=I2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function I2(e){var t=$M(e,"string");return Fr(t)=="symbol"?t:t+""}function $M(e,t){if(Fr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var lo=(function(e){function t(){return SM(this,t),EM(this,t,arguments)}return MM(t,e),PM(t,[{key:"getTickValueCoord",value:function(n){var a=n.coordinate,i=this.props,o=i.angle,u=i.cx,c=i.cy;return pe(u,c,a,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,a;switch(n){case"left":a="end";break;case"right":a="start";break;default:a="middle";break}return a}},{key:"getViewBox",value:function(){var n=this.props,a=n.cx,i=n.cy,o=n.angle,u=n.ticks,c=gM(u,function(f){return f.coordinate||0}),s=wM(u,function(f){return f.coordinate||0});return{cx:a,cy:i,startAngle:o,endAngle:o,innerRadius:s.coordinate||0,outerRadius:c.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,a=n.cx,i=n.cy,o=n.angle,u=n.ticks,c=n.axisLine,s=B1(n,OM),f=u.reduce(function(y,v){return[Math.min(y[0],v.coordinate),Math.max(y[1],v.coordinate)]},[1/0,-1/0]),l=pe(a,i,f[0],o),h=pe(a,i,f[1],o),d=Kt(Kt(Kt({},K(s,!1)),{},{fill:"none"},K(c,!1)),{},{x1:l.x,y1:l.y,x2:h.x,y2:h.y});return P.createElement("line",Tn({className:"recharts-polar-radius-axis-line"},d))}},{key:"renderTicks",value:function(){var n=this,a=this.props,i=a.ticks,o=a.tick,u=a.angle,c=a.tickFormatter,s=a.stroke,f=B1(a,AM),l=this.getTickTextAnchor(),h=K(f,!1),d=K(o,!1),y=i.map(function(v,p){var x=n.getTickValueCoord(v),O=Kt(Kt(Kt(Kt({textAnchor:l,transform:"rotate(".concat(90-u,", ").concat(x.x,", ").concat(x.y,")")},h),{},{stroke:"none",fill:s},d),{},{index:p},x),{},{payload:v});return P.createElement(ue,Tn({className:Q("recharts-polar-radius-axis-tick",w2(o)),key:"tick-".concat(v.coordinate)},Bt(n.props,v,p)),t.renderTickItem(o,O,c?c(v.value,p):v.value))});return P.createElement(ue,{className:"recharts-polar-radius-axis-ticks"},y)}},{key:"render",value:function(){var n=this.props,a=n.ticks,i=n.axisLine,o=n.tick;return!a||!a.length?null:P.createElement(ue,{className:Q("recharts-polar-radius-axis",this.props.className)},i&&this.renderAxisLine(),o&&this.renderTicks(),Te.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,a,i){var o;return P.isValidElement(n)?o=P.cloneElement(n,a):X(n)?o=n(a):o=P.createElement(Dr,Tn({},a,{className:"recharts-polar-radius-axis-tick-value"}),i),o}}])})(g.PureComponent);so(lo,"displayName","PolarRadiusAxis");so(lo,"axisType","radiusAxis");so(lo,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function Vr(e){"@babel/helpers - typeof";return Vr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vr(e)}function Qt(){return Qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qt.apply(this,arguments)}function F1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Xt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?F1(Object(r),!0).forEach(function(n){fo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):F1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function CM(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function V1(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,R2(n.key),n)}}function IM(e,t,r){return t&&V1(e.prototype,t),r&&V1(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function kM(e,t,r){return t=bi(t),RM(e,k2()?Reflect.construct(t,r||[],bi(e).constructor):t.apply(e,r))}function RM(e,t){if(t&&(Vr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return DM(e)}function DM(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function k2(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(k2=function(){return!!e})()}function bi(e){return bi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},bi(e)}function NM(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_f(e,t)}function _f(e,t){return _f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},_f(e,t)}function fo(e,t,r){return t=R2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function R2(e){var t=LM(e,"string");return Vr(t)=="symbol"?t:t+""}function LM(e,t){if(Vr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Vr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var qM=Math.PI/180,z1=1e-5,ho=(function(e){function t(){return CM(this,t),kM(this,t,arguments)}return NM(t,e),IM(t,[{key:"getTickLineCoord",value:function(n){var a=this.props,i=a.cx,o=a.cy,u=a.radius,c=a.orientation,s=a.tickSize,f=s||8,l=pe(i,o,u,n.coordinate),h=pe(i,o,u+(c==="inner"?-1:1)*f,n.coordinate);return{x1:l.x,y1:l.y,x2:h.x,y2:h.y}}},{key:"getTickTextAnchor",value:function(n){var a=this.props.orientation,i=Math.cos(-n.coordinate*qM),o;return i>z1?o=a==="outer"?"start":"end":i<-z1?o=a==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,a=n.cx,i=n.cy,o=n.radius,u=n.axisLine,c=n.axisLineType,s=Xt(Xt({},K(this.props,!1)),{},{fill:"none"},K(u,!1));if(c==="circle")return P.createElement(Wh,Qt({className:"recharts-polar-angle-axis-line"},s,{cx:a,cy:i,r:o}));var f=this.props.ticks,l=f.map(function(h){return pe(a,i,o,h.coordinate)});return P.createElement(oM,Qt({className:"recharts-polar-angle-axis-line"},s,{points:l}))}},{key:"renderTicks",value:function(){var n=this,a=this.props,i=a.ticks,o=a.tick,u=a.tickLine,c=a.tickFormatter,s=a.stroke,f=K(this.props,!1),l=K(o,!1),h=Xt(Xt({},f),{},{fill:"none"},K(u,!1)),d=i.map(function(y,v){var p=n.getTickLineCoord(y),x=n.getTickTextAnchor(y),O=Xt(Xt(Xt({textAnchor:x},f),{},{stroke:"none",fill:s},l),{},{index:v,payload:y,x:p.x2,y:p.y2});return P.createElement(ue,Qt({className:Q("recharts-polar-angle-axis-tick",w2(o)),key:"tick-".concat(y.coordinate)},Bt(n.props,y,v)),u&&P.createElement("line",Qt({className:"recharts-polar-angle-axis-tick-line"},h,p)),o&&t.renderTickItem(o,O,c?c(y.value,v):y.value))});return P.createElement(ue,{className:"recharts-polar-angle-axis-ticks"},d)}},{key:"render",value:function(){var n=this.props,a=n.ticks,i=n.radius,o=n.axisLine;return i<=0||!a||!a.length?null:P.createElement(ue,{className:Q("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,a,i){var o;return P.isValidElement(n)?o=P.cloneElement(n,a):X(n)?o=n(a):o=P.createElement(Dr,Qt({},a,{className:"recharts-polar-angle-axis-tick-value"}),i),o}}])})(g.PureComponent);fo(ho,"displayName","PolarAngleAxis");fo(ho,"axisType","angleAxis");fo(ho,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var Js,Z1;function BM(){if(Z1)return Js;Z1=1;var e=zg(),t=e(Object.getPrototypeOf,Object);return Js=t,Js}var Qs,W1;function HM(){if(W1)return Qs;W1=1;var e=Tt(),t=BM(),r=Mt(),n="[object Object]",a=Function.prototype,i=Object.prototype,o=a.toString,u=i.hasOwnProperty,c=o.call(Object);function s(f){if(!r(f)||e(f)!=n)return!1;var l=t(f);if(l===null)return!0;var h=u.call(l,"constructor")&&l.constructor;return typeof h=="function"&&h instanceof h&&o.call(h)==c}return Qs=s,Qs}var FM=HM();const VM=se(FM);var el,U1;function zM(){if(U1)return el;U1=1;var e=Tt(),t=Mt(),r="[object Boolean]";function n(a){return a===!0||a===!1||t(a)&&e(a)==r}return el=n,el}var ZM=zM();const WM=se(ZM);function ra(e){"@babel/helpers - typeof";return ra=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ra(e)}function xi(){return xi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xi.apply(this,arguments)}function UM(e,t){return YM(e)||XM(e,t)||KM(e,t)||GM()}function GM(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function KM(e,t){if(e){if(typeof e=="string")return G1(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return G1(e,t)}}function G1(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function XM(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function YM(e){if(Array.isArray(e))return e}function K1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function X1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?K1(Object(r),!0).forEach(function(n){JM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function JM(e,t,r){return t=QM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function QM(e){var t=e3(e,"string");return ra(t)=="symbol"?t:t+""}function e3(e,t){if(ra(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ra(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Y1=function(t,r,n,a,i){var o=n-a,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+i),u+="L ".concat(t+n-o/2-a,",").concat(r+i),u+="L ".concat(t,",").concat(r," Z"),u},t3={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},r3=function(t){var r=X1(X1({},t3),t),n=g.useRef(),a=g.useState(-1),i=UM(a,2),o=i[0],u=i[1];g.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var w=n.current.getTotalLength();w&&u(w)}catch{}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,h=r.height,d=r.className,y=r.animationEasing,v=r.animationDuration,p=r.animationBegin,x=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||h!==+h||f===0&&l===0||h===0)return null;var O=Q("recharts-trapezoid",d);return x?P.createElement(jt,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:h,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:h,x:c,y:s},duration:v,animationEasing:y,isActive:x},function(w){var A=w.upperWidth,m=w.lowerWidth,b=w.height,_=w.x,S=w.y;return P.createElement(jt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:v,easing:y},P.createElement("path",xi({},K(r,!0),{className:O,d:Y1(_,S,A,m,b),ref:n})))}):P.createElement("g",null,P.createElement("path",xi({},K(r,!0),{className:O,d:Y1(c,s,f,l,h)})))},n3=["option","shapeType","propTransformer","activeClassName","isActive"];function na(e){"@babel/helpers - typeof";return na=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},na(e)}function a3(e,t){if(e==null)return{};var r=i3(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function i3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function J1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function wi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?J1(Object(r),!0).forEach(function(n){o3(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):J1(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function o3(e,t,r){return t=u3(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u3(e){var t=c3(e,"string");return na(t)=="symbol"?t:t+""}function c3(e,t){if(na(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(na(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function s3(e,t){return wi(wi({},t),e)}function l3(e,t){return e==="symbols"}function Q1(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return P.createElement(Zh,r);case"trapezoid":return P.createElement(r3,r);case"sector":return P.createElement(_2,r);case"symbols":if(l3(t))return P.createElement(ch,r);break;default:return null}}function f3(e){return g.isValidElement(e)?e.props:e}function D2(e){var t=e.option,r=e.shapeType,n=e.propTransformer,a=n===void 0?s3:n,i=e.activeClassName,o=i===void 0?"recharts-active-shape":i,u=e.isActive,c=a3(e,n3),s;if(g.isValidElement(t))s=g.cloneElement(t,wi(wi({},c),f3(t)));else if(X(t))s=t(c);else if(VM(t)&&!WM(t)){var f=a(t,c);s=P.createElement(Q1,{shapeType:r,elementProps:f})}else{var l=c;s=P.createElement(Q1,{shapeType:r,elementProps:l})}return u?P.createElement(ue,{className:o},s):s}function po(e,t){return t!=null&&"trapezoids"in e.props}function vo(e,t){return t!=null&&"sectors"in e.props}function aa(e,t){return t!=null&&"points"in e.props}function h3(e,t){var r,n,a=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,i=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return a&&i}function d3(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function p3(e,t){var r=e.x===t.x,n=e.y===t.y,a=e.z===t.z;return r&&n&&a}function v3(e,t){var r;return po(e,t)?r=h3:vo(e,t)?r=d3:aa(e,t)&&(r=p3),r}function y3(e,t){var r;return po(e,t)?r="trapezoids":vo(e,t)?r="sectors":aa(e,t)&&(r="points"),r}function m3(e,t){if(po(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(vo(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return aa(e,t)?t.payload:{}}function g3(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,a=y3(r,t),i=m3(r,t),o=n.filter(function(c,s){var f=io(i,c),l=r.props[a].filter(function(y){var v=v3(r,t);return v(y,t)}),h=r.props[a].indexOf(l[l.length-1]),d=s===h;return f&&d}),u=n.indexOf(o[o.length-1]);return u}function ia(e){"@babel/helpers - typeof";return ia=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ia(e)}function Sf(){return Sf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sf.apply(this,arguments)}function em(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function tl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?em(Object(r),!0).forEach(function(n){b3(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):em(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function b3(e,t,r){return t=x3(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function x3(e){var t=w3(e,"string");return ia(t)=="symbol"?t:t+""}function w3(e,t){if(ia(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ia(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function tm(e){return typeof e=="string"?parseInt(e,10):e}function O3(e,t){var r="".concat(t.cx||e.cx),n=Number(r),a="".concat(t.cy||e.cy),i=Number(a);return tl(tl(tl({},t),e),{},{cx:n,cy:i})}function rm(e){return P.createElement(D2,Sf({shapeType:"sector",propTransformer:O3},e))}var A3=["shape","activeShape","activeIndex","cornerRadius"],_3=["value","background"];function zr(e){"@babel/helpers - typeof";return zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zr(e)}function Oi(){return Oi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Oi.apply(this,arguments)}function nm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Se(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?nm(Object(r),!0).forEach(function(n){ur(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function am(e,t){if(e==null)return{};var r=S3(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function S3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function P3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function im(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,L2(n.key),n)}}function E3(e,t,r){return t&&im(e.prototype,t),r&&im(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function j3(e,t,r){return t=Ai(t),T3(e,N2()?Reflect.construct(t,r||[],Ai(e).constructor):t.apply(e,r))}function T3(e,t){if(t&&(zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return M3(e)}function M3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function N2(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(N2=function(){return!!e})()}function Ai(e){return Ai=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ai(e)}function $3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pf(e,t)}function Pf(e,t){return Pf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Pf(e,t)}function ur(e,t,r){return t=L2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function L2(e){var t=C3(e,"string");return zr(t)=="symbol"?t:t+""}function C3(e,t){if(zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var _a=(function(e){function t(){var r;P3(this,t);for(var n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return r=j3(this,t,[].concat(a)),ur(r,"state",{isAnimationFinished:!1}),ur(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),X(o)&&o()}),ur(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),X(o)&&o()}),r}return $3(t,e),E3(t,[{key:"getDeltaAngle",value:function(){var n=this.props,a=n.startAngle,i=n.endAngle,o=Ne(i-a),u=Math.min(Math.abs(i-a),360);return o*u}},{key:"renderSectorsStatically",value:function(n){var a=this,i=this.props,o=i.shape,u=i.activeShape,c=i.activeIndex,s=i.cornerRadius,f=am(i,A3),l=K(f,!1);return n.map(function(h,d){var y=d===c,v=Se(Se(Se(Se({},l),{},{cornerRadius:tm(s)},h),Bt(a.props,h,d)),{},{className:"recharts-radial-bar-sector ".concat(h.className),forceCornerRadius:f.forceCornerRadius,cornerIsExternal:f.cornerIsExternal,isActive:y,option:y?u:o});return P.createElement(rm,Oi({},v,{key:"sector-".concat(d)}))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,a=this.props,i=a.data,o=a.isAnimationActive,u=a.animationBegin,c=a.animationDuration,s=a.animationEasing,f=a.animationId,l=this.state.prevData;return P.createElement(jt,{begin:u,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},key:"radialBar-".concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(h){var d=h.t,y=i.map(function(v,p){var x=l&&l[p];if(x){var O=gt(x.startAngle,v.startAngle),w=gt(x.endAngle,v.endAngle);return Se(Se({},v),{},{startAngle:O(d),endAngle:w(d)})}var A=v.endAngle,m=v.startAngle,b=gt(m,A);return Se(Se({},v),{},{endAngle:b(d)})});return P.createElement(ue,null,n.renderSectorsStatically(y))})}},{key:"renderSectors",value:function(){var n=this.props,a=n.data,i=n.isAnimationActive,o=this.state.prevData;return i&&a&&a.length&&(!o||!io(o,a))?this.renderSectorsWithAnimation():this.renderSectorsStatically(a)}},{key:"renderBackground",value:function(n){var a=this,i=this.props.cornerRadius,o=K(this.props.background,!1);return n.map(function(u,c){u.value;var s=u.background,f=am(u,_3);if(!s)return null;var l=Se(Se(Se(Se(Se({cornerRadius:tm(i)},f),{},{fill:"#eee"},s),o),Bt(a.props,u,c)),{},{index:c,className:Q("recharts-radial-bar-background-sector",o==null?void 0:o.className),option:s,isActive:!1});return P.createElement(rm,Oi({},l,{key:"sector-".concat(c)}))})}},{key:"render",value:function(){var n=this.props,a=n.hide,i=n.data,o=n.className,u=n.background,c=n.isAnimationActive;if(a||!i||!i.length)return null;var s=this.state.isAnimationFinished,f=Q("recharts-area",o);return P.createElement(ue,{className:f},u&&P.createElement(ue,{className:"recharts-radial-bar-background"},this.renderBackground(i)),P.createElement(ue,{className:"recharts-radial-bar-sectors"},this.renderSectors()),(!c||s)&&qt.renderCallByParent(Se({},this.props),i))}}],[{key:"getDerivedStateFromProps",value:function(n,a){return n.animationId!==a.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:a.curData}:n.data!==a.curData?{curData:n.data}:null}}])})(g.PureComponent);ur(_a,"displayName","RadialBar");ur(_a,"defaultProps",{angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!un.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1});ur(_a,"getComposedData",function(e){var t=e.item,r=e.props,n=e.radiusAxis,a=e.radiusAxisTicks,i=e.angleAxis,o=e.angleAxisTicks,u=e.displayedData,c=e.dataKey,s=e.stackedData,f=e.barPosition,l=e.bandSize,h=e.dataStartIndex,d=v2(f,t);if(!d)return null;var y=i.cx,v=i.cy,p=r.layout,x=t.props,O=x.children,w=x.minPointSize,A=p==="radial"?i:n,m=s?A.scale.domain():null,b=g2({numericAxis:A}),_=Qe(O,gh),S=u.map(function(j,C){var E,M,$,k,I,D;if(s?E=y2(s[h+C],m):(E=ot(j,c),Array.isArray(E)||(E=[b,E])),p==="radial"){M=li({axis:n,ticks:a,bandSize:l,offset:d.offset,entry:j,index:C}),I=i.scale(E[1]),k=i.scale(E[0]),$=M+d.size;var N=I-k;if(Math.abs(w)>0&&Math.abs(N)<Math.abs(w)){var q=Ne(N||w)*(Math.abs(w)-Math.abs(N));I+=q}D={background:{cx:y,cy:v,innerRadius:M,outerRadius:$,startAngle:r.startAngle,endAngle:r.endAngle}}}else{M=n.scale(E[0]),$=n.scale(E[1]),k=li({axis:i,ticks:o,bandSize:l,offset:d.offset,entry:j,index:C}),I=k+d.size;var H=$-M;if(Math.abs(w)>0&&Math.abs(H)<Math.abs(w)){var Z=Ne(H||w)*(Math.abs(w)-Math.abs(H));$+=Z}}return Se(Se(Se(Se({},j),D),{},{payload:j,value:s?E:E[1],cx:y,cy:v,innerRadius:M,outerRadius:$,startAngle:k,endAngle:I},_&&_[C]&&_[C].props),{},{tooltipPayload:[zh(t,j)],tooltipPosition:pe(y,v,(M+$)/2,(k+I)/2)})});return{data:S,layout:p}});var rl,om;function I3(){if(om)return rl;om=1;var e=Math.ceil,t=Math.max;function r(n,a,i,o){for(var u=-1,c=t(e((a-n)/(i||1)),0),s=Array(c);c--;)s[o?c:++u]=n,n+=i;return s}return rl=r,rl}var nl,um;function q2(){if(um)return nl;um=1;var e=ob(),t=1/0,r=17976931348623157e292;function n(a){if(!a)return a===0?a:0;if(a=e(a),a===t||a===-t){var i=a<0?-1:1;return i*r}return a===a?a:0}return nl=n,nl}var al,cm;function k3(){if(cm)return al;cm=1;var e=I3(),t=Ki(),r=q2();function n(a){return function(i,o,u){return u&&typeof u!="number"&&t(i,o,u)&&(o=u=void 0),i=r(i),o===void 0?(o=i,i=0):o=r(o),u=u===void 0?i<o?1:-1:r(u),e(i,o,u,a)}}return al=n,al}var il,sm;function R3(){if(sm)return il;sm=1;var e=k3(),t=e();return il=t,il}var D3=R3();const _i=se(D3);function oa(e){"@babel/helpers - typeof";return oa=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},oa(e)}function lm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function fm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?lm(Object(r),!0).forEach(function(n){B2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function B2(e,t,r){return t=N3(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function N3(e){var t=L3(e,"string");return oa(t)=="symbol"?t:t+""}function L3(e,t){if(oa(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(oa(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var q3=["Webkit","Moz","O","ms"],B3=function(t,r){var n=t.replace(/(\w)/,function(i){return i.toUpperCase()}),a=q3.reduce(function(i,o){return fm(fm({},i),{},B2({},o+n,r))},{});return a[t]=r,a};function Zr(e){"@babel/helpers - typeof";return Zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zr(e)}function Si(){return Si=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Si.apply(this,arguments)}function hm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function ol(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hm(Object(r),!0).forEach(function(n){He(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function dm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,F2(n.key),n)}}function F3(e,t,r){return t&&dm(e.prototype,t),r&&dm(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function V3(e,t,r){return t=Pi(t),z3(e,H2()?Reflect.construct(t,r||[],Pi(e).constructor):t.apply(e,r))}function z3(e,t){if(t&&(Zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Z3(e)}function Z3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function H2(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(H2=function(){return!!e})()}function Pi(e){return Pi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Pi(e)}function W3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ef(e,t)}function Ef(e,t){return Ef=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Ef(e,t)}function He(e,t,r){return t=F2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function F2(e){var t=U3(e,"string");return Zr(t)=="symbol"?t:t+""}function U3(e,t){if(Zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var G3=function(t){var r=t.data,n=t.startIndex,a=t.endIndex,i=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=Sn().domain(_i(0,c)).range([i,i+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(a),scale:s,scaleValues:f}},pm=function(t){return t.changedTouches&&!!t.changedTouches.length},Wr=(function(e){function t(r){var n;return H3(this,t),n=V3(this,t,[r]),He(n,"handleDrag",function(a){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(a):n.state.isSlideMoving&&n.handleSlideDrag(a)}),He(n,"handleTouchMove",function(a){a.changedTouches!=null&&a.changedTouches.length>0&&n.handleDrag(a.changedTouches[0])}),He(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var a=n.props,i=a.endIndex,o=a.onDragEnd,u=a.startIndex;o==null||o({endIndex:i,startIndex:u})}),n.detachDragEndListener()}),He(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),He(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),He(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),He(n,"handleSlideDragStart",function(a){var i=pm(a)?a.changedTouches[0]:a;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:i.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return W3(t,e),F3(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var a=n.startX,i=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(a,i),h=Math.max(a,i),d=t.getIndexInRange(o,l),y=t.getIndexInRange(o,h);return{startIndex:d-d%c,endIndex:y===f?f:y-y%c}}},{key:"getTextOfTick",value:function(n){var a=this.props,i=a.data,o=a.tickFormatter,u=a.dataKey,c=ot(i[n],u,n);return X(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var a=this.state,i=a.slideMoveStartX,o=a.startX,u=a.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,h=c.startIndex,d=c.endIndex,y=c.onChange,v=n.pageX-i;v>0?v=Math.min(v,s+f-l-u,s+f-l-o):v<0&&(v=Math.max(v,s-o,s-u));var p=this.getIndex({startX:o+v,endX:u+v});(p.startIndex!==h||p.endIndex!==d)&&y&&y(p),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,a){var i=pm(a)?a.changedTouches[0]:a;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:i.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var a=this.state,i=a.brushMoveStartX,o=a.movingTravellerId,u=a.endX,c=a.startX,s=this.state[o],f=this.props,l=f.x,h=f.width,d=f.travellerWidth,y=f.onChange,v=f.gap,p=f.data,x={startX:this.state.startX,endX:this.state.endX},O=n.pageX-i;O>0?O=Math.min(O,l+h-d-s):O<0&&(O=Math.max(O,l-s)),x[o]=s+O;var w=this.getIndex(x),A=w.startIndex,m=w.endIndex,b=function(){var S=p.length-1;return o==="startX"&&(u>c?A%v===0:m%v===0)||u<c&&m===S||o==="endX"&&(u>c?m%v===0:A%v===0)||u>c&&m===S};this.setState(He(He({},o,s+O),"brushMoveStartX",n.pageX),function(){y&&b()&&y(w)})}},{key:"handleTravellerMoveKeyboard",value:function(n,a){var i=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[a],l=u.indexOf(f);if(l!==-1){var h=l+n;if(!(h===-1||h>=u.length)){var d=u[h];a==="startX"&&d>=s||a==="endX"&&d<=c||this.setState(He({},a,d),function(){i.props.onChange(i.getIndex({startX:i.state.startX,endX:i.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,a=n.x,i=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return P.createElement("rect",{stroke:s,fill:c,x:a,y:i,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,a=n.x,i=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=g.Children.only(s);return l?P.cloneElement(l,{x:a,y:i,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,a){var i,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,h=c.traveller,d=c.ariaLabel,y=c.data,v=c.startIndex,p=c.endIndex,x=Math.max(n,this.props.x),O=ol(ol({},K(this.props,!1)),{},{x,y:s,width:f,height:l}),w=d||"Min value: ".concat((i=y[v])===null||i===void 0?void 0:i.name,", Max value: ").concat((o=y[p])===null||o===void 0?void 0:o.name);return P.createElement(ue,{tabIndex:0,role:"slider","aria-label":w,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[a],onTouchStart:this.travellerDragStartHandlers[a],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,a))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(h,O))}},{key:"renderSlide",value:function(n,a){var i=this.props,o=i.y,u=i.height,c=i.stroke,s=i.travellerWidth,f=Math.min(n,a)+s,l=Math.max(Math.abs(a-n)-s,0);return P.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,a=n.startIndex,i=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,h=f.endX,d=5,y={pointerEvents:"none",fill:s};return P.createElement(ue,{className:"recharts-brush-texts"},P.createElement(Dr,Si({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,h)-d,y:o+u/2},y),this.getTextOfTick(a)),P.createElement(Dr,Si({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,h)+c+d,y:o+u/2},y),this.getTextOfTick(i)))}},{key:"render",value:function(){var n=this.props,a=n.data,i=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,h=this.state,d=h.startX,y=h.endX,v=h.isTextActive,p=h.isSlideMoving,x=h.isTravellerMoving,O=h.isTravellerFocused;if(!a||!a.length||!B(u)||!B(c)||!B(s)||!B(f)||s<=0||f<=0)return null;var w=Q("recharts-brush",i),A=P.Children.count(o)===1,m=B3("userSelect","none");return P.createElement(ue,{className:w,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),A&&this.renderPanorama(),this.renderSlide(d,y),this.renderTravellerLayer(d,"startX"),this.renderTravellerLayer(y,"endX"),(v||p||x||O||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var a=n.x,i=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(i+u/2)-1;return P.createElement(P.Fragment,null,P.createElement("rect",{x:a,y:i,width:o,height:u,fill:c,stroke:"none"}),P.createElement("line",{x1:a+1,y1:s,x2:a+o-1,y2:s,fill:"none",stroke:"#fff"}),P.createElement("line",{x1:a+1,y1:s+2,x2:a+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,a){var i;return P.isValidElement(n)?i=P.cloneElement(n,a):X(n)?i=n(a):i=t.renderDefaultTraveller(a),i}},{key:"getDerivedStateFromProps",value:function(n,a){var i=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(i!==a.prevData||s!==a.prevUpdateId)return ol({prevData:i,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},i&&i.length?G3({data:i,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(a.scale&&(o!==a.prevWidth||u!==a.prevX||c!==a.prevTravellerWidth)){a.scale.range([u,u+o-c]);var h=a.scale.domain().map(function(d){return a.scale(d)});return{prevData:i,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:a.scale(n.startIndex),endX:a.scale(n.endIndex),scaleValues:h}}return null}},{key:"getIndexInRange",value:function(n,a){for(var i=n.length,o=0,u=i-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>a?u=c:o=c}return a>=n[u]?u:o}}])})(g.PureComponent);He(Wr,"displayName","Brush");He(Wr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var ul,vm;function K3(){if(vm)return ul;vm=1;var e=vh();function t(r,n){var a;return e(r,function(i,o,u){return a=n(i,o,u),!a}),!!a}return ul=t,ul}var cl,ym;function X3(){if(ym)return cl;ym=1;var e=Ng(),t=vt(),r=K3(),n=qe(),a=Ki();function i(o,u,c){var s=n(o)?e:r;return c&&a(o,u,c)&&(u=void 0),s(o,t(u,3))}return cl=i,cl}var Y3=X3();const J3=se(Y3);var ht=function(t,r){var n=t.alwaysShow,a=t.ifOverflow;return n&&(a="extendDomain"),a===r},sl,mm;function Q3(){if(mm)return sl;mm=1;var e=tb();function t(r,n,a){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:a,writable:!0}):r[n]=a}return sl=t,sl}var ll,gm;function e$(){if(gm)return ll;gm=1;var e=Q3(),t=Qg(),r=vt();function n(a,i){var o={};return i=r(i,3),t(a,function(u,c,s){e(o,c,i(u,c,s))}),o}return ll=n,ll}var t$=e$();const r$=se(t$);var fl,bm;function n$(){if(bm)return fl;bm=1;function e(t,r){for(var n=-1,a=t==null?0:t.length;++n<a;)if(!r(t[n],n,t))return!1;return!0}return fl=e,fl}var hl,xm;function a$(){if(xm)return hl;xm=1;var e=vh();function t(r,n){var a=!0;return e(r,function(i,o,u){return a=!!n(i,o,u),a}),a}return hl=t,hl}var dl,wm;function i$(){if(wm)return dl;wm=1;var e=n$(),t=a$(),r=vt(),n=qe(),a=Ki();function i(o,u,c){var s=n(o)?e:t;return c&&a(o,u,c)&&(u=void 0),s(o,r(u,3))}return dl=i,dl}var o$=i$();const V2=se(o$);var u$=["x","y"];function ua(e){"@babel/helpers - typeof";return ua=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ua(e)}function jf(){return jf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jf.apply(this,arguments)}function Om(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function xn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Om(Object(r),!0).forEach(function(n){c$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Om(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function c$(e,t,r){return t=s$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s$(e){var t=l$(e,"string");return ua(t)=="symbol"?t:t+""}function l$(e,t){if(ua(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ua(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function f$(e,t){if(e==null)return{};var r=h$(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function h$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function d$(e,t){var r=e.x,n=e.y,a=f$(e,u$),i="".concat(r),o=parseInt(i,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||a.height),f=parseInt(s,10),l="".concat(t.width||a.width),h=parseInt(l,10);return xn(xn(xn(xn(xn({},t),a),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:h,name:t.name,radius:t.radius})}function Am(e){return P.createElement(D2,jf({shapeType:"rectangle",propTransformer:d$,activeClassName:"recharts-active-bar"},e))}var p$=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,a){if(typeof t=="number")return t;var i=typeof n=="number";return i?t(n,a):(i||lr(),r)}},v$=["value","background"],z2;function Ur(e){"@babel/helpers - typeof";return Ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ur(e)}function y$(e,t){if(e==null)return{};var r=m$(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function m$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ei(){return Ei=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ei.apply(this,arguments)}function _m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function ge(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_m(Object(r),!0).forEach(function(n){Dt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_m(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function g$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Sm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,W2(n.key),n)}}function b$(e,t,r){return t&&Sm(e.prototype,t),r&&Sm(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function x$(e,t,r){return t=ji(t),w$(e,Z2()?Reflect.construct(t,r||[],ji(e).constructor):t.apply(e,r))}function w$(e,t){if(t&&(Ur(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return O$(e)}function O$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Z2(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Z2=function(){return!!e})()}function ji(e){return ji=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ji(e)}function A$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tf(e,t)}function Tf(e,t){return Tf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Tf(e,t)}function Dt(e,t,r){return t=W2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function W2(e){var t=_$(e,"string");return Ur(t)=="symbol"?t:t+""}function _$(e,t){if(Ur(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ur(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var gr=(function(e){function t(){var r;g$(this,t);for(var n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return r=x$(this,t,[].concat(a)),Dt(r,"state",{isAnimationFinished:!1}),Dt(r,"id",Fi("recharts-bar-")),Dt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Dt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return A$(t,e),b$(t,[{key:"renderRectanglesStatically",value:function(n){var a=this,i=this.props,o=i.shape,u=i.dataKey,c=i.activeIndex,s=i.activeBar,f=K(this.props,!1);return n&&n.map(function(l,h){var d=h===c,y=d?s:o,v=ge(ge(ge({},f),l),{},{isActive:d,option:y,index:h,dataKey:u,onAnimationStart:a.handleAnimationStart,onAnimationEnd:a.handleAnimationEnd});return P.createElement(ue,Ei({className:"recharts-bar-rectangle"},Bt(a.props,l,h),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value,"-").concat(h)}),P.createElement(Am,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,a=this.props,i=a.data,o=a.layout,u=a.isAnimationActive,c=a.animationBegin,s=a.animationDuration,f=a.animationEasing,l=a.animationId,h=this.state.prevData;return P.createElement(jt,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(d){var y=d.t,v=i.map(function(p,x){var O=h&&h[x];if(O){var w=gt(O.x,p.x),A=gt(O.y,p.y),m=gt(O.width,p.width),b=gt(O.height,p.height);return ge(ge({},p),{},{x:w(y),y:A(y),width:m(y),height:b(y)})}if(o==="horizontal"){var _=gt(0,p.height),S=_(y);return ge(ge({},p),{},{y:p.y+p.height-S,height:S})}var j=gt(0,p.width),C=j(y);return ge(ge({},p),{},{width:C})});return P.createElement(ue,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,a=n.data,i=n.isAnimationActive,o=this.state.prevData;return i&&a&&a.length&&(!o||!io(o,a))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(a)}},{key:"renderBackground",value:function(){var n=this,a=this.props,i=a.data,o=a.dataKey,u=a.activeIndex,c=K(this.props.background,!1);return i.map(function(s,f){s.value;var l=s.background,h=y$(s,v$);if(!l)return null;var d=ge(ge(ge(ge(ge({},h),{},{fill:"#eee"},l),c),Bt(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return P.createElement(Am,Ei({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},d))})}},{key:"renderErrorBar",value:function(n,a){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,o=i.data,u=i.xAxis,c=i.yAxis,s=i.layout,f=i.children,l=Qe(f,co);if(!l)return null;var h=s==="vertical"?o[0].height/2:o[0].width/2,d=function(p,x){var O=Array.isArray(p.value)?p.value[1]:p.value;return{x:p.x,y:p.y,value:O,errorVal:ot(p,x)}},y={clipPath:n?"url(#clipPath-".concat(a,")"):null};return P.createElement(ue,y,l.map(function(v){return P.cloneElement(v,{key:"error-bar-".concat(a,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:h,dataPointFormatter:d})}))}},{key:"render",value:function(){var n=this.props,a=n.hide,i=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,h=n.height,d=n.isAnimationActive,y=n.background,v=n.id;if(a||!i||!i.length)return null;var p=this.state.isAnimationFinished,x=Q("recharts-bar",o),O=u&&u.allowDataOverflow,w=c&&c.allowDataOverflow,A=O||w,m=re(v)?this.id:v;return P.createElement(ue,{className:x},O||w?P.createElement("defs",null,P.createElement("clipPath",{id:"clipPath-".concat(m)},P.createElement("rect",{x:O?s:s-l/2,y:w?f:f-h/2,width:O?l:l*2,height:w?h:h*2}))):null,P.createElement(ue,{className:"recharts-bar-rectangles",clipPath:A?"url(#clipPath-".concat(m,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(A,m),(!d||p)&&qt.renderCallByParent(this.props,i))}}],[{key:"getDerivedStateFromProps",value:function(n,a){return n.animationId!==a.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:a.curData}:n.data!==a.curData?{curData:n.data}:null}}])})(g.PureComponent);z2=gr;Dt(gr,"displayName","Bar");Dt(gr,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!un.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Dt(gr,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,a=e.bandSize,i=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,h=e.offset,d=v2(n,r);if(!d)return null;var y=t.layout,v=r.type.defaultProps,p=v!==void 0?ge(ge({},v),r.props):r.props,x=p.dataKey,O=p.children,w=p.minPointSize,A=y==="horizontal"?o:i,m=s?A.scale.domain():null,b=g2({numericAxis:A}),_=Qe(O,gh),S=l.map(function(j,C){var E,M,$,k,I,D;s?E=y2(s[f+C],m):(E=ot(j,x),Array.isArray(E)||(E=[b,E]));var N=p$(w,z2.defaultProps.minPointSize)(E[1],C);if(y==="horizontal"){var q,H=[o.scale(E[0]),o.scale(E[1])],Z=H[0],U=H[1];M=li({axis:i,ticks:u,bandSize:a,offset:d.offset,entry:j,index:C}),$=(q=U??Z)!==null&&q!==void 0?q:void 0,k=d.size;var V=Z-U;if(I=Number.isNaN(V)?0:V,D={x:M,y:o.y,width:k,height:o.height},Math.abs(N)>0&&Math.abs(I)<Math.abs(N)){var G=Ne(I||N)*(Math.abs(N)-Math.abs(I));$-=G,I+=G}}else{var fe=[i.scale(E[0]),i.scale(E[1])],me=fe[0],Be=fe[1];if(M=me,$=li({axis:o,ticks:c,bandSize:a,offset:d.offset,entry:j,index:C}),k=Be-me,I=d.size,D={x:i.x,y:$,width:i.width,height:I},Math.abs(N)>0&&Math.abs(k)<Math.abs(N)){var Zt=Ne(k||N)*(Math.abs(N)-Math.abs(k));k+=Zt}}return ge(ge(ge({},j),{},{x:M,y:$,width:k,height:I,value:s?E:E[1],payload:j,background:D},_&&_[C]&&_[C].props),{},{tooltipPayload:[zh(r,j)],tooltipPosition:{x:M+k/2,y:$+I/2}})});return ge({data:S,layout:y},h)});function ca(e){"@babel/helpers - typeof";return ca=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ca(e)}function S$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Pm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,U2(n.key),n)}}function P$(e,t,r){return t&&Pm(e.prototype,t),r&&Pm(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Em(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function nt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Em(Object(r),!0).forEach(function(n){yo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Em(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yo(e,t,r){return t=U2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function U2(e){var t=E$(e,"string");return ca(t)=="symbol"?t:t+""}function E$(e,t){if(ca(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ca(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var j$=function(t,r,n,a,i){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},h=!!Fe(s,gr);return f.reduce(function(d,y){var v=r[y],p=v.orientation,x=v.domain,O=v.padding,w=O===void 0?{}:O,A=v.mirror,m=v.reversed,b="".concat(p).concat(A?"Mirror":""),_,S,j,C,E;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var M=x[1]-x[0],$=1/0,k=v.categoricalDomain.sort(Bw);if(k.forEach(function(fe,me){me>0&&($=Math.min((fe||0)-(k[me-1]||0),$))}),Number.isFinite($)){var I=$/M,D=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(_=I*D/2),v.padding==="no-gap"){var N=it(t.barCategoryGap,I*D),q=I*D/2;_=q-N-(q-N)/D*N}}}a==="xAxis"?S=[n.left+(w.left||0)+(_||0),n.left+n.width-(w.right||0)-(_||0)]:a==="yAxis"?S=c==="horizontal"?[n.top+n.height-(w.bottom||0),n.top+(w.top||0)]:[n.top+(w.top||0)+(_||0),n.top+n.height-(w.bottom||0)-(_||0)]:S=v.range,m&&(S=[S[1],S[0]]);var H=d2(v,i,h),Z=H.scale,U=H.realScaleType;Z.domain(x).range(S),p2(Z);var V=m2(Z,nt(nt({},v),{},{realScaleType:U}));a==="xAxis"?(E=p==="top"&&!A||p==="bottom"&&A,j=n.left,C=l[b]-E*v.height):a==="yAxis"&&(E=p==="left"&&!A||p==="right"&&A,j=l[b]-E*v.width,C=n.top);var G=nt(nt(nt({},v),V),{},{realScaleType:U,x:j,y:C,scale:Z,width:a==="xAxis"?n.width:v.width,height:a==="yAxis"?n.height:v.height});return G.bandSize=fi(G,V),!v.hide&&a==="xAxis"?l[b]+=(E?-1:1)*G.height:v.hide||(l[b]+=(E?-1:1)*G.width),nt(nt({},d),{},yo({},y,G))},{})},G2=function(t,r){var n=t.x,a=t.y,i=r.x,o=r.y;return{x:Math.min(n,i),y:Math.min(a,o),width:Math.abs(i-n),height:Math.abs(o-a)}},T$=function(t){var r=t.x1,n=t.y1,a=t.x2,i=t.y2;return G2({x:r,y:n},{x:a,y:i})},K2=(function(){function e(t){S$(this,e),this.scale=t}return P$(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=n.bandAware,i=n.position;if(r!==void 0){if(i)switch(i){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(a){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),a=n[0],i=n[n.length-1];return a<=i?r>=a&&r<=i:r>=i&&r<=a}}],[{key:"create",value:function(r){return new e(r)}}])})();yo(K2,"EPS",1e-4);var Uh=function(t){var r=Object.keys(t).reduce(function(n,a){return nt(nt({},n),{},yo({},a,K2.create(t[a])))},{});return nt(nt({},r),{},{apply:function(a){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=i.bandAware,u=i.position;return r$(a,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(a){return V2(a,function(i,o){return r[o].isInRange(i)})}})};function M$(e){return(e%180+180)%180}var $$=function(t){var r=t.width,n=t.height,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,i=M$(a),o=i*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},pl,jm;function C$(){if(jm)return pl;jm=1;var e=vt(),t=ba(),r=Ui();function n(a){return function(i,o,u){var c=Object(i);if(!t(i)){var s=e(o,3);i=r(i),o=function(l){return s(c[l],l,c)}}var f=a(i,o,u);return f>-1?c[s?i[f]:f]:void 0}}return pl=n,pl}var vl,Tm;function I$(){if(Tm)return vl;Tm=1;var e=q2();function t(r){var n=e(r),a=n%1;return n===n?a?n-a:n:0}return vl=t,vl}var yl,Mm;function k$(){if(Mm)return yl;Mm=1;var e=Gg(),t=vt(),r=I$(),n=Math.max;function a(i,o,u){var c=i==null?0:i.length;if(!c)return-1;var s=u==null?0:r(u);return s<0&&(s=n(c+s,0)),e(i,t(o,3),s)}return yl=a,yl}var ml,$m;function R$(){if($m)return ml;$m=1;var e=C$(),t=k$(),r=e(t);return ml=r,ml}var D$=R$();const N$=se(D$);var L$=lg();const q$=se(L$);var B$=q$(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),Gh=g.createContext(void 0),Kh=g.createContext(void 0),X2=g.createContext(void 0),Y2=g.createContext({}),J2=g.createContext(void 0),Q2=g.createContext(0),ex=g.createContext(0),Cm=function(t){var r=t.state,n=r.xAxisMap,a=r.yAxisMap,i=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=B$(i);return P.createElement(Gh.Provider,{value:n},P.createElement(Kh.Provider,{value:a},P.createElement(Y2.Provider,{value:i},P.createElement(X2.Provider,{value:f},P.createElement(J2.Provider,{value:o},P.createElement(Q2.Provider,{value:s},P.createElement(ex.Provider,{value:c},u)))))))},H$=function(){return g.useContext(J2)},tx=function(t){var r=g.useContext(Gh);r==null&&lr();var n=r[t];return n==null&&lr(),n},F$=function(){var t=g.useContext(Gh);return kt(t)},V$=function(){var t=g.useContext(Kh),r=N$(t,function(n){return V2(n.domain,Number.isFinite)});return r||kt(t)},rx=function(t){var r=g.useContext(Kh);r==null&&lr();var n=r[t];return n==null&&lr(),n},z$=function(){var t=g.useContext(X2);return t},Z$=function(){return g.useContext(Y2)},Xh=function(){return g.useContext(ex)},Yh=function(){return g.useContext(Q2)};function Gr(e){"@babel/helpers - typeof";return Gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gr(e)}function W$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function U$(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ax(n.key),n)}}function G$(e,t,r){return t&&U$(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function K$(e,t,r){return t=Ti(t),X$(e,nx()?Reflect.construct(t,r||[],Ti(e).constructor):t.apply(e,r))}function X$(e,t){if(t&&(Gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Y$(e)}function Y$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function nx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(nx=function(){return!!e})()}function Ti(e){return Ti=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ti(e)}function J$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mf(e,t)}function Mf(e,t){return Mf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Mf(e,t)}function Im(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function km(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Im(Object(r),!0).forEach(function(n){Jh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Im(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Jh(e,t,r){return t=ax(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ax(e){var t=Q$(e,"string");return Gr(t)=="symbol"?t:t+""}function Q$(e,t){if(Gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function eC(e,t){return aC(e)||nC(e,t)||rC(e,t)||tC()}function tC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function rC(e,t){if(e){if(typeof e=="string")return Rm(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Rm(e,t)}}function Rm(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function nC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function aC(e){if(Array.isArray(e))return e}function $f(){return $f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$f.apply(this,arguments)}var iC=function(t,r){var n;return P.isValidElement(t)?n=P.cloneElement(t,r):X(t)?n=t(r):n=P.createElement("line",$f({},r,{className:"recharts-reference-line-line"})),n},oC=function(t,r,n,a,i,o,u,c,s){var f=i.x,l=i.y,h=i.width,d=i.height;if(n){var y=s.y,v=t.y.apply(y,{position:o});if(ht(s,"discard")&&!t.y.isInRange(v))return null;var p=[{x:f+h,y:v},{x:f,y:v}];return c==="left"?p.reverse():p}if(r){var x=s.x,O=t.x.apply(x,{position:o});if(ht(s,"discard")&&!t.x.isInRange(O))return null;var w=[{x:O,y:l+d},{x:O,y:l}];return u==="top"?w.reverse():w}if(a){var A=s.segment,m=A.map(function(b){return t.apply(b,{position:o})});return ht(s,"discard")&&J3(m,function(b){return!t.isInRange(b)})?null:m}return null};function uC(e){var t=e.x,r=e.y,n=e.segment,a=e.xAxisId,i=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=H$(),f=tx(a),l=rx(i),h=z$();if(!s||!h)return null;At(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=Uh({x:f.scale,y:l.scale}),y=Ae(t),v=Ae(r),p=n&&n.length===2,x=oC(d,y,v,p,h,e.position,f.orientation,l.orientation,e);if(!x)return null;var O=eC(x,2),w=O[0],A=w.x,m=w.y,b=O[1],_=b.x,S=b.y,j=ht(e,"hidden")?"url(#".concat(s,")"):void 0,C=km(km({clipPath:j},K(e,!0)),{},{x1:A,y1:m,x2:_,y2:S});return P.createElement(ue,{className:Q("recharts-reference-line",u)},iC(o,C),Te.renderCallByParent(e,T$({x1:A,y1:m,x2:_,y2:S})))}var Qh=(function(e){function t(){return W$(this,t),K$(this,t,arguments)}return J$(t,e),G$(t,[{key:"render",value:function(){return P.createElement(uC,this.props)}}])})(P.Component);Jh(Qh,"displayName","ReferenceLine");Jh(Qh,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Cf(){return Cf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cf.apply(this,arguments)}function Kr(e){"@babel/helpers - typeof";return Kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kr(e)}function Dm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Nm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dm(Object(r),!0).forEach(function(n){mo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function sC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ox(n.key),n)}}function lC(e,t,r){return t&&sC(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function fC(e,t,r){return t=Mi(t),hC(e,ix()?Reflect.construct(t,r||[],Mi(e).constructor):t.apply(e,r))}function hC(e,t){if(t&&(Kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return dC(e)}function dC(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ix(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ix=function(){return!!e})()}function Mi(e){return Mi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Mi(e)}function pC(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&If(e,t)}function If(e,t){return If=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},If(e,t)}function mo(e,t,r){return t=ox(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ox(e){var t=vC(e,"string");return Kr(t)=="symbol"?t:t+""}function vC(e,t){if(Kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var yC=function(t){var r=t.x,n=t.y,a=t.xAxis,i=t.yAxis,o=Uh({x:a.scale,y:i.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return ht(t,"discard")&&!o.isInRange(u)?null:u},go=(function(e){function t(){return cC(this,t),fC(this,t,arguments)}return pC(t,e),lC(t,[{key:"render",value:function(){var n=this.props,a=n.x,i=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=Ae(a),f=Ae(i);if(At(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=yC(this.props);if(!l)return null;var h=l.x,d=l.y,y=this.props,v=y.shape,p=y.className,x=ht(this.props,"hidden")?"url(#".concat(c,")"):void 0,O=Nm(Nm({clipPath:x},K(this.props,!0)),{},{cx:h,cy:d});return P.createElement(ue,{className:Q("recharts-reference-dot",p)},t.renderDot(v,O),Te.renderCallByParent(this.props,{x:h-o,y:d-o,width:2*o,height:2*o}))}}])})(P.Component);mo(go,"displayName","ReferenceDot");mo(go,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});mo(go,"renderDot",function(e,t){var r;return P.isValidElement(e)?r=P.cloneElement(e,t):X(e)?r=e(t):r=P.createElement(Wh,Cf({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function kf(){return kf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},kf.apply(this,arguments)}function Xr(e){"@babel/helpers - typeof";return Xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xr(e)}function Lm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function qm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lm(Object(r),!0).forEach(function(n){bo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function gC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,cx(n.key),n)}}function bC(e,t,r){return t&&gC(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function xC(e,t,r){return t=$i(t),wC(e,ux()?Reflect.construct(t,r||[],$i(e).constructor):t.apply(e,r))}function wC(e,t){if(t&&(Xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return OC(e)}function OC(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ux(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ux=function(){return!!e})()}function $i(e){return $i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},$i(e)}function AC(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Rf(e,t)}function Rf(e,t){return Rf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Rf(e,t)}function bo(e,t,r){return t=cx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cx(e){var t=_C(e,"string");return Xr(t)=="symbol"?t:t+""}function _C(e,t){if(Xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var SC=function(t,r,n,a,i){var o=i.x1,u=i.x2,c=i.y1,s=i.y2,f=i.xAxis,l=i.yAxis;if(!f||!l)return null;var h=Uh({x:f.scale,y:l.scale}),d={x:t?h.x.apply(o,{position:"start"}):h.x.rangeMin,y:n?h.y.apply(c,{position:"start"}):h.y.rangeMin},y={x:r?h.x.apply(u,{position:"end"}):h.x.rangeMax,y:a?h.y.apply(s,{position:"end"}):h.y.rangeMax};return ht(i,"discard")&&(!h.isInRange(d)||!h.isInRange(y))?null:G2(d,y)},xo=(function(e){function t(){return mC(this,t),xC(this,t,arguments)}return AC(t,e),bC(t,[{key:"render",value:function(){var n=this.props,a=n.x1,i=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;At(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=Ae(a),h=Ae(i),d=Ae(o),y=Ae(u),v=this.props.shape;if(!l&&!h&&!d&&!y&&!v)return null;var p=SC(l,h,d,y,this.props);if(!p&&!v)return null;var x=ht(this.props,"hidden")?"url(#".concat(f,")"):void 0;return P.createElement(ue,{className:Q("recharts-reference-area",c)},t.renderRect(v,qm(qm({clipPath:x},K(this.props,!0)),p)),Te.renderCallByParent(this.props,p))}}])})(P.Component);bo(xo,"displayName","ReferenceArea");bo(xo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});bo(xo,"renderRect",function(e,t){var r;return P.isValidElement(e)?r=P.cloneElement(e,t):X(e)?r=e(t):r=P.createElement(Zh,kf({},t,{className:"recharts-reference-area-rect"})),r});function sx(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],a=0;a<e.length;a+=t)n.push(e[a]);return n}function PC(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return $$(n,r)}function EC(e,t,r){var n=r==="width",a=e.x,i=e.y,o=e.width,u=e.height;return t===1?{start:n?a:i,end:n?a+o:i+u}:{start:n?a+o:i+u,end:n?a:i}}function Ci(e,t,r,n,a){if(e*t<e*n||e*t>e*a)return!1;var i=r();return e*(t-e*i/2-n)>=0&&e*(t+e*i/2-a)<=0}function jC(e,t){return sx(e,t+1)}function TC(e,t,r,n,a){for(var i=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var y=n==null?void 0:n[c];if(y===void 0)return{v:sx(n,s)};var v=c,p,x=function(){return p===void 0&&(p=r(y,v)),p},O=y.coordinate,w=c===0||Ci(e,O,x,f,u);w||(c=0,f=o,s+=1),w&&(f=O+e*(x()/2+a),c+=s)},h;s<=i.length;)if(h=l(),h)return h.v;return[]}function sa(e){"@babel/helpers - typeof";return sa=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},sa(e)}function Bm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function $e(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Bm(Object(r),!0).forEach(function(n){MC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function MC(e,t,r){return t=$C(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $C(e){var t=CC(e,"string");return sa(t)=="symbol"?t:t+""}function CC(e,t){if(sa(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(sa(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function IC(e,t,r,n,a){for(var i=(n||[]).slice(),o=i.length,u=t.start,c=t.end,s=function(h){var d=i[h],y,v=function(){return y===void 0&&(y=r(d,h)),y};if(h===o-1){var p=e*(d.coordinate+e*v()/2-c);i[h]=d=$e($e({},d),{},{tickCoord:p>0?d.coordinate-p*e:d.coordinate})}else i[h]=d=$e($e({},d),{},{tickCoord:d.coordinate});var x=Ci(e,d.tickCoord,v,u,c);x&&(c=d.tickCoord-e*(v()/2+a),i[h]=$e($e({},d),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return i}function kC(e,t,r,n,a,i){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(i){var f=n[u-1],l=r(f,u-1),h=e*(f.coordinate+e*l/2-s);o[u-1]=f=$e($e({},f),{},{tickCoord:h>0?f.coordinate-h*e:f.coordinate});var d=Ci(e,f.tickCoord,function(){return l},c,s);d&&(s=f.tickCoord-e*(l/2+a),o[u-1]=$e($e({},f),{},{isShow:!0}))}for(var y=i?u-1:u,v=function(O){var w=o[O],A,m=function(){return A===void 0&&(A=r(w,O)),A};if(O===0){var b=e*(w.coordinate-e*m()/2-c);o[O]=w=$e($e({},w),{},{tickCoord:b<0?w.coordinate-b*e:w.coordinate})}else o[O]=w=$e($e({},w),{},{tickCoord:w.coordinate});var _=Ci(e,w.tickCoord,m,c,s);_&&(c=w.tickCoord+e*(m()/2+a),o[O]=$e($e({},w),{},{isShow:!0}))},p=0;p<y;p++)v(p);return o}function e0(e,t,r){var n=e.tick,a=e.ticks,i=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!a||!a.length||!n)return[];if(B(c)||un.isSsr)return jC(a,typeof c=="number"&&B(c)?c:0);var h=[],d=u==="top"||u==="bottom"?"width":"height",y=f&&d==="width"?_n(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(w,A){var m=X(s)?s(w.value,A):w.value;return d==="width"?PC(_n(m,{fontSize:t,letterSpacing:r}),y,l):_n(m,{fontSize:t,letterSpacing:r})[d]},p=a.length>=2?Ne(a[1].coordinate-a[0].coordinate):1,x=EC(i,p,d);return c==="equidistantPreserveStart"?TC(p,x,v,a,o):(c==="preserveStart"||c==="preserveStartEnd"?h=kC(p,x,v,a,o,c==="preserveStartEnd"):h=IC(p,x,v,a,o),h.filter(function(O){return O.isShow}))}var RC=["viewBox"],DC=["viewBox"],NC=["ticks"];function Yr(e){"@babel/helpers - typeof";return Yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yr(e)}function Sr(){return Sr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sr.apply(this,arguments)}function Hm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Ie(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hm(Object(r),!0).forEach(function(n){t0(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gl(e,t){if(e==null)return{};var r=LC(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function LC(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function qC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Fm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fx(n.key),n)}}function BC(e,t,r){return t&&Fm(e.prototype,t),r&&Fm(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function HC(e,t,r){return t=Ii(t),FC(e,lx()?Reflect.construct(t,r||[],Ii(e).constructor):t.apply(e,r))}function FC(e,t){if(t&&(Yr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return VC(e)}function VC(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function lx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(lx=function(){return!!e})()}function Ii(e){return Ii=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ii(e)}function zC(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Df(e,t)}function Df(e,t){return Df=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Df(e,t)}function t0(e,t,r){return t=fx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fx(e){var t=ZC(e,"string");return Yr(t)=="symbol"?t:t+""}function ZC(e,t){if(Yr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var fn=(function(e){function t(r){var n;return qC(this,t),n=HC(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return zC(t,e),BC(t,[{key:"shouldComponentUpdate",value:function(n,a){var i=n.viewBox,o=gl(n,RC),u=this.props,c=u.viewBox,s=gl(u,DC);return!Er(i,c)||!Er(o,s)||!Er(a,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var a=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];a&&this.setState({fontSize:window.getComputedStyle(a).fontSize,letterSpacing:window.getComputedStyle(a).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var a=this.props,i=a.x,o=a.y,u=a.width,c=a.height,s=a.orientation,f=a.tickSize,l=a.mirror,h=a.tickMargin,d,y,v,p,x,O,w=l?-1:1,A=n.tickSize||f,m=B(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":d=y=n.coordinate,p=o+ +!l*c,v=p-w*A,O=v-w*h,x=m;break;case"left":v=p=n.coordinate,y=i+ +!l*u,d=y-w*A,x=d-w*h,O=m;break;case"right":v=p=n.coordinate,y=i+ +l*u,d=y+w*A,x=d+w*h,O=m;break;default:d=y=n.coordinate,p=o+ +l*c,v=p+w*A,O=v+w*h,x=m;break}return{line:{x1:d,y1:v,x2:y,y2:p},tick:{x,y:O}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,a=n.orientation,i=n.mirror,o;switch(a){case"left":o=i?"start":"end";break;case"right":o=i?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,a=n.orientation,i=n.mirror,o="end";switch(a){case"left":case"right":o="middle";break;case"top":o=i?"start":"end";break;default:o=i?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,a=n.x,i=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=Ie(Ie(Ie({},K(this.props,!1)),K(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var h=+(c==="top"&&!s||c==="bottom"&&s);l=Ie(Ie({},l),{},{x1:a,y1:i+h*u,x2:a+o,y2:i+h*u})}else{var d=+(c==="left"&&!s||c==="right"&&s);l=Ie(Ie({},l),{},{x1:a+d*o,y1:i,x2:a+d*o,y2:i+u})}return P.createElement("line",Sr({},l,{className:Q("recharts-cartesian-axis-line",Je(f,"className"))}))}},{key:"renderTicks",value:function(n,a,i){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,h=u.unit,d=e0(Ie(Ie({},this.props),{},{ticks:n}),a,i),y=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),p=K(this.props,!1),x=K(f,!1),O=Ie(Ie({},p),{},{fill:"none"},K(c,!1)),w=d.map(function(A,m){var b=o.getTickLineCoord(A),_=b.line,S=b.tick,j=Ie(Ie(Ie(Ie({textAnchor:y,verticalAnchor:v},p),{},{stroke:"none",fill:s},x),S),{},{index:m,payload:A,visibleTicksCount:d.length,tickFormatter:l});return P.createElement(ue,Sr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(A.value,"-").concat(A.coordinate,"-").concat(A.tickCoord)},Bt(o.props,A,m)),c&&P.createElement("line",Sr({},O,_,{className:Q("recharts-cartesian-axis-tick-line",Je(c,"className"))})),f&&t.renderTickItem(f,j,"".concat(X(l)?l(A.value,m):A.value).concat(h||"")))});return P.createElement("g",{className:"recharts-cartesian-axis-ticks"},w)}},{key:"render",value:function(){var n=this,a=this.props,i=a.axisLine,o=a.width,u=a.height,c=a.ticksGenerator,s=a.className,f=a.hide;if(f)return null;var l=this.props,h=l.ticks,d=gl(l,NC),y=h;return X(c)&&(y=h&&h.length>0?c(this.props):c(d)),o<=0||u<=0||!y||!y.length?null:P.createElement(ue,{className:Q("recharts-cartesian-axis",s),ref:function(p){n.layerReference=p}},i&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),Te.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,a,i){var o;return P.isValidElement(n)?o=P.cloneElement(n,a):X(n)?o=n(a):o=P.createElement(Dr,Sr({},a,{className:"recharts-cartesian-axis-tick-value"}),i),o}}])})(g.Component);t0(fn,"displayName","CartesianAxis");t0(fn,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var WC=["x1","y1","x2","y2","key"],UC=["offset"];function fr(e){"@babel/helpers - typeof";return fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fr(e)}function Vm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Ce(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vm(Object(r),!0).forEach(function(n){GC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function GC(e,t,r){return t=KC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function KC(e){var t=XC(e,"string");return fr(t)=="symbol"?t:t+""}function XC(e,t){if(fr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function nr(){return nr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nr.apply(this,arguments)}function zm(e,t){if(e==null)return{};var r=YC(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function YC(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var JC=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,a=t.x,i=t.y,o=t.width,u=t.height,c=t.ry;return P.createElement("rect",{x:a,y:i,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function hx(e,t){var r;if(P.isValidElement(e))r=P.cloneElement(e,t);else if(X(e))r=e(t);else{var n=t.x1,a=t.y1,i=t.x2,o=t.y2,u=t.key,c=zm(t,WC),s=K(c,!1);s.offset;var f=zm(s,UC);r=P.createElement("line",nr({},f,{x1:n,y1:a,x2:i,y2:o,fill:"none",key:u}))}return r}function QC(e){var t=e.x,r=e.width,n=e.horizontal,a=n===void 0?!0:n,i=e.horizontalPoints;if(!a||!i||!i.length)return null;var o=i.map(function(u,c){var s=Ce(Ce({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return hx(a,s)});return P.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function eI(e){var t=e.y,r=e.height,n=e.vertical,a=n===void 0?!0:n,i=e.verticalPoints;if(!a||!i||!i.length)return null;var o=i.map(function(u,c){var s=Ce(Ce({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return hx(a,s)});return P.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function tI(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,a=e.y,i=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,s=c===void 0?!0:c;if(!s||!t||!t.length)return null;var f=u.map(function(h){return Math.round(h+a-a)}).sort(function(h,d){return h-d});a!==f[0]&&f.unshift(0);var l=f.map(function(h,d){var y=!f[d+1],v=y?a+o-h:f[d+1]-h;if(v<=0)return null;var p=d%t.length;return P.createElement("rect",{key:"react-".concat(d),y:h,x:n,height:v,width:i,stroke:"none",fill:t[p],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return P.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function rI(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,a=e.fillOpacity,i=e.x,o=e.y,u=e.width,c=e.height,s=e.verticalPoints;if(!r||!n||!n.length)return null;var f=s.map(function(h){return Math.round(h+i-i)}).sort(function(h,d){return h-d});i!==f[0]&&f.unshift(0);var l=f.map(function(h,d){var y=!f[d+1],v=y?i+u-h:f[d+1]-h;if(v<=0)return null;var p=d%n.length;return P.createElement("rect",{key:"react-".concat(d),x:h,y:o,width:v,height:c,stroke:"none",fill:n[p],fillOpacity:a,className:"recharts-cartesian-grid-bg"})});return P.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var nI=function(t,r){var n=t.xAxis,a=t.width,i=t.height,o=t.offset;return h2(e0(Ce(Ce(Ce({},fn.defaultProps),n),{},{ticks:wt(n,!0),viewBox:{x:0,y:0,width:a,height:i}})),o.left,o.left+o.width,r)},aI=function(t,r){var n=t.yAxis,a=t.width,i=t.height,o=t.offset;return h2(e0(Ce(Ce(Ce({},fn.defaultProps),n),{},{ticks:wt(n,!0),viewBox:{x:0,y:0,width:a,height:i}})),o.top,o.top+o.height,r)},Or={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function dx(e){var t,r,n,a,i,o,u=Xh(),c=Yh(),s=Z$(),f=Ce(Ce({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:Or.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:Or.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:Or.horizontal,horizontalFill:(a=e.horizontalFill)!==null&&a!==void 0?a:Or.horizontalFill,vertical:(i=e.vertical)!==null&&i!==void 0?i:Or.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:Or.verticalFill,x:B(e.x)?e.x:s.left,y:B(e.y)?e.y:s.top,width:B(e.width)?e.width:s.width,height:B(e.height)?e.height:s.height}),l=f.x,h=f.y,d=f.width,y=f.height,v=f.syncWithTicks,p=f.horizontalValues,x=f.verticalValues,O=F$(),w=V$();if(!B(d)||d<=0||!B(y)||y<=0||!B(l)||l!==+l||!B(h)||h!==+h)return null;var A=f.verticalCoordinatesGenerator||nI,m=f.horizontalCoordinatesGenerator||aI,b=f.horizontalPoints,_=f.verticalPoints;if((!b||!b.length)&&X(m)){var S=p&&p.length,j=m({yAxis:w?Ce(Ce({},w),{},{ticks:S?p:w.ticks}):void 0,width:u,height:c,offset:s},S?!0:v);At(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(fr(j),"]")),Array.isArray(j)&&(b=j)}if((!_||!_.length)&&X(A)){var C=x&&x.length,E=A({xAxis:O?Ce(Ce({},O),{},{ticks:C?x:O.ticks}):void 0,width:u,height:c,offset:s},C?!0:v);At(Array.isArray(E),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(fr(E),"]")),Array.isArray(E)&&(_=E)}return P.createElement("g",{className:"recharts-cartesian-grid"},P.createElement(JC,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),P.createElement(QC,nr({},f,{offset:s,horizontalPoints:b,xAxis:O,yAxis:w})),P.createElement(eI,nr({},f,{offset:s,verticalPoints:_,xAxis:O,yAxis:w})),P.createElement(tI,nr({},f,{horizontalPoints:b})),P.createElement(rI,nr({},f,{verticalPoints:_})))}dx.displayName="CartesianGrid";function Jr(e){"@babel/helpers - typeof";return Jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jr(e)}function iI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function oI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yx(n.key),n)}}function uI(e,t,r){return t&&oI(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function cI(e,t,r){return t=ki(t),sI(e,px()?Reflect.construct(t,r||[],ki(e).constructor):t.apply(e,r))}function sI(e,t){if(t&&(Jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return lI(e)}function lI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function px(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(px=function(){return!!e})()}function ki(e){return ki=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ki(e)}function fI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Nf(e,t)}function Nf(e,t){return Nf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Nf(e,t)}function vx(e,t,r){return t=yx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yx(e){var t=hI(e,"string");return Jr(t)=="symbol"?t:t+""}function hI(e,t){if(Jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Lf(){return Lf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Lf.apply(this,arguments)}function dI(e){var t=e.xAxisId,r=Xh(),n=Yh(),a=tx(t);return a==null?null:P.createElement(fn,Lf({},a,{className:Q("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return wt(o,!0)}}))}var la=(function(e){function t(){return iI(this,t),cI(this,t,arguments)}return fI(t,e),uI(t,[{key:"render",value:function(){return P.createElement(dI,this.props)}}])})(P.Component);vx(la,"displayName","XAxis");vx(la,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Qr(e){"@babel/helpers - typeof";return Qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qr(e)}function pI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function vI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,bx(n.key),n)}}function yI(e,t,r){return t&&vI(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function mI(e,t,r){return t=Ri(t),gI(e,mx()?Reflect.construct(t,r||[],Ri(e).constructor):t.apply(e,r))}function gI(e,t){if(t&&(Qr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return bI(e)}function bI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function mx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(mx=function(){return!!e})()}function Ri(e){return Ri=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ri(e)}function xI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qf(e,t)}function qf(e,t){return qf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},qf(e,t)}function gx(e,t,r){return t=bx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bx(e){var t=wI(e,"string");return Qr(t)=="symbol"?t:t+""}function wI(e,t){if(Qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Bf(){return Bf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Bf.apply(this,arguments)}var OI=function(t){var r=t.yAxisId,n=Xh(),a=Yh(),i=rx(r);return i==null?null:P.createElement(fn,Bf({},i,{className:Q("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:n,height:a},ticksGenerator:function(u){return wt(u,!0)}}))},wo=(function(e){function t(){return pI(this,t),mI(this,t,arguments)}return xI(t,e),yI(t,[{key:"render",value:function(){return P.createElement(OI,this.props)}}])})(P.Component);gx(wo,"displayName","YAxis");gx(wo,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function Zm(e){return PI(e)||SI(e)||_I(e)||AI()}function AI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _I(e,t){if(e){if(typeof e=="string")return Hf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hf(e,t)}}function SI(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function PI(e){if(Array.isArray(e))return Hf(e)}function Hf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Ff=function(t,r,n,a,i){var o=Qe(t,Qh),u=Qe(t,go),c=[].concat(Zm(o),Zm(u)),s=Qe(t,xo),f="".concat(a,"Id"),l=a[0],h=r;if(c.length&&(h=c.reduce(function(v,p){if(p.props[f]===n&&ht(p.props,"extendDomain")&&B(p.props[l])){var x=p.props[l];return[Math.min(v[0],x),Math.max(v[1],x)]}return v},h)),s.length){var d="".concat(l,"1"),y="".concat(l,"2");h=s.reduce(function(v,p){if(p.props[f]===n&&ht(p.props,"extendDomain")&&B(p.props[d])&&B(p.props[y])){var x=p.props[d],O=p.props[y];return[Math.min(v[0],x,O),Math.max(v[1],x,O)]}return v},h)}return i&&i.length&&(h=i.reduce(function(v,p){return B(p)?[Math.min(v[0],p),Math.max(v[1],p)]:v},h)),h},bl={exports:{}},Wm;function EI(){return Wm||(Wm=1,(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function a(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function i(c,s,f,l,h){if(typeof f!="function")throw new TypeError("The listener must be a function");var d=new a(f,l||c,h),y=r?r+s:s;return c._events[y]?c._events[y].fn?c._events[y]=[c._events[y],d]:c._events[y].push(d):(c._events[y]=d,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var h=0,d=l.length,y=new Array(d);h<d;h++)y[h]=l[h].fn;return y},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,h,d,y){var v=r?r+s:s;if(!this._events[v])return!1;var p=this._events[v],x=arguments.length,O,w;if(p.fn){switch(p.once&&this.removeListener(s,p.fn,void 0,!0),x){case 1:return p.fn.call(p.context),!0;case 2:return p.fn.call(p.context,f),!0;case 3:return p.fn.call(p.context,f,l),!0;case 4:return p.fn.call(p.context,f,l,h),!0;case 5:return p.fn.call(p.context,f,l,h,d),!0;case 6:return p.fn.call(p.context,f,l,h,d,y),!0}for(w=1,O=new Array(x-1);w<x;w++)O[w-1]=arguments[w];p.fn.apply(p.context,O)}else{var A=p.length,m;for(w=0;w<A;w++)switch(p[w].once&&this.removeListener(s,p[w].fn,void 0,!0),x){case 1:p[w].fn.call(p[w].context);break;case 2:p[w].fn.call(p[w].context,f);break;case 3:p[w].fn.call(p[w].context,f,l);break;case 4:p[w].fn.call(p[w].context,f,l,h);break;default:if(!O)for(m=1,O=new Array(x-1);m<x;m++)O[m-1]=arguments[m];p[w].fn.apply(p[w].context,O)}}return!0},u.prototype.on=function(s,f,l){return i(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return i(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,h){var d=r?r+s:s;if(!this._events[d])return this;if(!f)return o(this,d),this;var y=this._events[d];if(y.fn)y.fn===f&&(!h||y.once)&&(!l||y.context===l)&&o(this,d);else{for(var v=0,p=[],x=y.length;v<x;v++)(y[v].fn!==f||h&&!y[v].once||l&&y[v].context!==l)&&p.push(y[v]);p.length?this._events[d]=p.length===1?p[0]:p:o(this,d)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u})(bl)),bl.exports}var jI=EI();const TI=se(jI);var xl=new TI,wl="recharts.syncMouseEvents";function fa(e){"@babel/helpers - typeof";return fa=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fa(e)}function MI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function $I(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,xx(n.key),n)}}function CI(e,t,r){return t&&$I(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ol(e,t,r){return t=xx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xx(e){var t=II(e,"string");return fa(t)=="symbol"?t:t+""}function II(e,t){if(fa(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(fa(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var kI=(function(){function e(){MI(this,e),Ol(this,"activeIndex",0),Ol(this,"coordinateList",[]),Ol(this,"layout","horizontal")}return CI(e,[{key:"setDetails",value:function(r){var n,a=r.coordinateList,i=a===void 0?null:a,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,h=r.mouseHandlerCallback,d=h===void 0?null:h;this.coordinateList=(n=i??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=s??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=d??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var a=this.container.getBoundingClientRect(),i=a.x,o=a.y,u=a.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=i+c+s,h=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:h})}}}])})();function RI(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],a=e==null?void 0:e[1];if(n&&a&&B(n)&&B(a))return!0}return!1}function DI(e,t,r,n){var a=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-a:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-a,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function wx(e){var t=e.cx,r=e.cy,n=e.radius,a=e.startAngle,i=e.endAngle,o=pe(t,r,n,a),u=pe(t,r,n,i);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:a,endAngle:i}}function NI(e,t,r){var n,a,i,o;if(e==="horizontal")n=t.x,i=n,a=r.top,o=r.top+r.height;else if(e==="vertical")a=t.y,o=a,n=r.left,i=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,h=pe(u,c,s,l),d=pe(u,c,f,l);n=h.x,a=h.y,i=d.x,o=d.y}else return wx(t);return[{x:n,y:a},{x:i,y:o}]}function ha(e){"@babel/helpers - typeof";return ha=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ha(e)}function Um(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function La(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Um(Object(r),!0).forEach(function(n){LI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Um(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function LI(e,t,r){return t=qI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qI(e){var t=BI(e,"string");return ha(t)=="symbol"?t:t+""}function BI(e,t){if(ha(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ha(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function HI(e){var t,r,n=e.element,a=e.tooltipEventType,i=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,h=e.chartName,d=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!d||!i||!o||h!=="ScatterChart"&&a!=="axis")return null;var y,v=f1;if(h==="ScatterChart")y=o,v=vM;else if(h==="BarChart")y=DI(l,o,c,f),v=Zh;else if(l==="radial"){var p=wx(o),x=p.cx,O=p.cy,w=p.radius,A=p.startAngle,m=p.endAngle;y={cx:x,cy:O,startAngle:A,endAngle:m,innerRadius:w,outerRadius:w},v=_2}else y={points:NI(l,o,c)},v=f1;var b=La(La(La(La({stroke:"#ccc",pointerEvents:"none"},c),y),K(d,!1)),{},{payload:u,payloadIndex:s,className:Q("recharts-tooltip-cursor",d.className)});return g.isValidElement(d)?g.cloneElement(d,b):g.createElement(v,b)}var FI=["item"],VI=["children","className","width","height","style","compact","title","desc"];function en(e){"@babel/helpers - typeof";return en=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},en(e)}function Pr(){return Pr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pr.apply(this,arguments)}function Gm(e,t){return WI(e)||ZI(e,t)||Ax(e,t)||zI()}function zI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ZI(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,a=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function WI(e){if(Array.isArray(e))return e}function Km(e,t){if(e==null)return{};var r=UI(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function UI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function GI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function KI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_x(n.key),n)}}function XI(e,t,r){return t&&KI(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function YI(e,t,r){return t=Di(t),JI(e,Ox()?Reflect.construct(t,r||[],Di(e).constructor):t.apply(e,r))}function JI(e,t){if(t&&(en(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return QI(e)}function QI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ox(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ox=function(){return!!e})()}function Di(e){return Di=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Di(e)}function e5(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Vf(e,t)}function Vf(e,t){return Vf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},Vf(e,t)}function tn(e){return n5(e)||r5(e)||Ax(e)||t5()}function t5(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ax(e,t){if(e){if(typeof e=="string")return zf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zf(e,t)}}function r5(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function n5(e){if(Array.isArray(e))return zf(e)}function zf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Xm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xm(Object(r),!0).forEach(function(n){W(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function W(e,t,r){return t=_x(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _x(e){var t=a5(e,"string");return en(t)=="symbol"?t:t+""}function a5(e,t){if(en(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(en(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var i5={xAxis:["bottom","top"],yAxis:["left","right"]},o5={width:"100%",height:"100%"},Sx={x:0,y:0};function qa(e){return e}var u5=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},c5=function(t,r,n,a){var i=r.find(function(f){return f&&f.index===n});if(i){if(t==="horizontal")return{x:i.coordinate,y:a.y};if(t==="vertical")return{x:a.x,y:i.coordinate};if(t==="centric"){var o=i.coordinate,u=a.radius;return R(R(R({},a),pe(a.cx,a.cy,u,o)),{},{angle:o,radius:u})}var c=i.coordinate,s=a.angle;return R(R(R({},a),pe(a.cx,a.cy,c,s)),{},{angle:s,radius:c})}return Sx},Oo=function(t,r){var n=r.graphicalItems,a=r.dataStartIndex,i=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(tn(u),tn(s)):u},[]);return o.length>0?o:t&&t.length&&B(a)&&B(i)?t.slice(a,i+1):[]};function Px(e){return e==="number"?[0,"auto"]:void 0}var Zf=function(t,r,n,a){var i=t.graphicalItems,o=t.tooltipAxis,u=Oo(r,t);return n<0||!i||!i.length||n>=u.length?null:i.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var h;if(o.dataKey&&!o.allowDuplicatedCategory){var d=l===void 0?u:l;h=Sl(d,o.dataKey,a)}else h=l&&l[n]||u[n];return h?[].concat(tn(c),[zh(s,h)]):c},[])},Ym=function(t,r,n,a){var i=a||{x:t.chartX,y:t.chartY},o=u5(i,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=WP(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,h=Zf(t,r,f,l),d=c5(n,u,f,i);return{activeTooltipIndex:f,activeLabel:l,activePayload:h,activeCoordinate:d}}return null},s5=function(t,r){var n=r.axes,a=r.graphicalItems,i=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=t.stackOffset,d=f2(f,i);return n.reduce(function(y,v){var p,x=v.type.defaultProps!==void 0?R(R({},v.type.defaultProps),v.props):v.props,O=x.type,w=x.dataKey,A=x.allowDataOverflow,m=x.allowDuplicatedCategory,b=x.scale,_=x.ticks,S=x.includeHidden,j=x[o];if(y[j])return y;var C=Oo(t.data,{graphicalItems:a.filter(function(V){var G,fe=o in V.props?V.props[o]:(G=V.type.defaultProps)===null||G===void 0?void 0:G[o];return fe===j}),dataStartIndex:c,dataEndIndex:s}),E=C.length,M,$,k;RI(x.domain,A,O)&&(M=of(x.domain,null,A),d&&(O==="number"||b!=="auto")&&(k=Pn(C,w,"category")));var I=Px(O);if(!M||M.length===0){var D,N=(D=x.domain)!==null&&D!==void 0?D:I;if(w){if(M=Pn(C,w,O),O==="category"&&d){var q=qw(M);m&&q?($=M,M=_i(0,E)):m||(M=Yy(N,M,v).reduce(function(V,G){return V.indexOf(G)>=0?V:[].concat(tn(V),[G])},[]))}else if(O==="category")m?M=M.filter(function(V){return V!==""&&!re(V)}):M=Yy(N,M,v).reduce(function(V,G){return V.indexOf(G)>=0||G===""||re(G)?V:[].concat(tn(V),[G])},[]);else if(O==="number"){var H=YP(C,a.filter(function(V){var G,fe,me=o in V.props?V.props[o]:(G=V.type.defaultProps)===null||G===void 0?void 0:G[o],Be="hide"in V.props?V.props.hide:(fe=V.type.defaultProps)===null||fe===void 0?void 0:fe.hide;return me===j&&(S||!Be)}),w,i,f);H&&(M=H)}d&&(O==="number"||b!=="auto")&&(k=Pn(C,w,"category"))}else d?M=_i(0,E):u&&u[j]&&u[j].hasStack&&O==="number"?M=h==="expand"?[0,1]:b2(u[j].stackGroups,c,s):M=l2(C,a.filter(function(V){var G=o in V.props?V.props[o]:V.type.defaultProps[o],fe="hide"in V.props?V.props.hide:V.type.defaultProps.hide;return G===j&&(S||!fe)}),O,f,!0);if(O==="number")M=Ff(l,M,j,i,_),N&&(M=of(N,M,A));else if(O==="category"&&N){var Z=N,U=M.every(function(V){return Z.indexOf(V)>=0});U&&(M=Z)}}return R(R({},y),{},W({},j,R(R({},x),{},{axisType:i,domain:M,categoricalDomain:k,duplicateDomain:$,originalDomain:(p=x.domain)!==null&&p!==void 0?p:I,isCategorical:d,layout:f})))},{})},l5=function(t,r){var n=r.graphicalItems,a=r.Axis,i=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=Oo(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),d=h.length,y=f2(f,i),v=-1;return n.reduce(function(p,x){var O=x.type.defaultProps!==void 0?R(R({},x.type.defaultProps),x.props):x.props,w=O[o],A=Px("number");if(!p[w]){v++;var m;return y?m=_i(0,d):u&&u[w]&&u[w].hasStack?(m=b2(u[w].stackGroups,c,s),m=Ff(l,m,w,i)):(m=of(A,l2(h,n.filter(function(b){var _,S,j=o in b.props?b.props[o]:(_=b.type.defaultProps)===null||_===void 0?void 0:_[o],C="hide"in b.props?b.props.hide:(S=b.type.defaultProps)===null||S===void 0?void 0:S.hide;return j===w&&!C}),"number",f),a.defaultProps.allowDataOverflow),m=Ff(l,m,w,i)),R(R({},p),{},W({},w,R(R({axisType:i},a.defaultProps),{},{hide:!0,orientation:Je(i5,"".concat(i,".").concat(v%2),null),domain:m,originalDomain:A,isCategorical:y,layout:f})))}return p},{})},f5=function(t,r){var n=r.axisType,a=n===void 0?"xAxis":n,i=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(a,"Id"),h=Qe(f,i),d={};return h&&h.length?d=s5(t,{axes:h,graphicalItems:o,axisType:a,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(d=l5(t,{Axis:i,graphicalItems:o,axisType:a,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),d},h5=function(t){var r=kt(t),n=wt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:yh(n,function(a){return a.coordinate}),tooltipAxis:r,tooltipAxisBandSize:fi(r,n)}},Jm=function(t){var r=t.children,n=t.defaultShowTooltip,a=Fe(r,Wr),i=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),a&&a.props&&(a.props.startIndex>=0&&(i=a.props.startIndex),a.props.endIndex>=0&&(o=a.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},d5=function(t){return!t||!t.length?!1:t.some(function(r){var n=Ot(r&&r.type);return n&&n.indexOf("Bar")>=0})},Qm=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},p5=function(t,r){var n=t.props,a=t.graphicalItems,i=t.xAxisMap,o=i===void 0?{}:i,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,h=n.margin||{},d=Fe(l,Wr),y=Fe(l,jr),v=Object.keys(c).reduce(function(m,b){var _=c[b],S=_.orientation;return!_.mirror&&!_.hide?R(R({},m),{},W({},S,m[S]+_.width)):m},{left:h.left||0,right:h.right||0}),p=Object.keys(o).reduce(function(m,b){var _=o[b],S=_.orientation;return!_.mirror&&!_.hide?R(R({},m),{},W({},S,Je(m,"".concat(S))+_.height)):m},{top:h.top||0,bottom:h.bottom||0}),x=R(R({},p),v),O=x.bottom;d&&(x.bottom+=d.props.height||Wr.defaultProps.height),y&&r&&(x=KP(x,a,n,r));var w=s-x.left-x.right,A=f-x.top-x.bottom;return R(R({brushBottom:O},x),{},{width:Math.max(w,0),height:Math.max(A,0)})},v5=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},Ex=function(t){var r=t.chartName,n=t.GraphicalChild,a=t.defaultTooltipEventType,i=a===void 0?"axis":a,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,h=function(x,O){var w=O.graphicalItems,A=O.stackGroups,m=O.offset,b=O.updateId,_=O.dataStartIndex,S=O.dataEndIndex,j=x.barSize,C=x.layout,E=x.barGap,M=x.barCategoryGap,$=x.maxBarSize,k=Qm(C),I=k.numericAxisName,D=k.cateAxisName,N=d5(w),q=[];return w.forEach(function(H,Z){var U=Oo(x.data,{graphicalItems:[H],dataStartIndex:_,dataEndIndex:S}),V=H.type.defaultProps!==void 0?R(R({},H.type.defaultProps),H.props):H.props,G=V.dataKey,fe=V.maxBarSize,me=V["".concat(I,"Id")],Be=V["".concat(D,"Id")],Zt={},Re=c.reduce(function(Wt,Ut){var Ao=O["".concat(Ut.axisType,"Map")],n0=V["".concat(Ut.axisType,"Id")];Ao&&Ao[n0]||Ut.axisType==="zAxis"||lr();var a0=Ao[n0];return R(R({},Wt),{},W(W({},Ut.axisType,a0),"".concat(Ut.axisType,"Ticks"),wt(a0)))},Zt),F=Re[D],Y=Re["".concat(D,"Ticks")],J=A&&A[me]&&A[me].hasStack&&nE(H,A[me].stackGroups),L=Ot(H.type).indexOf("Bar")>=0,ve=fi(F,Y),ee=[],xe=N&&UP({barSize:j,stackGroups:A,totalSize:v5(Re,D)});if(L){var we,De,Ct=re(fe)?$:fe,br=(we=(De=fi(F,Y,!0))!==null&&De!==void 0?De:Ct)!==null&&we!==void 0?we:0;ee=GP({barGap:E,barCategoryGap:M,bandSize:br!==ve?br:ve,sizeList:xe[Be],maxBarSize:Ct}),br!==ve&&(ee=ee.map(function(Wt){return R(R({},Wt),{},{position:R(R({},Wt.position),{},{offset:Wt.position.offset-br/2})})}))}var Sa=H&&H.type&&H.type.getComposedData;Sa&&q.push({props:R(R({},Sa(R(R({},Re),{},{displayedData:U,props:x,dataKey:G,item:H,bandSize:ve,barPosition:ee,offset:m,stackedData:J,layout:C,dataStartIndex:_,dataEndIndex:S}))),{},W(W(W({key:H.key||"item-".concat(Z)},I,Re[I]),D,Re[D]),"animationId",b)),childIndex:Yw(H,x.children),item:H})}),q},d=function(x,O){var w=x.props,A=x.dataStartIndex,m=x.dataEndIndex,b=x.updateId;if(!dd({props:w}))return null;var _=w.children,S=w.layout,j=w.stackOffset,C=w.data,E=w.reverseStackOrder,M=Qm(S),$=M.numericAxisName,k=M.cateAxisName,I=Qe(_,n),D=rE(C,I,"".concat($,"Id"),"".concat(k,"Id"),j,E),N=c.reduce(function(V,G){var fe="".concat(G.axisType,"Map");return R(R({},V),{},W({},fe,f5(w,R(R({},G),{},{graphicalItems:I,stackGroups:G.axisType===$&&D,dataStartIndex:A,dataEndIndex:m}))))},{}),q=p5(R(R({},N),{},{props:w,graphicalItems:I}),O==null?void 0:O.legendBBox);Object.keys(N).forEach(function(V){N[V]=f(w,N[V],q,V.replace("Map",""),r)});var H=N["".concat(k,"Map")],Z=h5(H),U=h(w,R(R({},N),{},{dataStartIndex:A,dataEndIndex:m,updateId:b,graphicalItems:I,stackGroups:D,offset:q}));return R(R({formattedGraphicalItems:U,graphicalItems:I,offset:q,stackGroups:D},Z),N)},y=(function(p){function x(O){var w,A,m;return GI(this,x),m=YI(this,x,[O]),W(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),W(m,"accessibilityManager",new kI),W(m,"handleLegendBBoxUpdate",function(b){if(b){var _=m.state,S=_.dataStartIndex,j=_.dataEndIndex,C=_.updateId;m.setState(R({legendBBox:b},d({props:m.props,dataStartIndex:S,dataEndIndex:j,updateId:C},R(R({},m.state),{},{legendBBox:b}))))}}),W(m,"handleReceiveSyncEvent",function(b,_,S){if(m.props.syncId===b){if(S===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(_)}}),W(m,"handleBrushChange",function(b){var _=b.startIndex,S=b.endIndex;if(_!==m.state.dataStartIndex||S!==m.state.dataEndIndex){var j=m.state.updateId;m.setState(function(){return R({dataStartIndex:_,dataEndIndex:S},d({props:m.props,dataStartIndex:_,dataEndIndex:S,updateId:j},m.state))}),m.triggerSyncEvent({dataStartIndex:_,dataEndIndex:S})}}),W(m,"handleMouseEnter",function(b){var _=m.getMouseInfo(b);if(_){var S=R(R({},_),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var j=m.props.onMouseEnter;X(j)&&j(S,b)}}),W(m,"triggeredAfterMouseMove",function(b){var _=m.getMouseInfo(b),S=_?R(R({},_),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(S),m.triggerSyncEvent(S);var j=m.props.onMouseMove;X(j)&&j(S,b)}),W(m,"handleItemMouseEnter",function(b){m.setState(function(){return{isTooltipActive:!0,activeItem:b,activePayload:b.tooltipPayload,activeCoordinate:b.tooltipPosition||{x:b.cx,y:b.cy}}})}),W(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),W(m,"handleMouseMove",function(b){b.persist(),m.throttleTriggeredAfterMouseMove(b)}),W(m,"handleMouseLeave",function(b){m.throttleTriggeredAfterMouseMove.cancel();var _={isTooltipActive:!1};m.setState(_),m.triggerSyncEvent(_);var S=m.props.onMouseLeave;X(S)&&S(_,b)}),W(m,"handleOuterEvent",function(b){var _=Xw(b),S=Je(m.props,"".concat(_));if(_&&X(S)){var j,C;/.*touch.*/i.test(_)?C=m.getMouseInfo(b.changedTouches[0]):C=m.getMouseInfo(b),S((j=C)!==null&&j!==void 0?j:{},b)}}),W(m,"handleClick",function(b){var _=m.getMouseInfo(b);if(_){var S=R(R({},_),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var j=m.props.onClick;X(j)&&j(S,b)}}),W(m,"handleMouseDown",function(b){var _=m.props.onMouseDown;if(X(_)){var S=m.getMouseInfo(b);_(S,b)}}),W(m,"handleMouseUp",function(b){var _=m.props.onMouseUp;if(X(_)){var S=m.getMouseInfo(b);_(S,b)}}),W(m,"handleTouchMove",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(b.changedTouches[0])}),W(m,"handleTouchStart",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&m.handleMouseDown(b.changedTouches[0])}),W(m,"handleTouchEnd",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&m.handleMouseUp(b.changedTouches[0])}),W(m,"handleDoubleClick",function(b){var _=m.props.onDoubleClick;if(X(_)){var S=m.getMouseInfo(b);_(S,b)}}),W(m,"handleContextMenu",function(b){var _=m.props.onContextMenu;if(X(_)){var S=m.getMouseInfo(b);_(S,b)}}),W(m,"triggerSyncEvent",function(b){m.props.syncId!==void 0&&xl.emit(wl,m.props.syncId,b,m.eventEmitterSymbol)}),W(m,"applySyncEvent",function(b){var _=m.props,S=_.layout,j=_.syncMethod,C=m.state.updateId,E=b.dataStartIndex,M=b.dataEndIndex;if(b.dataStartIndex!==void 0||b.dataEndIndex!==void 0)m.setState(R({dataStartIndex:E,dataEndIndex:M},d({props:m.props,dataStartIndex:E,dataEndIndex:M,updateId:C},m.state)));else if(b.activeTooltipIndex!==void 0){var $=b.chartX,k=b.chartY,I=b.activeTooltipIndex,D=m.state,N=D.offset,q=D.tooltipTicks;if(!N)return;if(typeof j=="function")I=j(q,b);else if(j==="value"){I=-1;for(var H=0;H<q.length;H++)if(q[H].value===b.activeLabel){I=H;break}}var Z=R(R({},N),{},{x:N.left,y:N.top}),U=Math.min($,Z.x+Z.width),V=Math.min(k,Z.y+Z.height),G=q[I]&&q[I].value,fe=Zf(m.state,m.props.data,I),me=q[I]?{x:S==="horizontal"?q[I].coordinate:U,y:S==="horizontal"?V:q[I].coordinate}:Sx;m.setState(R(R({},b),{},{activeLabel:G,activeCoordinate:me,activePayload:fe,activeTooltipIndex:I}))}else m.setState(b)}),W(m,"renderCursor",function(b){var _,S=m.state,j=S.isTooltipActive,C=S.activeCoordinate,E=S.activePayload,M=S.offset,$=S.activeTooltipIndex,k=S.tooltipAxisBandSize,I=m.getTooltipEventType(),D=(_=b.props.active)!==null&&_!==void 0?_:j,N=m.props.layout,q=b.key||"_recharts-cursor";return P.createElement(HI,{key:q,activeCoordinate:C,activePayload:E,activeTooltipIndex:$,chartName:r,element:b,isActive:D,layout:N,offset:M,tooltipAxisBandSize:k,tooltipEventType:I})}),W(m,"renderPolarAxis",function(b,_,S){var j=Je(b,"type.axisType"),C=Je(m.state,"".concat(j,"Map")),E=b.type.defaultProps,M=E!==void 0?R(R({},E),b.props):b.props,$=C&&C[M["".concat(j,"Id")]];return g.cloneElement(b,R(R({},$),{},{className:Q(j,$.className),key:b.key||"".concat(_,"-").concat(S),ticks:wt($,!0)}))}),W(m,"renderPolarGrid",function(b){var _=b.props,S=_.radialLines,j=_.polarAngles,C=_.polarRadius,E=m.state,M=E.radiusAxisMap,$=E.angleAxisMap,k=kt(M),I=kt($),D=I.cx,N=I.cy,q=I.innerRadius,H=I.outerRadius;return g.cloneElement(b,{polarAngles:Array.isArray(j)?j:wt(I,!0).map(function(Z){return Z.coordinate}),polarRadius:Array.isArray(C)?C:wt(k,!0).map(function(Z){return Z.coordinate}),cx:D,cy:N,innerRadius:q,outerRadius:H,key:b.key||"polar-grid",radialLines:S})}),W(m,"renderLegend",function(){var b=m.state.formattedGraphicalItems,_=m.props,S=_.children,j=_.width,C=_.height,E=m.props.margin||{},M=j-(E.left||0)-(E.right||0),$=c2({children:S,formattedGraphicalItems:b,legendWidth:M,legendContent:s});if(!$)return null;var k=$.item,I=Km($,FI);return g.cloneElement(k,R(R({},I),{},{chartWidth:j,chartHeight:C,margin:E,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),W(m,"renderTooltip",function(){var b,_=m.props,S=_.children,j=_.accessibilityLayer,C=Fe(S,st);if(!C)return null;var E=m.state,M=E.isTooltipActive,$=E.activeCoordinate,k=E.activePayload,I=E.activeLabel,D=E.offset,N=(b=C.props.active)!==null&&b!==void 0?b:M;return g.cloneElement(C,{viewBox:R(R({},D),{},{x:D.left,y:D.top}),active:N,label:I,payload:N?k:[],coordinate:$,accessibilityLayer:j})}),W(m,"renderBrush",function(b){var _=m.props,S=_.margin,j=_.data,C=m.state,E=C.offset,M=C.dataStartIndex,$=C.dataEndIndex,k=C.updateId;return g.cloneElement(b,{key:b.key||"_recharts-brush",onChange:ka(m.handleBrushChange,b.props.onChange),data:j,x:B(b.props.x)?b.props.x:E.left,y:B(b.props.y)?b.props.y:E.top+E.height+E.brushBottom-(S.bottom||0),width:B(b.props.width)?b.props.width:E.width,startIndex:M,endIndex:$,updateId:"brush-".concat(k)})}),W(m,"renderReferenceElement",function(b,_,S){if(!b)return null;var j=m,C=j.clipPathId,E=m.state,M=E.xAxisMap,$=E.yAxisMap,k=E.offset,I=b.type.defaultProps||{},D=b.props,N=D.xAxisId,q=N===void 0?I.xAxisId:N,H=D.yAxisId,Z=H===void 0?I.yAxisId:H;return g.cloneElement(b,{key:b.key||"".concat(_,"-").concat(S),xAxis:M[q],yAxis:$[Z],viewBox:{x:k.left,y:k.top,width:k.width,height:k.height},clipPathId:C})}),W(m,"renderActivePoints",function(b){var _=b.item,S=b.activePoint,j=b.basePoint,C=b.childIndex,E=b.isRange,M=[],$=_.props.key,k=_.item.type.defaultProps!==void 0?R(R({},_.item.type.defaultProps),_.item.props):_.item.props,I=k.activeDot,D=k.dataKey,N=R(R({index:C,dataKey:D,cx:S.x,cy:S.y,r:4,fill:Vh(_.item),strokeWidth:2,stroke:"#fff",payload:S.payload,value:S.value},K(I,!1)),Ba(I));return M.push(x.renderActiveDot(I,N,"".concat($,"-activePoint-").concat(C))),j?M.push(x.renderActiveDot(I,R(R({},N),{},{cx:j.x,cy:j.y}),"".concat($,"-basePoint-").concat(C))):E&&M.push(null),M}),W(m,"renderGraphicChild",function(b,_,S){var j=m.filterFormatItem(b,_,S);if(!j)return null;var C=m.getTooltipEventType(),E=m.state,M=E.isTooltipActive,$=E.tooltipAxis,k=E.activeTooltipIndex,I=E.activeLabel,D=m.props.children,N=Fe(D,st),q=j.props,H=q.points,Z=q.isRange,U=q.baseLine,V=j.item.type.defaultProps!==void 0?R(R({},j.item.type.defaultProps),j.item.props):j.item.props,G=V.activeDot,fe=V.hide,me=V.activeBar,Be=V.activeShape,Zt=!!(!fe&&M&&N&&(G||me||Be)),Re={};C!=="axis"&&N&&N.props.trigger==="click"?Re={onClick:ka(m.handleItemMouseEnter,b.props.onClick)}:C!=="axis"&&(Re={onMouseLeave:ka(m.handleItemMouseLeave,b.props.onMouseLeave),onMouseEnter:ka(m.handleItemMouseEnter,b.props.onMouseEnter)});var F=g.cloneElement(b,R(R({},j.props),Re));function Y(Ut){return typeof $.dataKey=="function"?$.dataKey(Ut.payload):null}if(Zt)if(k>=0){var J,L;if($.dataKey&&!$.allowDuplicatedCategory){var ve=typeof $.dataKey=="function"?Y:"payload.".concat($.dataKey.toString());J=Sl(H,ve,I),L=Z&&U&&Sl(U,ve,I)}else J=H==null?void 0:H[k],L=Z&&U&&U[k];if(Be||me){var ee=b.props.activeIndex!==void 0?b.props.activeIndex:k;return[g.cloneElement(b,R(R(R({},j.props),Re),{},{activeIndex:ee})),null,null]}if(!re(J))return[F].concat(tn(m.renderActivePoints({item:j,activePoint:J,basePoint:L,childIndex:k,isRange:Z})))}else{var xe,we=(xe=m.getItemByXY(m.state.activeCoordinate))!==null&&xe!==void 0?xe:{graphicalItem:F},De=we.graphicalItem,Ct=De.item,br=Ct===void 0?b:Ct,Sa=De.childIndex,Wt=R(R(R({},j.props),Re),{},{activeIndex:Sa});return[g.cloneElement(br,Wt),null,null]}return Z?[F,null,null]:[F,null]}),W(m,"renderCustomized",function(b,_,S){return g.cloneElement(b,R(R({key:"recharts-customized-".concat(S)},m.props),m.state))}),W(m,"renderMap",{CartesianGrid:{handler:qa,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:qa},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:qa},YAxis:{handler:qa},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((w=O.id)!==null&&w!==void 0?w:Fi("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=ub(m.triggeredAfterMouseMove,(A=O.throttleDelay)!==null&&A!==void 0?A:1e3/60),m.state={},m}return e5(x,p),XI(x,[{key:"componentDidMount",value:function(){var w,A;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(w=this.props.margin.left)!==null&&w!==void 0?w:0,top:(A=this.props.margin.top)!==null&&A!==void 0?A:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var w=this.props,A=w.children,m=w.data,b=w.height,_=w.layout,S=Fe(A,st);if(S){var j=S.props.defaultIndex;if(!(typeof j!="number"||j<0||j>this.state.tooltipTicks.length-1)){var C=this.state.tooltipTicks[j]&&this.state.tooltipTicks[j].value,E=Zf(this.state,m,j,C),M=this.state.tooltipTicks[j].coordinate,$=(this.state.offset.top+b)/2,k=_==="horizontal",I=k?{x:M,y:$}:{y:M,x:$},D=this.state.formattedGraphicalItems.find(function(q){var H=q.item;return H.type.name==="Scatter"});D&&(I=R(R({},I),D.props.points[j].tooltipPosition),E=D.props.points[j].tooltipPayload);var N={activeTooltipIndex:j,isTooltipActive:!0,activeLabel:C,activePayload:E,activeCoordinate:I};this.setState(N),this.renderCursor(S),this.accessibilityManager.setIndex(j)}}}},{key:"getSnapshotBeforeUpdate",value:function(w,A){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==A.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==w.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==w.margin){var m,b;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(b=this.props.margin.top)!==null&&b!==void 0?b:0}})}return null}},{key:"componentDidUpdate",value:function(w){El([Fe(w.children,st)],[Fe(this.props.children,st)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var w=Fe(this.props.children,st);if(w&&typeof w.props.shared=="boolean"){var A=w.props.shared?"axis":"item";return u.indexOf(A)>=0?A:i}return i}},{key:"getMouseInfo",value:function(w){if(!this.container)return null;var A=this.container,m=A.getBoundingClientRect(),b=L_(m),_={chartX:Math.round(w.pageX-b.left),chartY:Math.round(w.pageY-b.top)},S=m.width/A.offsetWidth||1,j=this.inRange(_.chartX,_.chartY,S);if(!j)return null;var C=this.state,E=C.xAxisMap,M=C.yAxisMap,$=this.getTooltipEventType(),k=Ym(this.state,this.props.data,this.props.layout,j);if($!=="axis"&&E&&M){var I=kt(E).scale,D=kt(M).scale,N=I&&I.invert?I.invert(_.chartX):null,q=D&&D.invert?D.invert(_.chartY):null;return R(R({},_),{},{xValue:N,yValue:q},k)}return k?R(R({},_),k):null}},{key:"inRange",value:function(w,A){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,b=this.props.layout,_=w/m,S=A/m;if(b==="horizontal"||b==="vertical"){var j=this.state.offset,C=_>=j.left&&_<=j.left+j.width&&S>=j.top&&S<=j.top+j.height;return C?{x:_,y:S}:null}var E=this.state,M=E.angleAxisMap,$=E.radiusAxisMap;if(M&&$){var k=kt(M);return e1({x:_,y:S},k)}return null}},{key:"parseEventsOfWrapper",value:function(){var w=this.props.children,A=this.getTooltipEventType(),m=Fe(w,st),b={};m&&A==="axis"&&(m.props.trigger==="click"?b={onClick:this.handleClick}:b={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var _=Ba(this.props,this.handleOuterEvent);return R(R({},_),b)}},{key:"addListener",value:function(){xl.on(wl,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){xl.removeListener(wl,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(w,A,m){for(var b=this.state.formattedGraphicalItems,_=0,S=b.length;_<S;_++){var j=b[_];if(j.item===w||j.props.key===w.key||A===Ot(j.item.type)&&m===j.childIndex)return j}return null}},{key:"renderClipPath",value:function(){var w=this.clipPathId,A=this.state.offset,m=A.left,b=A.top,_=A.height,S=A.width;return P.createElement("defs",null,P.createElement("clipPath",{id:w},P.createElement("rect",{x:m,y:b,height:_,width:S})))}},{key:"getXScales",value:function(){var w=this.state.xAxisMap;return w?Object.entries(w).reduce(function(A,m){var b=Gm(m,2),_=b[0],S=b[1];return R(R({},A),{},W({},_,S.scale))},{}):null}},{key:"getYScales",value:function(){var w=this.state.yAxisMap;return w?Object.entries(w).reduce(function(A,m){var b=Gm(m,2),_=b[0],S=b[1];return R(R({},A),{},W({},_,S.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(w){var A;return(A=this.state.xAxisMap)===null||A===void 0||(A=A[w])===null||A===void 0?void 0:A.scale}},{key:"getYScaleByAxisId",value:function(w){var A;return(A=this.state.yAxisMap)===null||A===void 0||(A=A[w])===null||A===void 0?void 0:A.scale}},{key:"getItemByXY",value:function(w){var A=this.state,m=A.formattedGraphicalItems,b=A.activeItem;if(m&&m.length)for(var _=0,S=m.length;_<S;_++){var j=m[_],C=j.props,E=j.item,M=E.type.defaultProps!==void 0?R(R({},E.type.defaultProps),E.props):E.props,$=Ot(E.type);if($==="Bar"){var k=(C.data||[]).find(function(q){return KT(w,q)});if(k)return{graphicalItem:j,payload:k}}else if($==="RadialBar"){var I=(C.data||[]).find(function(q){return e1(w,q)});if(I)return{graphicalItem:j,payload:I}}else if(po(j,b)||vo(j,b)||aa(j,b)){var D=g3({graphicalItem:j,activeTooltipItem:b,itemData:M.data}),N=M.activeIndex===void 0?D:M.activeIndex;return{graphicalItem:R(R({},j),{},{childIndex:N}),payload:aa(j,b)?M.data[D]:j.props.data[D]}}}return null}},{key:"render",value:function(){var w=this;if(!dd(this))return null;var A=this.props,m=A.children,b=A.className,_=A.width,S=A.height,j=A.style,C=A.compact,E=A.title,M=A.desc,$=Km(A,VI),k=K($,!1);if(C)return P.createElement(Cm,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},P.createElement(Tl,Pr({},k,{width:_,height:S,title:E,desc:M}),this.renderClipPath(),vd(m,this.renderMap)));if(this.props.accessibilityLayer){var I,D;k.tabIndex=(I=this.props.tabIndex)!==null&&I!==void 0?I:0,k.role=(D=this.props.role)!==null&&D!==void 0?D:"application",k.onKeyDown=function(q){w.accessibilityManager.keyboardEvent(q)},k.onFocus=function(){w.accessibilityManager.focus()}}var N=this.parseEventsOfWrapper();return P.createElement(Cm,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},P.createElement("div",Pr({className:Q("recharts-wrapper",b),style:R({position:"relative",cursor:"default",width:_,height:S},j)},N,{ref:function(H){w.container=H}}),P.createElement(Tl,Pr({},k,{width:_,height:S,title:E,desc:M,style:o5}),this.renderClipPath(),vd(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])})(g.Component);W(y,"displayName",r),W(y,"defaultProps",R({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),W(y,"getDerivedStateFromProps",function(p,x){var O=p.dataKey,w=p.data,A=p.children,m=p.width,b=p.height,_=p.layout,S=p.stackOffset,j=p.margin,C=x.dataStartIndex,E=x.dataEndIndex;if(x.updateId===void 0){var M=Jm(p);return R(R(R({},M),{},{updateId:0},d(R(R({props:p},M),{},{updateId:0}),x)),{},{prevDataKey:O,prevData:w,prevWidth:m,prevHeight:b,prevLayout:_,prevStackOffset:S,prevMargin:j,prevChildren:A})}if(O!==x.prevDataKey||w!==x.prevData||m!==x.prevWidth||b!==x.prevHeight||_!==x.prevLayout||S!==x.prevStackOffset||!Er(j,x.prevMargin)){var $=Jm(p),k={chartX:x.chartX,chartY:x.chartY,isTooltipActive:x.isTooltipActive},I=R(R({},Ym(x,w,_)),{},{updateId:x.updateId+1}),D=R(R(R({},$),k),I);return R(R(R({},D),d(R({props:p},D),x)),{},{prevDataKey:O,prevData:w,prevWidth:m,prevHeight:b,prevLayout:_,prevStackOffset:S,prevMargin:j,prevChildren:A})}if(!El(A,x.prevChildren)){var N,q,H,Z,U=Fe(A,Wr),V=U&&(N=(q=U.props)===null||q===void 0?void 0:q.startIndex)!==null&&N!==void 0?N:C,G=U&&(H=(Z=U.props)===null||Z===void 0?void 0:Z.endIndex)!==null&&H!==void 0?H:E,fe=V!==C||G!==E,me=!re(w),Be=me&&!fe?x.updateId:x.updateId+1;return R(R({updateId:Be},d(R(R({props:p},x),{},{updateId:Be,dataStartIndex:V,dataEndIndex:G}),x)),{},{prevChildren:A,dataStartIndex:V,dataEndIndex:G})}return null}),W(y,"renderActiveDot",function(p,x,O){var w;return g.isValidElement(p)?w=g.cloneElement(p,x):X(p)?w=p(x):w=P.createElement(Wh,x),P.createElement(ue,{className:"recharts-active-dot",key:O},w)});var v=g.forwardRef(function(x,O){return P.createElement(y,Pr({},x,{ref:O}))});return v.displayName=y.displayName,v},y5=Ex({chartName:"BarChart",GraphicalChild:gr,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:la},{axisType:"yAxis",AxisComp:wo}],formatAxisMap:j$}),m5=Ex({chartName:"RadialBarChart",GraphicalChild:_a,legendContent:"children",defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"angleAxis",AxisComp:ho},{axisType:"radiusAxis",AxisComp:lo}],formatAxisMap:pE,defaultProps:{layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});function g5({usage:e}){const r=[{name:"Empty",value:100},{name:"Usage",value:e}];return T.jsxs(hr,{children:[T.jsx(da,{action:T.jsx(pa,{children:T.jsx(ya,{weight:"bold"})}),avatar:T.jsx(Nt,{children:T.jsx(og,{fontSize:"var(--Icon-fontSize)"})}),title:"App limits"}),T.jsxs(rn,{children:[T.jsx(Ve,{sx:{display:"flex",justifyContent:"center"},children:T.jsx(eg,{fallback:T.jsx(Ve,{sx:{height:"240px"}}),children:T.jsxs(Ve,{sx:{display:"flex",justifyContent:"center",position:"relative",'& .recharts-layer path[name="Empty"]':{display:"none"},"& .recharts-layer .recharts-radial-bar-background-sector":{fill:"var(--mui-palette-neutral-100)"}},children:[T.jsx(m5,{barSize:24,data:r,endAngle:-10,height:240,innerRadius:166,startAngle:190,width:240,children:T.jsx(_a,{animationDuration:300,background:!0,cornerRadius:10,dataKey:"value",endAngle:-320,fill:"var(--mui-palette-primary-main)",startAngle:20})}),T.jsx(Ve,{sx:{alignItems:"center",bottom:0,display:"flex",justifyContent:"center",left:0,position:"absolute",right:0,top:0},children:T.jsx(Ve,{sx:{textAlign:"center",mt:"-40px"},children:T.jsx(ie,{variant:"h5",children:new Intl.NumberFormat("en-US",{style:"percent",maximumFractionDigits:2}).format(e/100)})})})]})})}),T.jsxs(Me,{spacing:2,sx:{mt:"-80px",textAlign:"center"},children:[T.jsx(ie,{variant:"h6",children:"You've almost reached your limit"}),T.jsxs(ie,{color:"text.secondary",variant:"body2",children:["You have used"," ",new Intl.NumberFormat("en-US",{style:"percent",maximumFractionDigits:2}).format(e/100)," of your available spots. Upgrade plan to create more projects."]})]})]}),T.jsx(dr,{}),T.jsx(va,{sx:{justifyContent:"flex-end"},children:T.jsx(Rt,{color:"secondary",startIcon:T.jsx(ug,{}),variant:"contained",children:"Upgrade plan"})})]})}const jx=[{name:"This year",dataKey:"v1",color:"var(--mui-palette-primary-400)"},{name:"Last year",dataKey:"v2",color:"var(--mui-palette-primary-600)"}];function b5({data:e}){return T.jsxs(hr,{children:[T.jsx(da,{title:"App usage"}),T.jsx(rn,{children:T.jsxs(Me,{direction:{xs:"column",sm:"row"},spacing:3,children:[T.jsxs(Me,{spacing:3,sx:{flex:"0 0 auto",justifyContent:"space-between",width:"240px"},children:[T.jsxs(Me,{spacing:2,children:[T.jsx(ie,{color:"success.main",variant:"h2",children:"+28%"}),T.jsxs(ie,{color:"text.secondary",children:["increase in app usage with"," ",T.jsx(ie,{color:"text.primary",component:"span",children:"6,521"})," ","new products purchased"]})]}),T.jsx("div",{children:T.jsxs(ie,{color:"text.secondary",variant:"body2",children:[T.jsx(ie,{color:"primary.main",component:"span",variant:"subtitle2",children:"This year"})," ","is forecasted to increase in your traffic by the end of the current month"]})})]}),T.jsxs(Me,{divider:T.jsx(dr,{}),spacing:2,sx:{flex:"1 1 auto"},children:[T.jsx(eg,{fallback:T.jsx(Ve,{sx:{height:"300px"}}),children:T.jsx($_,{height:300,children:T.jsxs(y5,{barGap:-32,data:e,margin:{top:0,right:0,bottom:0,left:0},children:[T.jsx(dx,{strokeDasharray:"2 4",vertical:!1}),T.jsx(la,{axisLine:!1,dataKey:"name",tickLine:!1,type:"category",xAxisId:0}),T.jsx(la,{axisLine:!1,dataKey:"name",hide:!0,type:"category",xAxisId:1}),T.jsx(wo,{axisLine:!1,domain:[0,50],hide:!0,tickCount:6,type:"number"}),jx.map((r,n)=>T.jsx(gr,{animationDuration:300,barSize:32,dataKey:r.dataKey,fill:r.color,name:r.name,radius:[5,5,5,5],xAxisId:n},r.name)),T.jsx(st,{animationDuration:50,content:T.jsx(w5,{}),cursor:!1})]})})}),T.jsx(x5,{})]})]})})]})}function x5(){return T.jsx(Me,{direction:"row",spacing:2,children:jx.map(e=>T.jsxs(Me,{direction:"row",spacing:1,sx:{alignItems:"center"},children:[T.jsx(Ve,{sx:{bgcolor:e.color,borderRadius:"2px",height:"4px",width:"16px"}}),T.jsx(ie,{color:"text.secondary",variant:"caption",children:e.name})]},e.name))})}function w5({active:e,payload:t}){return e?T.jsx(Rx,{sx:{border:"1px solid var(--mui-palette-divider)",boxShadow:"var(--mui-shadows-16)",p:1},children:T.jsx(Me,{spacing:2,children:t==null?void 0:t.map(r=>T.jsxs(Me,{direction:"row",spacing:3,sx:{alignItems:"center"},children:[T.jsxs(Me,{direction:"row",spacing:1,sx:{alignItems:"center",flex:"1 1 auto"},children:[T.jsx(Ve,{sx:{bgcolor:r.fill,borderRadius:"2px",height:"8px",width:"8px"}}),T.jsx(ie,{sx:{whiteSpace:"nowrap"},children:r.name})]}),T.jsx(ie,{color:"text.secondary",variant:"body2",children:new Intl.NumberFormat("en-US").format(r.value)})]},r.name))})}):null}const O5=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M208,28H188V24a12,12,0,0,0-24,0v4H92V24a12,12,0,0,0-24,0v4H48A20,20,0,0,0,28,48V208a20,20,0,0,0,20,20H208a20,20,0,0,0,20-20V48A20,20,0,0,0,208,28ZM68,52a12,12,0,0,0,24,0h72a12,12,0,0,0,24,0h16V76H52V52ZM52,204V100H204V204Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M216,48V88H40V48a8,8,0,0,1,8-8H208A8,8,0,0,1,216,48Z",opacity:"0.2"}),g.createElement("path",{d:"M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM72,48v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24V80H48V48ZM208,208H48V96H208V208Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32Zm0,48H48V48H72v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M208,34H182V24a6,6,0,0,0-12,0V34H86V24a6,6,0,0,0-12,0V34H48A14,14,0,0,0,34,48V208a14,14,0,0,0,14,14H208a14,14,0,0,0,14-14V48A14,14,0,0,0,208,34ZM48,46H74V56a6,6,0,0,0,12,0V46h84V56a6,6,0,0,0,12,0V46h26a2,2,0,0,1,2,2V82H46V48A2,2,0,0,1,48,46ZM208,210H48a2,2,0,0,1-2-2V94H210V208A2,2,0,0,1,208,210Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM72,48v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24V80H48V48ZM208,208H48V96H208V208Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M208,36H180V24a4,4,0,0,0-8,0V36H84V24a4,4,0,0,0-8,0V36H48A12,12,0,0,0,36,48V208a12,12,0,0,0,12,12H208a12,12,0,0,0,12-12V48A12,12,0,0,0,208,36ZM48,44H76V56a4,4,0,0,0,8,0V44h88V56a4,4,0,0,0,8,0V44h28a4,4,0,0,1,4,4V84H44V48A4,4,0,0,1,48,44ZM208,212H48a4,4,0,0,1-4-4V92H212V208A4,4,0,0,1,208,212Z"}))]]),r0=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:O5}));r0.displayName="CalendarBlankIcon";function A5({events:e}){return T.jsxs(hr,{children:[T.jsx(da,{action:T.jsx(pa,{children:T.jsx(ya,{weight:"bold"})}),avatar:T.jsx(Nt,{children:T.jsx(r0,{fontSize:"var(--Icon-fontSize)"})}),subheader:"Based on the linked bank accounts",title:"Upcoming events"}),T.jsx(rn,{sx:{py:"8px"},children:T.jsx(Wf,{disablePadding:!0,children:e.map(t=>T.jsx(_5,{event:t},t.id))})}),T.jsx(dr,{}),T.jsx(va,{children:T.jsx(Rt,{color:"secondary",endIcon:T.jsx(ar,{}),size:"small",children:"See all events"})})]})}function _5({event:e}){return T.jsxs(Uf,{disableGutters:!0,children:[T.jsx(Gf,{children:T.jsxs(Ve,{sx:{bgcolor:"var(--mui-palette-background-level1)",borderRadius:1.5,flex:"0 0 auto",p:"4px 8px",textAlign:"center"},children:[T.jsx(ie,{variant:"caption",children:Xe(e.createdAt).format("MMM").toUpperCase()}),T.jsx(ie,{variant:"h6",children:Xe(e.createdAt).format("D")})]})}),T.jsx(Kf,{disableTypography:!0,primary:T.jsx(ie,{noWrap:!0,variant:"subtitle2",children:e.title}),secondary:T.jsx(ie,{color:"text.secondary",noWrap:!0,variant:"body2",children:e.description})}),T.jsx(pa,{children:T.jsx(r0,{})})]},e.id)}function Al({action:e,description:t,icon:r,label:n,title:a}){return T.jsxs(hr,{children:[T.jsx(rn,{children:T.jsxs(Me,{spacing:2,children:[T.jsx("div",{children:T.jsxs(Me,{direction:"row",spacing:1,sx:{alignItems:"center",border:"1px solid var(--mui-palette-divider)",borderRadius:1.5,boxShadow:"var(--mui-shadows-8)",display:"inline-flex",p:"6px 12px"},children:[T.jsx(r,{fontSize:"var(--icon-fontSize-md)"}),T.jsx(ie,{variant:"subtitle2",children:n})]})}),T.jsxs(Me,{spacing:1,children:[T.jsx(ie,{variant:"h6",children:a}),T.jsx(ie,{color:"text.secondary",variant:"body2",children:t})]})]})}),T.jsx(dr,{}),T.jsx(va,{children:e})]})}const S5=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M152.58,66.35a130.61,130.61,0,0,1,0,123.3,12,12,0,1,1-21.17-11.3,106.7,106.7,0,0,0,0-100.7,12,12,0,1,1,21.16-11.3ZM100.36,77.41a12,12,0,0,0-5,16.23,73,73,0,0,1,0,68.72,12,12,0,1,0,21.18,11.28,97,97,0,0,0,0-91.28A12,12,0,0,0,100.36,77.41ZM236,128A108,108,0,1,1,128,20,108.12,108.12,0,0,1,236,128Zm-24,0a84,84,0,1,0-84,84A84.09,84.09,0,0,0,212,128Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M224,128a96,96,0,1,1-96-96A96,96,0,0,1,224,128Z",opacity:"0.2"}),g.createElement("path",{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216ZM97.07,100.26a59.33,59.33,0,0,1,0,55.48,8,8,0,1,1-14.14-7.48,42.79,42.79,0,0,0,0-40.52,8,8,0,0,1,14.14-7.48Zm56-32a126.67,126.67,0,0,1,0,119.54A8,8,0,0,1,139,180.23a110.62,110.62,0,0,0,0-104.46,8,8,0,0,1,14.12-7.54Zm-28,16a93,93,0,0,1,0,87.52,8,8,0,1,1-14.12-7.52,77,77,0,0,0,0-72.48,8,8,0,1,1,14.12-7.52Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24ZM97.07,155.74a8,8,0,1,1-14.14-7.48,42.79,42.79,0,0,0,0-40.52,8,8,0,0,1,14.14-7.48A59.33,59.33,0,0,1,97.07,155.74Zm28,16a8,8,0,1,1-14.12-7.52,77.07,77.07,0,0,0,0-72.48,8,8,0,1,1,14.12-7.52A93,93,0,0,1,125.06,171.76Zm28,16A8,8,0,0,1,139,180.23a110.62,110.62,0,0,0,0-104.46,8,8,0,0,1,14.12-7.54A126.67,126.67,0,0,1,153.07,187.77Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M95.31,101.2a57.37,57.37,0,0,1,0,53.6,6,6,0,0,1-10.62-5.6,44.75,44.75,0,0,0,0-42.4,6,6,0,1,1,10.62-5.6Zm47.86-34.49a6,6,0,0,0-2.46,8.12,112.67,112.67,0,0,1,0,106.34,6,6,0,1,0,10.58,5.66,124.65,124.65,0,0,0,0-117.66A6,6,0,0,0,143.17,66.71Zm-28,16a6,6,0,0,0-2.48,8.12,79,79,0,0,1,0,74.36,6,6,0,0,0,10.6,5.64,91,91,0,0,0,0-85.64A6,6,0,0,0,115.18,82.7ZM230,128A102,102,0,1,1,128,26,102.12,102.12,0,0,1,230,128Zm-12,0a90,90,0,1,0-90,90A90.1,90.1,0,0,0,218,128Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216ZM97.07,100.26a59.33,59.33,0,0,1,0,55.48,8,8,0,1,1-14.14-7.48,42.79,42.79,0,0,0,0-40.52,8,8,0,0,1,14.14-7.48Zm56-32a126.67,126.67,0,0,1,0,119.54A8,8,0,0,1,139,180.23a110.62,110.62,0,0,0,0-104.46,8,8,0,0,1,14.12-7.54Zm-28,16a93,93,0,0,1,0,87.52,8,8,0,1,1-14.12-7.52,77,77,0,0,0,0-72.48,8,8,0,1,1,14.12-7.52Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M93.54,102.13a55.39,55.39,0,0,1,0,51.74A4,4,0,0,1,90,156a4.07,4.07,0,0,1-1.87-.46,4,4,0,0,1-1.67-5.41,46.73,46.73,0,0,0,0-44.26,4,4,0,1,1,7.08-3.74Zm50.58-33.66a4,4,0,0,0-1.65,5.41,114.67,114.67,0,0,1,0,108.24,4,4,0,1,0,7.06,3.76,122.65,122.65,0,0,0,0-115.76A4,4,0,0,0,144.12,68.47Zm-28,16a4,4,0,0,0-1.65,5.41,81,81,0,0,1,0,76.24,4,4,0,1,0,7.06,3.76,89,89,0,0,0,0-83.76A4,4,0,0,0,116.12,84.47ZM228,128A100,100,0,1,1,128,28,100.11,100.11,0,0,1,228,128Zm-8,0a92,92,0,1,0-92,92A92.1,92.1,0,0,0,220,128Z"}))]]),Tx=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:S5}));Tx.displayName="ContactlessPaymentIcon";function P5({subscriptions:e}){return T.jsxs(hr,{children:[T.jsx(da,{avatar:T.jsx(Nt,{children:T.jsx(Tx,{fontSize:"var(--Icon-fontSize)"})}),title:"Our subscriptions"}),T.jsx(rn,{sx:{pb:"8px"},children:T.jsx(Wf,{disablePadding:!0,children:e.map(t=>T.jsx(E5,{subscription:t},t.id))})}),T.jsx(dr,{}),T.jsx(va,{children:T.jsx(Rt,{color:"secondary",endIcon:T.jsx(ar,{}),size:"small",children:"See all subscriptions"})})]})}function E5({subscription:e}){const{label:t,color:r}={paid:{label:"Paid",color:"success"},canceled:{label:"Canceled",color:"error"},expiring:{label:"Expiring",color:"warning"}}[e.status];return T.jsxs(Uf,{disableGutters:!0,children:[T.jsx(Gf,{children:T.jsx(Nt,{src:e.icon,sx:{bgcolor:"var(--mui-palette-background-paper)",boxShadow:"var(--mui-shadows-8)",color:"var(--mui-palette-text-primary)"}})}),T.jsx(Kf,{disableTypography:!0,primary:T.jsx(ie,{noWrap:!0,variant:"subtitle2",children:e.title}),secondary:T.jsxs(ie,{sx:{whiteSpace:"nowrap"},variant:"body2",children:[e.costs," ",T.jsxs(ie,{color:"text.secondary",component:"span",variant:"inherit",children:["/",e.billingCycle]})]})}),T.jsx(Dx,{color:r,label:t,size:"small",variant:"soft"}),T.jsx(pa,{children:T.jsx(ya,{weight:"bold"})})]})}const j5=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M244,128v64a12,12,0,0,1-12,12H168a12,12,0,0,1,0-24h35l-67-67-31.51,31.52a12,12,0,0,1-17,0l-72-72a12,12,0,0,1,17-17L96,119l31.51-31.52a12,12,0,0,1,17,0L220,163V128a12,12,0,0,1,24,0Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M232,128v64H168Z",opacity:"0.2"}),g.createElement("path",{d:"M235.06,120.61a8,8,0,0,0-8.72,1.73L200,148.69,141.66,90.34a8,8,0,0,0-11.32,0L96,124.69,29.66,58.34A8,8,0,0,0,18.34,69.66l72,72a8,8,0,0,0,11.32,0L136,107.31,188.69,160l-26.35,26.34A8,8,0,0,0,168,200h64a8,8,0,0,0,8-8V128A8,8,0,0,0,235.06,120.61ZM224,184H187.31L224,147.31Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M240,128v64a8,8,0,0,1-8,8H168a8,8,0,0,1-5.66-13.66L188.69,160,136,107.31l-34.34,34.35a8,8,0,0,1-11.32,0l-72-72A8,8,0,0,1,29.66,58.34L96,124.69l34.34-34.35a8,8,0,0,1,11.32,0L200,148.69l26.34-26.35A8,8,0,0,1,240,128Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M238,128v64a6,6,0,0,1-6,6H168a6,6,0,0,1,0-12h49.52L136,104.49l-35.76,35.75a6,6,0,0,1-8.48,0l-72-72a6,6,0,0,1,8.48-8.48L96,127.51l35.76-35.75a6,6,0,0,1,8.48,0L226,177.52V128a6,6,0,0,1,12,0Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M240,128v64a8,8,0,0,1-8,8H168a8,8,0,0,1,0-16h44.69L136,107.31l-34.34,34.35a8,8,0,0,1-11.32,0l-72-72A8,8,0,0,1,29.66,58.34L96,124.69l34.34-34.35a8,8,0,0,1,11.32,0L224,172.69V128a8,8,0,0,1,16,0Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M236,128v64a4,4,0,0,1-4,4H168a4,4,0,0,1,0-8h54.34L136,101.66,98.83,138.83a4,4,0,0,1-5.66,0l-72-72a4,4,0,0,1,5.66-5.66L96,130.34l37.17-37.17a4,4,0,0,1,5.66,0L228,182.34V128a4,4,0,0,1,8,0Z"}))]]),Mx=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:j5}));Mx.displayName="TrendDownIcon";const T5=new Map([["bold",g.createElement(g.Fragment,null,g.createElement("path",{d:"M244,56v64a12,12,0,0,1-24,0V85l-75.51,75.52a12,12,0,0,1-17,0L96,129,32.49,192.49a12,12,0,0,1-17-17l72-72a12,12,0,0,1,17,0L136,135l67-67H168a12,12,0,0,1,0-24h64A12,12,0,0,1,244,56Z"}))],["duotone",g.createElement(g.Fragment,null,g.createElement("path",{d:"M232,56v64L168,56Z",opacity:"0.2"}),g.createElement("path",{d:"M232,48H168a8,8,0,0,0-5.66,13.66L188.69,88,136,140.69l-34.34-34.35a8,8,0,0,0-11.32,0l-72,72a8,8,0,0,0,11.32,11.32L96,123.31l34.34,34.35a8,8,0,0,0,11.32,0L200,99.31l26.34,26.35A8,8,0,0,0,240,120V56A8,8,0,0,0,232,48Zm-8,52.69L187.31,64H224Z"}))],["fill",g.createElement(g.Fragment,null,g.createElement("path",{d:"M240,56v64a8,8,0,0,1-13.66,5.66L200,99.31l-58.34,58.35a8,8,0,0,1-11.32,0L96,123.31,29.66,189.66a8,8,0,0,1-11.32-11.32l72-72a8,8,0,0,1,11.32,0L136,140.69,188.69,88,162.34,61.66A8,8,0,0,1,168,48h64A8,8,0,0,1,240,56Z"}))],["light",g.createElement(g.Fragment,null,g.createElement("path",{d:"M238,56v64a6,6,0,0,1-12,0V70.48l-85.76,85.76a6,6,0,0,1-8.48,0L96,120.49,28.24,188.24a6,6,0,0,1-8.48-8.48l72-72a6,6,0,0,1,8.48,0L136,143.51,217.52,62H168a6,6,0,0,1,0-12h64A6,6,0,0,1,238,56Z"}))],["regular",g.createElement(g.Fragment,null,g.createElement("path",{d:"M240,56v64a8,8,0,0,1-16,0V75.31l-82.34,82.35a8,8,0,0,1-11.32,0L96,123.31,29.66,189.66a8,8,0,0,1-11.32-11.32l72-72a8,8,0,0,1,11.32,0L136,140.69,212.69,64H168a8,8,0,0,1,0-16h64A8,8,0,0,1,240,56Z"}))],["thin",g.createElement(g.Fragment,null,g.createElement("path",{d:"M236,56v64a4,4,0,0,1-8,0V65.66l-89.17,89.17a4,4,0,0,1-5.66,0L96,117.66,26.83,186.83a4,4,0,0,1-5.66-5.66l72-72a4,4,0,0,1,5.66,0L136,146.34,222.34,60H168a4,4,0,0,1,0-8h64A4,4,0,0,1,236,56Z"}))]]),$x=g.forwardRef((e,t)=>g.createElement(Ze,{ref:t,...e,weights:T5}));$x.displayName="TrendUpIcon";function _l({amount:e,diff:t,icon:r,title:n,trend:a}){return T.jsxs(hr,{children:[T.jsx(rn,{children:T.jsxs(Me,{direction:"row",spacing:3,sx:{alignItems:"center"},children:[T.jsx(Nt,{sx:{"--Avatar-size":"48px",bgcolor:"var(--mui-palette-background-paper)",boxShadow:"var(--mui-shadows-8)",color:"var(--mui-palette-text-primary)"},children:T.jsx(r,{fontSize:"var(--icon-fontSize-lg)"})}),T.jsxs("div",{children:[T.jsx(ie,{color:"text.secondary",variant:"body1",children:n}),T.jsx(ie,{variant:"h3",children:new Intl.NumberFormat("en-US").format(e)})]})]})}),T.jsx(dr,{}),T.jsx(Ve,{sx:{p:"16px"},children:T.jsxs(Me,{direction:"row",spacing:1,sx:{alignItems:"center"},children:[T.jsx(Ve,{sx:{alignItems:"center",color:a==="up"?"var(--mui-palette-success-main)":"var(--mui-palette-error-main)",display:"flex",justifyContent:"center"},children:a==="up"?T.jsx($x,{fontSize:"var(--icon-fontSize-md)"}):T.jsx(Mx,{fontSize:"var(--icon-fontSize-md)"})}),T.jsxs(ie,{color:"text.secondary",variant:"body2",children:[T.jsx(ie,{color:a==="up"?"success.main":"error.main",component:"span",variant:"subtitle2",children:new Intl.NumberFormat("en-US",{style:"percent",maximumFractionDigits:2}).format(t/100)})," ",a==="up"?"increase":"decrease"," vs last month"]})]})})]})}const M5={title:`Overview | Dashboard | ${Lx.name}`};function N5(){return T.jsxs(g.Fragment,{children:[T.jsx(Nx,{children:T.jsx("title",{children:M5.title})}),T.jsx(Ve,{sx:{maxWidth:"var(--Content-maxWidth)",m:"var(--Content-margin)",p:"var(--Content-padding)",width:"var(--Content-width)"},children:T.jsxs(Me,{spacing:4,children:[T.jsxs(Me,{direction:{xs:"column",sm:"row"},spacing:3,sx:{alignItems:"flex-start"},children:[T.jsx(Ve,{sx:{flex:"1 1 auto"},children:T.jsx(ie,{variant:"h4",children:"Overview"})}),T.jsx("div",{children:T.jsx(Rt,{startIcon:T.jsx(Hx,{}),variant:"contained",children:"Dashboard"})})]}),T.jsxs(We,{container:!0,spacing:4,children:[T.jsx(We,{size:{md:4,xs:12},children:T.jsx(_l,{amount:31,diff:15,icon:ng,title:"Tickets",trend:"up"})}),T.jsx(We,{size:{md:4,xs:12},children:T.jsx(_l,{amount:240,diff:5,icon:qx,title:"Sign ups",trend:"down"})}),T.jsx(We,{size:{md:4,xs:12},children:T.jsx(_l,{amount:21,diff:12,icon:ag,title:"Open issues",trend:"up"})}),T.jsx(We,{size:{md:8,xs:12},children:T.jsx(b5,{data:[{name:"Jan",v1:36,v2:19},{name:"Feb",v1:45,v2:23},{name:"Mar",v1:26,v2:12},{name:"Apr",v1:39,v2:20},{name:"May",v1:26,v2:12},{name:"Jun",v1:42,v2:31},{name:"Jul",v1:38,v2:19},{name:"Aug",v1:39,v2:20},{name:"Sep",v1:37,v2:18},{name:"Oct",v1:41,v2:22},{name:"Nov",v1:45,v2:24},{name:"Dec",v1:23,v2:17}]})}),T.jsx(We,{size:{md:4,xs:12},children:T.jsx(P5,{subscriptions:[{id:"supabase",title:"Supabase",icon:ct.companyAvatar5(),costs:"$599",billingCycle:"year",status:"paid"},{id:"vercel",title:"Vercel",icon:ct.companyAvatar4(),costs:"$20",billingCycle:"month",status:"expiring"},{id:"auth0",title:"Auth0",icon:ct.companyAvatar3(),costs:"$20-80",billingCycle:"month",status:"canceled"},{id:"google_cloud",title:"Google Cloud",icon:ct.companyAvatar2(),costs:"$100-200",billingCycle:"month",status:"paid"},{id:"stripe",title:"Stripe",icon:ct.companyAvatar1(),costs:"$70",billingCycle:"month",status:"paid"}]})}),T.jsx(We,{size:{md:4,xs:12},children:T.jsx(Kx,{messages:[{id:"MSG-001",content:"Hello, we spoke earlier on the phone",author:{name:"Alcides Antonio",avatar:ct.avatar10(),status:"online"},createdAt:Xe().subtract(2,"minute").toDate()},{id:"MSG-002",content:"Is the job still available?",author:{name:"Marcus Finn",avatar:ct.avatar9(),status:"offline"},createdAt:Xe().subtract(56,"minute").toDate()},{id:"MSG-003",content:"What is a screening task? I'd like to",author:{name:"Carson Darrin",avatar:ct.avatar3(),status:"online"},createdAt:Xe().subtract(3,"hour").subtract(23,"minute").toDate()},{id:"MSG-004",content:"Still waiting for feedback",author:{name:"Fran Perez",avatar:ct.avatar5(),status:"online"},createdAt:Xe().subtract(8,"hour").subtract(6,"minute").toDate()},{id:"MSG-005",content:"Need more information about campaigns",author:{name:"Jie Yan",avatar:ct.avatar8(),status:"offline"},createdAt:Xe().subtract(10,"hour").subtract(18,"minute").toDate()}]})}),T.jsx(We,{size:{md:4,xs:12},children:T.jsx(A5,{events:[{id:"EV-004",title:"Meeting with partners",description:"17:00 to 18:00",createdAt:Xe().add(1,"day").toDate()},{id:"EV-003",title:"Interview with Jonas",description:"15:30 to 16:45",createdAt:Xe().add(4,"day").toDate()},{id:"EV-002",title:"Doctor's appointment",description:"12:30 to 15:30",createdAt:Xe().add(4,"day").toDate()},{id:"EV-001",title:"Weekly meeting",description:"09:00 to 09:30",createdAt:Xe().add(7,"day").toDate()}]})}),T.jsx(We,{size:{md:4,xs:12},children:T.jsx(g5,{usage:80})}),T.jsx(We,{size:{md:4,xs:12},children:T.jsx(Al,{action:T.jsx(Rt,{color:"secondary",endIcon:T.jsx(ar,{}),size:"small",children:"Search jobs"}),description:"Search for jobs that match your skills and apply to them directly.",icon:tg,label:"Jobs",title:"Find your dream job"})}),T.jsx(We,{size:{md:4,xs:12},children:T.jsx(Al,{action:T.jsx(Rt,{color:"secondary",endIcon:T.jsx(ar,{}),size:"small",children:"Help center"}),description:"Find answers to your questions and get in touch with our team.",icon:Bx,label:"Help center",title:"Need help figuring things out?"})}),T.jsx(We,{size:{md:4,xs:12},children:T.jsx(Al,{action:T.jsx(Rt,{color:"secondary",endIcon:T.jsx(ar,{}),size:"small",children:"Documentation"}),description:"Learn how to get started with our product and make the most of it.",icon:rg,label:"Documentation",title:"Explore documentation"})})]})]})})]})}export{N5 as Page};
//# sourceMappingURL=overview.DlcU2Fcn.js.map
