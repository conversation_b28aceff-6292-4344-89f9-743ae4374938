{"version": 3, "file": "administration-DCdGn-A8.js", "sources": ["../../../src/store/api/generated/administration.generated.ts", "../../../src/store/api/administration.ts"], "sourcesContent": ["import { baseApi as api } from \"../base-api\";\r\nconst injectedRtkApi = api.injectEndpoints({\r\n  endpoints: (build) => ({\r\n    atApiServiceEndpointsAdministrationJobsDeleteJobEndpoint: build.mutation<\r\n      AtApiServiceEndpointsAdministrationJobsDeleteJobEndpointApiResponse,\r\n      AtApiServiceEndpointsAdministrationJobsDeleteJobEndpointApiArg\r\n    >({\r\n      query: (queryArg) => ({\r\n        url: `/api/v2/jobs/${queryArg.entityId}`,\r\n        method: \"DELETE\",\r\n      }),\r\n    }),\r\n    atApiServiceEndpointsAdministrationJobsPutJobEndpoint: build.mutation<\r\n      AtApiServiceEndpointsAdministrationJobsPutJobEndpointApiResponse,\r\n      AtApiServiceEndpointsAdministrationJobsPutJobEndpointApiArg\r\n    >({\r\n      query: (queryArg) => ({\r\n        url: `/api/v2/jobs/${queryArg.entityId}`,\r\n        method: \"PUT\",\r\n        body: queryArg.atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId,\r\n      }),\r\n    }),\r\n    atApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpoint:\r\n      build.mutation<\r\n        AtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointApiResponse,\r\n        AtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointApiArg\r\n      >({\r\n        query: (queryArg) => ({\r\n          url: `/api/v2/jobs/${queryArg.entityId1}/triggers/${queryArg.entityId2}`,\r\n          method: \"DELETE\",\r\n        }),\r\n      }),\r\n    atApiServiceEndpointsAdministrationJobsPutJobTriggerEndpoint:\r\n      build.mutation<\r\n        AtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointApiResponse,\r\n        AtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointApiArg\r\n      >({\r\n        query: (queryArg) => ({\r\n          url: `/api/v2/jobs/${queryArg.entityId1}/triggers/${queryArg.entityId2}`,\r\n          method: \"PUT\",\r\n          body: queryArg.atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId,\r\n        }),\r\n      }),\r\n    atApiServiceEndpointsAdministrationJobsGetJobEndpoint: build.query<\r\n      AtApiServiceEndpointsAdministrationJobsGetJobEndpointApiResponse,\r\n      AtApiServiceEndpointsAdministrationJobsGetJobEndpointApiArg\r\n    >({\r\n      query: (queryArg) => ({ url: `/api/v2/jobs/${queryArg.rootEntityId}` }),\r\n    }),\r\n    atApiServiceEndpointsAdministrationJobsGetJobRunEndpoint: build.query<\r\n      AtApiServiceEndpointsAdministrationJobsGetJobRunEndpointApiResponse,\r\n      AtApiServiceEndpointsAdministrationJobsGetJobRunEndpointApiArg\r\n    >({\r\n      query: (queryArg) => ({\r\n        url: `/api/v2/jobs/jobRuns/${queryArg.rootEntityId}`,\r\n      }),\r\n    }),\r\n    atApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpoint: build.query<\r\n      AtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointApiResponse,\r\n      AtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointApiArg\r\n    >({\r\n      query: (queryArg) => ({\r\n        url: `/api/v2/jobs/jobRuns/${queryArg.jobRunId}/logs`,\r\n        params: {\r\n          $filter: queryArg.$filter,\r\n          $orderby: queryArg.$orderby,\r\n          $select: queryArg.$select,\r\n          $top: queryArg.$top,\r\n          $skip: queryArg.$skip,\r\n          $count: queryArg.$count,\r\n        },\r\n      }),\r\n    }),\r\n    atApiServiceEndpointsAdministrationJobsGetJobRunsEndpoint: build.query<\r\n      AtApiServiceEndpointsAdministrationJobsGetJobRunsEndpointApiResponse,\r\n      AtApiServiceEndpointsAdministrationJobsGetJobRunsEndpointApiArg\r\n    >({\r\n      query: (queryArg) => ({\r\n        url: `/api/v2/jobs/jobRuns`,\r\n        params: {\r\n          $filter: queryArg.$filter,\r\n          $orderby: queryArg.$orderby,\r\n          $select: queryArg.$select,\r\n          $top: queryArg.$top,\r\n          $skip: queryArg.$skip,\r\n          $count: queryArg.$count,\r\n        },\r\n      }),\r\n    }),\r\n    atApiServiceEndpointsAdministrationJobsGetJobsEndpoint: build.query<\r\n      AtApiServiceEndpointsAdministrationJobsGetJobsEndpointApiResponse,\r\n      AtApiServiceEndpointsAdministrationJobsGetJobsEndpointApiArg\r\n    >({\r\n      query: (queryArg) => ({\r\n        url: `/api/v2/jobs`,\r\n        params: {\r\n          $filter: queryArg.$filter,\r\n          $orderby: queryArg.$orderby,\r\n          $select: queryArg.$select,\r\n          $top: queryArg.$top,\r\n          $skip: queryArg.$skip,\r\n          $count: queryArg.$count,\r\n        },\r\n      }),\r\n    }),\r\n    atApiServiceEndpointsAdministrationJobsPostJobEndpoint: build.mutation<\r\n      AtApiServiceEndpointsAdministrationJobsPostJobEndpointApiResponse,\r\n      AtApiServiceEndpointsAdministrationJobsPostJobEndpointApiArg\r\n    >({\r\n      query: (queryArg) => ({\r\n        url: `/api/v2/jobs`,\r\n        method: \"POST\",\r\n        body: queryArg.atApiServiceEndpointsAdministrationModelsJobDto,\r\n      }),\r\n    }),\r\n    atApiServiceEndpointsAdministrationJobsGetJobTriggerEndpoint: build.query<\r\n      AtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointApiResponse,\r\n      AtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointApiArg\r\n    >({\r\n      query: (queryArg) => ({\r\n        url: `/api/v2/jobs/${queryArg.rootEntityId}/triggers/${queryArg.subEntityId1}`,\r\n      }),\r\n    }),\r\n    atApiServiceEndpointsAdministrationJobsGetJobTriggersEndpoint: build.query<\r\n      AtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointApiResponse,\r\n      AtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointApiArg\r\n    >({\r\n      query: (queryArg) => ({\r\n        url: `/api/v2/jobs/${queryArg.jobId}/triggers`,\r\n        params: {\r\n          $filter: queryArg.$filter,\r\n          $orderby: queryArg.$orderby,\r\n          $select: queryArg.$select,\r\n          $top: queryArg.$top,\r\n          $skip: queryArg.$skip,\r\n          $count: queryArg.$count,\r\n        },\r\n      }),\r\n    }),\r\n    atApiServiceEndpointsAdministrationJobsPostJobTriggerEndpoint:\r\n      build.mutation<\r\n        AtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointApiResponse,\r\n        AtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointApiArg\r\n      >({\r\n        query: (queryArg) => ({\r\n          url: `/api/v2/jobs/${queryArg.jobId}/triggers`,\r\n          method: \"POST\",\r\n          body: queryArg.atApiServiceEndpointsAdministrationModelsJobTriggerDto,\r\n        }),\r\n      }),\r\n  }),\r\n  overrideExisting: false,\r\n});\r\nexport { injectedRtkApi as administrationGeneratedApi };\r\nexport type AtApiServiceEndpointsAdministrationJobsDeleteJobEndpointApiResponse =\r\n  unknown;\r\nexport type AtApiServiceEndpointsAdministrationJobsDeleteJobEndpointApiArg = {\r\n  entityId: string;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationJobsPutJobEndpointApiResponse =\r\n  unknown;\r\nexport type AtApiServiceEndpointsAdministrationJobsPutJobEndpointApiArg = {\r\n  entityId: string;\r\n  atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId: AtApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointApiResponse =\r\n  unknown;\r\nexport type AtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointApiArg =\r\n  {\r\n    entityId1: string;\r\n    entityId2: string;\r\n  };\r\nexport type AtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointApiResponse =\r\n  unknown;\r\nexport type AtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointApiArg =\r\n  {\r\n    entityId1: string;\r\n    entityId2: string;\r\n    atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId: AtApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId;\r\n  };\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobEndpointApiResponse =\r\n  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobDto;\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobEndpointApiArg = {\r\n  rootEntityId: string;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobRunEndpointApiResponse =\r\n  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobRunDto;\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobRunEndpointApiArg = {\r\n  rootEntityId: string;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointApiResponse =\r\n  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntitiesResponse;\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointApiArg =\r\n  {\r\n    jobRunId: string;\r\n    $filter?: string | null;\r\n    $orderby?: string | null;\r\n    $select?: string | null;\r\n    $top?: number | null;\r\n    $skip?: number | null;\r\n    $count?: boolean | null;\r\n  };\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobRunsEndpointApiResponse =\r\n  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntitiesResponse;\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobRunsEndpointApiArg = {\r\n  $filter?: string | null;\r\n  $orderby?: string | null;\r\n  $select?: string | null;\r\n  $top?: number | null;\r\n  $skip?: number | null;\r\n  $count?: boolean | null;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobsEndpointApiResponse =\r\n  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntitiesResponse;\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobsEndpointApiArg = {\r\n  $filter?: string | null;\r\n  $orderby?: string | null;\r\n  $select?: string | null;\r\n  $top?: number | null;\r\n  $skip?: number | null;\r\n  $count?: boolean | null;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationJobsPostJobEndpointApiResponse =\r\n  unknown;\r\nexport type AtApiServiceEndpointsAdministrationJobsPostJobEndpointApiArg = {\r\n  atApiServiceEndpointsAdministrationModelsJobDto: AtApiServiceEndpointsAdministrationModelsJobDto;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointApiResponse =\r\n  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobTriggerDto;\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointApiArg =\r\n  {\r\n    rootEntityId: string;\r\n    subEntityId1: string;\r\n  };\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointApiResponse =\r\n  /** status 200 Success */ AtApiServiceEndpointsAdministrationModelsGetEntitiesResponse;\r\nexport type AtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointApiArg =\r\n  {\r\n    jobId: string;\r\n    $filter?: string | null;\r\n    $orderby?: string | null;\r\n    $select?: string | null;\r\n    $top?: number | null;\r\n    $skip?: number | null;\r\n    $count?: boolean | null;\r\n  };\r\nexport type AtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointApiResponse =\r\n  unknown;\r\nexport type AtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointApiArg =\r\n  {\r\n    jobId: string;\r\n    atApiServiceEndpointsAdministrationModelsJobTriggerDto: AtApiServiceEndpointsAdministrationModelsJobTriggerDto;\r\n  };\r\nexport type AtApiServiceEndpointsAdministrationModelsJobDto = {\r\n  id?: number;\r\n  name?: string;\r\n  description?: string | null;\r\n  parameters?: string;\r\n  type?: number;\r\n  disabled?: boolean;\r\n  successEmails?: string | null;\r\n  errorEmails?: string | null;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId =\r\n  {\r\n    model?: AtApiServiceEndpointsAdministrationModelsJobDto | null;\r\n  };\r\nexport type AtApiServiceEndpointsAdministrationModelsJobTriggerDto = {\r\n  id?: number;\r\n  name?: string;\r\n  description?: string | null;\r\n  parameters?: string;\r\n  type?: number;\r\n  disabled?: boolean;\r\n  jobId?: number;\r\n  runParameters?: string | null;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId =\r\n  {\r\n    model?: AtApiServiceEndpointsAdministrationModelsJobTriggerDto | null;\r\n  };\r\nexport type AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobDto =\r\n  {\r\n    entity?: AtApiServiceEndpointsAdministrationModelsJobDto | null;\r\n  };\r\nexport type AtPrimitivesEnumsJobRunState = 0 | 1 | 2;\r\nexport type AtPrimitivesEnumsJobResultType = 0 | 1 | 2 | 3 | 4;\r\nexport type AtApiServiceEndpointsAdministrationModelsJobRunDto = {\r\n  id?: number;\r\n  jobId?: number;\r\n  jobName?: string;\r\n  state?: AtPrimitivesEnumsJobRunState;\r\n  result?: AtPrimitivesEnumsJobResultType;\r\n  triggered?: string;\r\n  started?: string | null;\r\n  finished?: string | null;\r\n  authorId?: number | null;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobRunDto =\r\n  {\r\n    entity?: AtApiServiceEndpointsAdministrationModelsJobRunDto | null;\r\n  };\r\nexport type AtApiServiceEndpointsAdministrationModelsGetEntitiesResponse = {\r\n  value?: {\r\n    [key: string]: any;\r\n  }[];\r\n  count?: number | null;\r\n};\r\nexport type AtApiServiceEndpointsAdministrationModelsGetEntityResponseOfJobTriggerDto =\r\n  {\r\n    entity?: AtApiServiceEndpointsAdministrationModelsJobTriggerDto | null;\r\n  };\r\nexport const {\r\n  useAtApiServiceEndpointsAdministrationJobsDeleteJobEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsPutJobEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobRunEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobRunsEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobsEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsPostJobEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointMutation,\r\n} = injectedRtkApi;\r\n", "import {\r\n  administrationGenerated<PERSON>pi,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobsEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsPostJobEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsPutJobEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsDeleteJobEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointMutation,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobRunEndpointQuery,\r\n  useAtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointQuery,\r\n} from './generated/administration.generated';\r\n\r\n// Re-export only types and the enhanced API, not the verbose hooks\r\nexport type * from './generated/administration.generated';\r\n\r\n// Enhanced API with proper cache tags using enhanceEndpoints\r\nexport const administrationApi = administrationGeneratedApi.enhanceEndpoints({\r\n  addTagTypes: ['Job', 'JobTrigger'],\r\n  endpoints: {\r\n    // Job Management Endpoints\r\n    atApiServiceEndpointsAdministrationJobsGetJobsEndpoint: {\r\n      providesTags: ['Job'],\r\n    },\r\n    atApiServiceEndpointsAdministrationJobsGetJobEndpoint: {\r\n      providesTags: (result, error, arg) => [{ type: 'Job', id: arg.rootEntityId }],\r\n    },\r\n    atApiServiceEndpointsAdministrationJobsPostJobEndpoint: {\r\n      invalidatesTags: ['Job'],\r\n    },\r\n    atApiServiceEndpointsAdministrationJobsPutJobEndpoint: {\r\n      invalidatesTags: (result, error, arg) => [\r\n        { type: 'Job', id: arg.entityId },\r\n        'Job',\r\n      ],\r\n    },\r\n    atApiServiceEndpointsAdministrationJobsDeleteJobEndpoint: {\r\n      invalidatesTags: (result, error, arg) => [\r\n        { type: 'Job', id: arg.entityId },\r\n        'Job',\r\n      ],\r\n    },\r\n\r\n    // Job Trigger Management Endpoints\r\n    atApiServiceEndpointsAdministrationJobsGetJobTriggersEndpoint: {\r\n      providesTags: (result, error, arg) => [\r\n        { type: 'JobTrigger', id: `job-${arg.jobId}` },\r\n        'JobTrigger',\r\n      ],\r\n    },\r\n    atApiServiceEndpointsAdministrationJobsGetJobTriggerEndpoint: {\r\n      providesTags: (result, error, arg) => [\r\n        { type: 'JobTrigger', id: arg.subEntityId1 },\r\n      ],\r\n    },\r\n    atApiServiceEndpointsAdministrationJobsPostJobTriggerEndpoint: {\r\n      invalidatesTags: (result, error, arg) => [\r\n        { type: 'JobTrigger', id: `job-${arg.jobId}` },\r\n        'JobTrigger',\r\n      ],\r\n    },\r\n    atApiServiceEndpointsAdministrationJobsPutJobTriggerEndpoint: {\r\n      invalidatesTags: (result, error, arg) => [\r\n        { type: 'JobTrigger', id: arg.entityId2 },\r\n        { type: 'JobTrigger', id: `job-${arg.entityId1}` },\r\n        'JobTrigger',\r\n      ],\r\n    },\r\n    atApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpoint: {\r\n      invalidatesTags: (result, error, arg) => [\r\n        { type: 'JobTrigger', id: arg.entityId2 },\r\n        { type: 'JobTrigger', id: `job-${arg.entityId1}` },\r\n        'JobTrigger',\r\n      ],\r\n    },\r\n  },\r\n});\r\n\r\n// Clean hook exports that map to the verbose generated hook names\r\nexport const useGetJobsQuery = useAtApiServiceEndpointsAdministrationJobsGetJobsEndpointQuery;\r\nexport const useGetJobQuery = useAtApiServiceEndpointsAdministrationJobsGetJobEndpointQuery;\r\nexport const useGetJobRunQuery = useAtApiServiceEndpointsAdministrationJobsGetJobRunEndpointQuery;\r\nexport const useGetJobRunLogsQuery = useAtApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpointQuery;\r\nexport const useCreateJobMutation = useAtApiServiceEndpointsAdministrationJobsPostJobEndpointMutation;\r\nexport const useUpdateJobMutation = useAtApiServiceEndpointsAdministrationJobsPutJobEndpointMutation;\r\nexport const useDeleteJobMutation = useAtApiServiceEndpointsAdministrationJobsDeleteJobEndpointMutation;\r\n\r\n// Job Trigger hooks\r\nexport const useGetJobTriggersQuery = useAtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointQuery;\r\nexport const useGetJobTriggerQuery = useAtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointQuery;\r\nexport const useCreateJobTriggerMutation = useAtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointMutation;\r\nexport const useUpdateJobTriggerMutation = useAtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointMutation;\r\nexport const useDeleteJobTriggerMutation = useAtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointMutation;"], "names": ["injectedRtkApi", "api", "build", "queryArg", "useAtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointMutation", "useAtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointMutation", "useAtApiServiceEndpointsAdministrationJobsGetJobRunEndpointQuery", "useAtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointQuery", "useAtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointQuery", "useAtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointMutation", "administrationApi", "administrationGeneratedApi", "result", "error", "arg", "useGetJobRunQuery", "useGetJobTriggersQuery", "useGetJobTriggerQuery", "useCreateJobTriggerMutation", "useUpdateJobTriggerMutation", "useDeleteJobTriggerMutation"], "mappings": "wCACA,MAAMA,EAAiBC,EAAI,gBAAgB,CACzC,UAAYC,IAAW,CACrB,yDAA0DA,EAAM,SAG9D,CACA,MAAQC,IAAc,CACpB,IAAK,gBAAgBA,EAAS,QAAQ,GACtC,OAAQ,QAAA,EACV,CACD,EACD,sDAAuDD,EAAM,SAG3D,CACA,MAAQC,IAAc,CACpB,IAAK,gBAAgBA,EAAS,QAAQ,GACtC,OAAQ,MACR,KAAMA,EAAS,yEAAA,EACjB,CACD,EACD,gEACED,EAAM,SAGJ,CACA,MAAQC,IAAc,CACpB,IAAK,gBAAgBA,EAAS,SAAS,aAAaA,EAAS,SAAS,GACtE,OAAQ,QAAA,EACV,CACD,EACH,6DACED,EAAM,SAGJ,CACA,MAAQC,IAAc,CACpB,IAAK,gBAAgBA,EAAS,SAAS,aAAaA,EAAS,SAAS,GACtE,OAAQ,MACR,KAAMA,EAAS,+FAAA,EACjB,CACD,EACH,sDAAuDD,EAAM,MAG3D,CACA,MAAQC,IAAc,CAAE,IAAK,gBAAgBA,EAAS,YAAY,EAAA,EAAG,CACtE,EACD,yDAA0DD,EAAM,MAG9D,CACA,MAAQC,IAAc,CACpB,IAAK,wBAAwBA,EAAS,YAAY,EAAA,EACpD,CACD,EACD,6DAA8DD,EAAM,MAGlE,CACA,MAAQC,IAAc,CACpB,IAAK,wBAAwBA,EAAS,QAAQ,QAC9C,OAAQ,CACN,QAASA,EAAS,QAClB,SAAUA,EAAS,SACnB,QAASA,EAAS,QAClB,KAAMA,EAAS,KACf,MAAOA,EAAS,MAChB,OAAQA,EAAS,MAAA,CACnB,EACF,CACD,EACD,0DAA2DD,EAAM,MAG/D,CACA,MAAQC,IAAc,CACpB,IAAK,uBACL,OAAQ,CACN,QAASA,EAAS,QAClB,SAAUA,EAAS,SACnB,QAASA,EAAS,QAClB,KAAMA,EAAS,KACf,MAAOA,EAAS,MAChB,OAAQA,EAAS,MAAA,CACnB,EACF,CACD,EACD,uDAAwDD,EAAM,MAG5D,CACA,MAAQC,IAAc,CACpB,IAAK,eACL,OAAQ,CACN,QAASA,EAAS,QAClB,SAAUA,EAAS,SACnB,QAASA,EAAS,QAClB,KAAMA,EAAS,KACf,MAAOA,EAAS,MAChB,OAAQA,EAAS,MAAA,CACnB,EACF,CACD,EACD,uDAAwDD,EAAM,SAG5D,CACA,MAAQC,IAAc,CACpB,IAAK,eACL,OAAQ,OACR,KAAMA,EAAS,+CAAA,EACjB,CACD,EACD,6DAA8DD,EAAM,MAGlE,CACA,MAAQC,IAAc,CACpB,IAAK,gBAAgBA,EAAS,YAAY,aAAaA,EAAS,YAAY,EAAA,EAC9E,CACD,EACD,8DAA+DD,EAAM,MAGnE,CACA,MAAQC,IAAc,CACpB,IAAK,gBAAgBA,EAAS,KAAK,YACnC,OAAQ,CACN,QAASA,EAAS,QAClB,SAAUA,EAAS,SACnB,QAASA,EAAS,QAClB,KAAMA,EAAS,KACf,MAAOA,EAAS,MAChB,OAAQA,EAAS,MAAA,CACnB,EACF,CACD,EACD,8DACED,EAAM,SAGJ,CACA,MAAQC,IAAc,CACpB,IAAK,gBAAgBA,EAAS,KAAK,YACnC,OAAQ,OACR,KAAMA,EAAS,sDAAA,EACjB,CACD,CAAA,GAEL,iBAAkB,EACpB,CAAC,EAgKY,CAGX,2EAAAC,EACA,wEAAAC,EAEA,iEAAAC,EAKA,qEAAAC,EACA,sEAAAC,EACA,yEAAAC,CACF,EAAIT,EClTSU,EAAoBC,EAA2B,iBAAiB,CAC3E,YAAa,CAAC,MAAO,YAAY,EACjC,UAAW,CAET,uDAAwD,CACtD,aAAc,CAAC,KAAK,CAAA,EAEtB,sDAAuD,CACrD,aAAc,CAACC,EAAQC,EAAOC,IAAQ,CAAC,CAAE,KAAM,MAAO,GAAIA,EAAI,YAAA,CAAc,CAAA,EAE9E,uDAAwD,CACtD,gBAAiB,CAAC,KAAK,CAAA,EAEzB,sDAAuD,CACrD,gBAAiB,CAACF,EAAQC,EAAOC,IAAQ,CACvC,CAAE,KAAM,MAAO,GAAIA,EAAI,QAAA,EACvB,KAAA,CACF,EAEF,yDAA0D,CACxD,gBAAiB,CAACF,EAAQC,EAAOC,IAAQ,CACvC,CAAE,KAAM,MAAO,GAAIA,EAAI,QAAA,EACvB,KAAA,CACF,EAIF,8DAA+D,CAC7D,aAAc,CAACF,EAAQC,EAAOC,IAAQ,CACpC,CAAE,KAAM,aAAc,GAAI,OAAOA,EAAI,KAAK,EAAA,EAC1C,YAAA,CACF,EAEF,6DAA8D,CAC5D,aAAc,CAACF,EAAQC,EAAOC,IAAQ,CACpC,CAAE,KAAM,aAAc,GAAIA,EAAI,YAAA,CAAa,CAC7C,EAEF,8DAA+D,CAC7D,gBAAiB,CAACF,EAAQC,EAAOC,IAAQ,CACvC,CAAE,KAAM,aAAc,GAAI,OAAOA,EAAI,KAAK,EAAA,EAC1C,YAAA,CACF,EAEF,6DAA8D,CAC5D,gBAAiB,CAACF,EAAQC,EAAOC,IAAQ,CACvC,CAAE,KAAM,aAAc,GAAIA,EAAI,SAAA,EAC9B,CAAE,KAAM,aAAc,GAAI,OAAOA,EAAI,SAAS,EAAA,EAC9C,YAAA,CACF,EAEF,gEAAiE,CAC/D,gBAAiB,CAACF,EAAQC,EAAOC,IAAQ,CACvC,CAAE,KAAM,aAAc,GAAIA,EAAI,SAAA,EAC9B,CAAE,KAAM,aAAc,GAAI,OAAOA,EAAI,SAAS,EAAA,EAC9C,YAAA,CACF,CACF,CAEJ,CAAC,EAKYC,EAAoBT,EAOpBU,EAAyBR,EACzBS,EAAwBV,EACxBW,EAA8BT,EAC9BU,EAA8Bd,EAC9Be,EAA8BhB"}