{"version": 3, "file": "edit-CaGFsDnU.js", "sources": ["../../../node_modules/@phosphor-icons/react/dist/defs/Check.es.js", "../../../node_modules/@phosphor-icons/react/dist/ssr/Check.es.js", "../../../node_modules/@phosphor-icons/react/dist/ssr/PencilSimple.es.js", "../../../node_modules/@phosphor-icons/react/dist/defs/Trash.es.js", "../../../node_modules/@phosphor-icons/react/dist/ssr/Trash.es.js", "../../../node_modules/@phosphor-icons/react/dist/csr/Trash.es.js", "../../../src/components/core/option.tsx", "../../../src/store/api/jobs-api.ts", "../../../src/pages/administration/jobs/components/jobs-emails-list.tsx", "../../../src/pages/administration/jobs/components/job-edit-form.tsx", "../../../src/pages/administration/jobs/components/job-triggers-list.tsx", "../../../src/pages/administration/jobs/edit.tsx"], "sourcesContent": ["import * as e from \"react\";\nconst a = /* @__PURE__ */ new Map([\n  [\n    \"bold\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M232.49,80.49l-128,128a12,12,0,0,1-17,0l-56-56a12,12,0,1,1,17-17L96,183,215.51,63.51a12,12,0,0,1,17,17Z\" }))\n  ],\n  [\n    \"duotone\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\n      \"path\",\n      {\n        d: \"M232,56V200a16,16,0,0,1-16,16H40a16,16,0,0,1-16-16V56A16,16,0,0,1,40,40H216A16,16,0,0,1,232,56Z\",\n        opacity: \"0.2\"\n      }\n    ), /* @__PURE__ */ e.createElement(\"path\", { d: \"M205.66,85.66l-96,96a8,8,0,0,1-11.32,0l-40-40a8,8,0,0,1,11.32-11.32L104,164.69l90.34-90.35a8,8,0,0,1,11.32,11.32Z\" }))\n  ],\n  [\n    \"fill\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40ZM205.66,85.66l-96,96a8,8,0,0,1-11.32,0l-40-40a8,8,0,0,1,11.32-11.32L104,164.69l90.34-90.35a8,8,0,0,1,11.32,11.32Z\" }))\n  ],\n  [\n    \"light\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M228.24,76.24l-128,128a6,6,0,0,1-8.48,0l-56-56a6,6,0,0,1,8.48-8.48L96,191.51,219.76,67.76a6,6,0,0,1,8.48,8.48Z\" }))\n  ],\n  [\n    \"regular\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z\" }))\n  ],\n  [\n    \"thin\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M226.83,74.83l-128,128a4,4,0,0,1-5.66,0l-56-56a4,4,0,0,1,5.66-5.66L96,194.34,221.17,69.17a4,4,0,1,1,5.66,5.66Z\" }))\n  ]\n]);\nexport {\n  a as default\n};\n", "import * as e from \"react\";\nimport c from \"../lib/SSRBase.es.js\";\nimport a from \"../defs/Check.es.js\";\nconst o = e.forwardRef((r, t) => /* @__PURE__ */ e.createElement(c, { ref: t, ...r, weights: a }));\no.displayName = \"CheckIcon\";\nconst f = o;\nexport {\n  f as Check,\n  o as CheckIcon\n};\n", "import * as e from \"react\";\nimport r from \"../lib/SSRBase.es.js\";\nimport t from \"../defs/PencilSimple.es.js\";\nconst o = e.forwardRef((i, m) => /* @__PURE__ */ e.createElement(r, { ref: m, ...i, weights: t }));\no.displayName = \"PencilSimpleIcon\";\nconst l = o;\nexport {\n  l as PencilSimple,\n  o as PencilSimpleIcon\n};\n", "import * as a from \"react\";\nconst e = /* @__PURE__ */ new Map([\n  [\n    \"bold\",\n    /* @__PURE__ */ a.createElement(a.Fragment, null, /* @__PURE__ */ a.createElement(\"path\", { d: \"M216,48H180V36A28,28,0,0,0,152,8H104A28,28,0,0,0,76,36V48H40a12,12,0,0,0,0,24h4V208a20,20,0,0,0,20,20H192a20,20,0,0,0,20-20V72h4a12,12,0,0,0,0-24ZM100,36a4,4,0,0,1,4-4h48a4,4,0,0,1,4,4V48H100Zm88,168H68V72H188ZM116,104v64a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Zm48,0v64a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Z\" }))\n  ],\n  [\n    \"duotone\",\n    /* @__PURE__ */ a.createElement(a.Fragment, null, /* @__PURE__ */ a.createElement(\"path\", { d: \"M200,56V208a8,8,0,0,1-8,8H64a8,8,0,0,1-8-8V56Z\", opacity: \"0.2\" }), /* @__PURE__ */ a.createElement(\"path\", { d: \"M216,48H176V40a24,24,0,0,0-24-24H104A24,24,0,0,0,80,40v8H40a8,8,0,0,0,0,16h8V208a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V64h8a8,8,0,0,0,0-16ZM96,40a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8v8H96Zm96,168H64V64H192ZM112,104v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm48,0v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Z\" }))\n  ],\n  [\n    \"fill\",\n    /* @__PURE__ */ a.createElement(a.Fragment, null, /* @__PURE__ */ a.createElement(\"path\", { d: \"M216,48H176V40a24,24,0,0,0-24-24H104A24,24,0,0,0,80,40v8H40a8,8,0,0,0,0,16h8V208a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V64h8a8,8,0,0,0,0-16ZM112,168a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm48,0a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm0-120H96V40a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8Z\" }))\n  ],\n  [\n    \"light\",\n    /* @__PURE__ */ a.createElement(a.Fragment, null, /* @__PURE__ */ a.createElement(\"path\", { d: \"M216,50H174V40a22,22,0,0,0-22-22H104A22,22,0,0,0,82,40V50H40a6,6,0,0,0,0,12H50V208a14,14,0,0,0,14,14H192a14,14,0,0,0,14-14V62h10a6,6,0,0,0,0-12ZM94,40a10,10,0,0,1,10-10h48a10,10,0,0,1,10,10V50H94ZM194,208a2,2,0,0,1-2,2H64a2,2,0,0,1-2-2V62H194ZM110,104v64a6,6,0,0,1-12,0V104a6,6,0,0,1,12,0Zm48,0v64a6,6,0,0,1-12,0V104a6,6,0,0,1,12,0Z\" }))\n  ],\n  [\n    \"regular\",\n    /* @__PURE__ */ a.createElement(a.Fragment, null, /* @__PURE__ */ a.createElement(\"path\", { d: \"M216,48H176V40a24,24,0,0,0-24-24H104A24,24,0,0,0,80,40v8H40a8,8,0,0,0,0,16h8V208a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V64h8a8,8,0,0,0,0-16ZM96,40a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8v8H96Zm96,168H64V64H192ZM112,104v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm48,0v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Z\" }))\n  ],\n  [\n    \"thin\",\n    /* @__PURE__ */ a.createElement(a.Fragment, null, /* @__PURE__ */ a.createElement(\"path\", { d: \"M216,52H172V40a20,20,0,0,0-20-20H104A20,20,0,0,0,84,40V52H40a4,4,0,0,0,0,8H52V208a12,12,0,0,0,12,12H192a12,12,0,0,0,12-12V60h12a4,4,0,0,0,0-8ZM92,40a12,12,0,0,1,12-12h48a12,12,0,0,1,12,12V52H92ZM196,208a4,4,0,0,1-4,4H64a4,4,0,0,1-4-4V60H196ZM108,104v64a4,4,0,0,1-8,0V104a4,4,0,0,1,8,0Zm48,0v64a4,4,0,0,1-8,0V104a4,4,0,0,1,8,0Z\" }))\n  ]\n]);\nexport {\n  e as default\n};\n", "import * as r from \"react\";\nimport s from \"../lib/SSRBase.es.js\";\nimport t from \"../defs/Trash.es.js\";\nconst o = r.forwardRef((a, e) => /* @__PURE__ */ r.createElement(s, { ref: e, ...a, weights: t }));\no.displayName = \"TrashIcon\";\nconst f = o;\nexport {\n  f as Trash,\n  o as TrashIcon\n};\n", "import * as o from \"react\";\nimport s from \"../lib/IconBase.es.js\";\nimport t from \"../defs/Trash.es.js\";\nconst r = o.forwardRef((a, e) => /* @__PURE__ */ o.createElement(s, { ref: e, ...a, weights: t }));\nr.displayName = \"TrashIcon\";\nconst n = r;\nexport {\n  n as Trash,\n  r as TrashIcon\n};\n", "import type * as React from \"react\";\nimport MenuItem from \"@mui/material/MenuItem\";\n\nexport interface OptionProps {\n\tchildren: React.ReactNode;\n\tdisabled?: boolean;\n\tvalue: string | number;\n}\n\nexport function Option({ children, ...props }: OptionProps): React.JSX.Element {\n\treturn <MenuItem {...props}>{children}</MenuItem>;\n}\n", "import { baseApi } from './base-api';\n\nexport interface Job {\n  id?: number;\n  name: string;\n  type: number;\n  description?: string;\n  parameters: string;\n  successEmails?: string;\n  errorEmails?: string;\n  disabled: boolean;\n}\n\nexport interface JobTrigger {\n  id?: number;\n  name: string;\n  description?: string;\n  parameters: string;\n  type: number;\n  disabled: boolean;\n  jobId: number;\n  runParameters?: string;\n}\n\nexport interface JobCreateRequest {\n  name: string;\n  type: number;\n  description?: string;\n  parameters: string;\n  emailSuccess?: string;\n  emailFail?: string;\n  disabled: boolean;\n}\n\nexport interface JobUpdateRequest {\n  name: string;\n  type: number;\n  description?: string;\n  parameters: string;\n  successEmails?: string;\n  errorEmails?: string;\n  disabled: boolean;\n}\n\nexport interface JobsListResponse {\n  value: Job[];\n  count: number;\n}\n\nexport interface JobsTriggersListResponse {\n  value: JobTrigger[];\n  count: number;\n}\n\nexport interface JobDetailResponse {\n  entity: Job;\n}\n\n// OData query parameters interface\nexport interface ODataParams {\n  $top?: number;\n  $skip?: number;\n  $count?: boolean;\n  $filter?: string;\n  $orderby?: string;\n  $select?: string;\n}\n\nexport const jobsApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    // Get jobs list with OData support\n    getJobs: builder.query<JobsListResponse, ODataParams>({\n      query: (params) => {\n        const searchParams = new URLSearchParams();\n        \n        // Add OData parameters\n        if (params.$top !== undefined) {\n          searchParams.append('$top', params.$top.toString());\n        }\n        if (params.$skip !== undefined) {\n          searchParams.append('$skip', params.$skip.toString());\n        }\n        if (params.$count !== undefined) {\n          searchParams.append('$count', params.$count.toString());\n        }\n        if (params.$filter) {\n          searchParams.append('$filter', params.$filter);\n        }\n        if (params.$orderby) {\n          searchParams.append('$orderby', params.$orderby);\n        }\n        if (params.$select) {\n          searchParams.append('$select', params.$select);\n        }\n\n        return {\n          url: `/api/v2/jobs?${searchParams.toString()}`,\n          method: 'GET',\n        };\n      },\n      providesTags: ['Job'],\n    }),\n\n    // Get single job by ID\n    getJob: builder.query<JobDetailResponse, number>({\n      query: (id) => ({\n        url: `/api/v2/jobs/${id}`,\n        method: 'GET',\n      }),\n      providesTags: (result, error, id) => [{ type: 'Job', id }],\n    }),\n\n    // Create new job\n    createJob: builder.mutation<any, JobCreateRequest>({\n      query: (job) => ({\n        url: '/api/v2/jobs',\n        method: 'POST',\n        body: job,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      }),\n      invalidatesTags: ['Job'],\n    }),\n\n    // Update existing job\n    updateJob: builder.mutation<any, { id: number; job: JobUpdateRequest }>({\n      query: ({ id, job }) => ({\n        url: `/api/v2/jobs/${id}`,\n        method: 'PUT',\n        body: { model: job }, // Matching the existing putToApi format\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      }),\n      invalidatesTags: (result, error, { id }) => [{ type: 'Job', id }, 'Job'],\n    }),\n\n    deleteJob: builder.mutation<any, number>({\n      query: (id) => ({\n        url: `/api/v2/jobs/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Job'],\n    }),\n\n    getJobTriggers: builder.query<JobsTriggersListResponse, { jobId: number, params: ODataParams }>({\n      query: ({jobId, params}) => {\n        const searchParams = new URLSearchParams();\n        \n        // Add OData parameters\n        if (params.$top !== undefined) {\n          searchParams.append('$top', params.$top.toString());\n        }\n        if (params.$skip !== undefined) {\n          searchParams.append('$skip', params.$skip.toString());\n        }\n        if (params.$count !== undefined) {\n          searchParams.append('$count', params.$count.toString());\n        }\n        if (params.$filter) {\n          searchParams.append('$filter', params.$filter);\n        }\n        if (params.$orderby) {\n          searchParams.append('$orderby', params.$orderby);\n        }\n        if (params.$select) {\n          searchParams.append('$select', params.$select);\n        }\n\n        return {\n          url: `/api/v2/jobs/${jobId}/triggers?${searchParams.toString()}`,\n          method: 'GET',\n        };\n      },\n      // providesTags: ['Job'],\n    }),\n\n    getJobTrigger: builder.query<JobTrigger, {jobId: number, triggerId: number}>({\n      query: ({jobId, triggerId}) => ({\n        url: `/api/v2/jobs/${jobId}/triggers/${triggerId}`,\n        method: 'GET',\n      }),\n      // providesTags: ['Job'],\n    }),\n\n    createJobTrigger: builder.mutation<any, {jobId: number, trigger: JobTrigger}>({\n      query: ({jobId, trigger}) => ({\n        url: `/api/v2/jobs/${jobId}/triggers`,\n        method: 'POST',\n        body: trigger,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      }),\n      invalidatesTags: ['Job'],\n    }),\n\n    updateJobTrigger: builder.mutation<any, {jobId: number, triggerId: number, trigger: JobTrigger}>({\n      query: ({jobId, triggerId, trigger}) => ({\n        url: `/api/v2/jobs/${jobId}/triggers/${triggerId}`,\n        method: 'PUT',\n        body: { model: trigger },\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      }),\n      invalidatesTags: ['Job'],\n    }),\n\n    deleteJobTrigger: builder.mutation<any, {jobId: number, triggerId: number}>({\n      query: ({jobId, triggerId}) => ({\n        url: `/api/v2/jobs/${jobId}/triggers/${triggerId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Job'],\n    }),\n  }),\n});\n\n// Export hooks for usage in functional components\nexport const {\n  useGetJobsQuery,\n  useGetJobQuery,\n  useCreateJobMutation,\n  useUpdateJobMutation,\n  useDeleteJobMutation,\n  useGetJobTriggersQuery,\n  useGetJobTriggerQuery,\n  useCreateJobTriggerMutation,\n  useUpdateJobTriggerMutation,\n  useDeleteJobTriggerMutation,\n} = jobsApi;\n", "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Box, Button, Checkbox, FormControl, IconButton, InputLabel, OutlinedInput, Stack, Tooltip } from \"@mui/material\";\r\nimport { DataGridPremium, GridColDef, GridRenderCellParams } from \"@mui/x-data-grid-premium\";\r\nimport { CheckIcon } from \"@phosphor-icons/react/dist/ssr/Check\";\r\nimport { PencilSimpleIcon } from \"@phosphor-icons/react/dist/ssr/PencilSimple\";\r\nimport { TrashIcon } from \"@phosphor-icons/react/dist/ssr/Trash\";\r\nimport { useCallback, useEffect, useRef, useState } from \"react\";\r\nimport './jobs-emails-list.scss';\r\n\r\nexport type JobsEmail = {\r\n    email: string;\r\n    isSuccess: boolean;\r\n    isFailure: boolean;\r\n}\r\n\r\nexport type JobsEmailsListProps = {\r\n    successEmails: string[];\r\n    errorEmails: string[];\r\n    onEmailsChange?: (successEmails: string[], failureEmails: string[]) => void;\r\n};\r\n\r\nexport function JobsEmailsList({successEmails, errorEmails, onEmailsChange}: JobsEmailsListProps): React.JSX.Element {\r\n    const [allEmails, setEmails] = useState<JobsEmail[]>([]);\r\n    const [editingEmail, setEditingEmail] = useState<string | null>(null);\r\n    const [newEmailError, setNewEmailError] = useState<string>(\"\");\r\n    const [editEmailError, setEditEmailError] = useState<string>(\"\");\r\n\r\n    const newEmailRef = useRef<HTMLInputElement>(null);\r\n    const editInputRef = useRef<HTMLInputElement>(null);\r\n\r\n    const notifyEmailsChange = useCallback((emails: JobsEmail[]) => {\r\n        if (onEmailsChange) {\r\n            const successEmails = emails\r\n                .filter(email => email.isSuccess)\r\n                .map(email => email.email);\r\n            const failureEmails = emails\r\n                .filter(email => email.isFailure)\r\n                .map(email => email.email);\r\n            onEmailsChange(successEmails, failureEmails);\r\n        }\r\n    }, [onEmailsChange]);\r\n\r\n    useEffect(() => {\r\n        const emailMap = new Map<string, JobsEmail>();\r\n\r\n        for (const existingEmail of allEmails) {\r\n            emailMap.set(existingEmail.email, {\r\n                email: existingEmail.email,\r\n                isSuccess: false,\r\n                isFailure: false\r\n            });\r\n        }\r\n\r\n        for (const email of successEmails) {\r\n                const trimmedEmail = email.trim();\r\n                const existingEmail = emailMap.get(trimmedEmail)\r\n                if(existingEmail) {\r\n                    existingEmail.isSuccess = true;\r\n                    continue;\r\n                }\r\n\r\n                emailMap.set(trimmedEmail, {\r\n                    email: trimmedEmail,\r\n                    isSuccess: true,\r\n                    isFailure: false\r\n                });\r\n        }\r\n\r\n        for (const email of errorEmails) {\r\n                const trimmedEmail = email.trim();\r\n                const existingEmail = emailMap.get(trimmedEmail)\r\n                if(existingEmail) {\r\n                    existingEmail.isFailure = true;\r\n                    continue;\r\n                }\r\n\r\n                emailMap.set(trimmedEmail, {\r\n                    email: trimmedEmail,\r\n                    isSuccess: false,\r\n                    isFailure: true\r\n                });\r\n        }\r\n\r\n        const deduplicatedEmails = Array.from(emailMap.values());\r\n        setEmails(deduplicatedEmails);\r\n    }, [successEmails, errorEmails]);\r\n\r\n    const addEmail = () => {\r\n        const emailValue = newEmailRef.current?.value || \"\";\r\n        const validationError = validateEmail(emailValue);\r\n\r\n        if (validationError) {\r\n            setNewEmailError(validationError);\r\n            return;\r\n        }\r\n\r\n        setNewEmailError(\"\");\r\n\r\n        const trimmedEmail = emailValue.trim();\r\n        const newEmails = [...allEmails, {\r\n            email: trimmedEmail,\r\n            isSuccess: true,\r\n            isFailure: true\r\n        }];\r\n        setEmails(newEmails);\r\n        notifyEmailsChange(newEmails);\r\n\r\n        if (newEmailRef.current) {\r\n            newEmailRef.current.value = \"\";\r\n        }\r\n    };\r\n\r\n    const handleSuccessChange = (emailAddress: string, checked: boolean) => {\r\n        setEmails(prevEmails => {\r\n            const newEmails = prevEmails.map(email =>\r\n                email.email === emailAddress\r\n                    ? { ...email, isSuccess: checked }\r\n                    : email\r\n            );\r\n            notifyEmailsChange(newEmails);\r\n            return newEmails;\r\n        });\r\n    };\r\n\r\n    const handleFailureChange = (emailAddress: string, checked: boolean) => {\r\n        setEmails(prevEmails => {\r\n            const newEmails = prevEmails.map(email =>\r\n                email.email === emailAddress\r\n                    ? { ...email, isFailure: checked }\r\n                    : email\r\n            );\r\n            notifyEmailsChange(newEmails);\r\n            return newEmails;\r\n        });\r\n    };\r\n\r\n    const handleEditStart = (emailAddress: string) => {\r\n        setEditingEmail(emailAddress);\r\n        setEditEmailError(\"\");\r\n        setTimeout(() => {\r\n            if (editInputRef.current) {\r\n                editInputRef.current.focus();\r\n                requestAnimationFrame(() => {\r\n                    if (editInputRef.current) {\r\n                        editInputRef.current.select();\r\n                    }\r\n                });\r\n            }\r\n        }, 50);\r\n    };\r\n\r\n    const handleEditConfirm = () => {\r\n        const newEmailValue = editInputRef.current?.value || \"\";\r\n        const validationError = validateEmail(newEmailValue, true);\r\n\r\n        if (validationError) {\r\n            setEditEmailError(validationError);\r\n            return;\r\n        }\r\n\r\n        setEditEmailError(\"\");\r\n\r\n        if (editingEmail && newEmailValue.trim()) {\r\n            setEmails(prevEmails => {\r\n                const newEmails = prevEmails.map(email =>\r\n                    email.email === editingEmail\r\n                        ? { ...email, email: newEmailValue.trim() }\r\n                        : email\r\n                );\r\n                notifyEmailsChange(newEmails);\r\n                return newEmails;\r\n            });\r\n        }\r\n        setEditingEmail(null);\r\n    };\r\n\r\n    const handleEditCancel = () => {\r\n        setEditingEmail(null);\r\n        setEditEmailError(\"\"); \r\n    };\r\n\r\n    const handleDeleteEmail = (emailAddress: string) => {\r\n        setEmails(prevEmails => {\r\n            const newEmails = prevEmails.filter(email => email.email !== emailAddress);\r\n            notifyEmailsChange(newEmails);\r\n            return newEmails;\r\n        });\r\n\r\n        if (editingEmail === emailAddress) {\r\n            setEditingEmail(null);\r\n        }\r\n    };\r\n\r\n    const isValidEmail = (email: string): boolean => {\r\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n        return emailRegex.test(email);\r\n    };\r\n\r\n    const validateEmail = (email: string, isEditing: boolean = false): string => {\r\n        if (!email.trim()) {\r\n            return \"Email address is required\";\r\n        }\r\n\r\n        if (!isValidEmail(email.trim())) {\r\n            return \"Please enter a valid email address\";\r\n        }\r\n\r\n        const emailExists = allEmails.some(existingEmail =>\r\n            existingEmail.email === email.trim() &&\r\n            (!isEditing || existingEmail.email !== editingEmail)\r\n        );\r\n\r\n        if (emailExists) {\r\n            return \"This email address already exists\";\r\n        }\r\n\r\n        return \"\";\r\n    };\r\n\r\n    const handleNewEmailChange = () => {\r\n        if (newEmailError) {\r\n            setNewEmailError(\"\");\r\n        }\r\n    };\r\n\r\n    const columns: GridColDef[] = [\r\n        {\r\n            field: 'email',\r\n            headerName: 'Email',\r\n            flex: 1,\r\n            minWidth: 200,\r\n            renderCell: (params: GridRenderCellParams) => {\r\n                const isEditing = editingEmail === params.row.email;\r\n\r\n                if (isEditing) {\r\n                    return (\r\n                        <Tooltip\r\n                            title={editEmailError || \"\"}\r\n                            open={!!editEmailError}\r\n                            placement=\"bottom-start\"\r\n                            arrow\r\n                            slotProps={{\r\n                                tooltip: {\r\n                                    className: 'jobs-emails-list__error-tooltip'\r\n                                }\r\n                            }}\r\n                        >\r\n                            <Box className=\"jobs-emails-list__edit-input-container\">\r\n                                <OutlinedInput\r\n                                    inputRef={editInputRef}\r\n                                    defaultValue={editingEmail}\r\n                                    error={!!editEmailError}\r\n                                    onChange={() => {\r\n                                        if (editEmailError) {\r\n                                            setEditEmailError(\"\");\r\n                                        }\r\n                                    }}\r\n                                    onKeyDown={(e) => {\r\n                                        if (e.key === 'Enter') {\r\n                                            e.preventDefault();\r\n                                            e.stopPropagation();\r\n                                            handleEditConfirm();\r\n                                        } else if (e.key === 'Escape') {\r\n                                            e.preventDefault();\r\n                                            e.stopPropagation();\r\n                                            handleEditCancel();\r\n                                        } else if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(e.key)) {\r\n                                            e.stopPropagation();\r\n                                        }\r\n                                    }}\r\n                                    size=\"small\"\r\n                                    sx={{ width: '100%' }}\r\n                                />\r\n                            </Box>\r\n                        </Tooltip>\r\n                    );\r\n                }\r\n\r\n                return (\r\n                    <span\r\n                        onDoubleClick={() => handleEditStart(params.row.email)}\r\n                        className=\"jobs-emails-list__email-cell\"\r\n                    >\r\n                        {params.value}\r\n                    </span>\r\n                );\r\n            }\r\n        },\r\n        {\r\n            field: 'isSuccess',\r\n            headerName: 'On Successful Completion',\r\n            width: 180,\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n            renderCell: (params: GridRenderCellParams) => (\r\n                <Checkbox\r\n                    checked={params.value as boolean}\r\n                    onChange={(e) => handleSuccessChange(params.row.email, e.target.checked)}\r\n                    size=\"small\"\r\n                    sx={{ padding: 0 }}\r\n                />\r\n            )\r\n        },\r\n        {\r\n            field: 'isFailure',\r\n            headerName: 'Failure',\r\n            width: 120,\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n            renderCell: (params: GridRenderCellParams) => (\r\n                <Checkbox\r\n                    checked={params.value as boolean}\r\n                    onChange={(e) => handleFailureChange(params.row.email, e.target.checked)}\r\n                    size=\"small\"\r\n                    sx={{ padding: 0 }}\r\n                />\r\n            )\r\n        },\r\n        {\r\n            field: 'actions',\r\n            headerName: '',\r\n            width: 100,\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n            sortable: false,\r\n            disableColumnMenu: true,\r\n            renderCell: (params: GridRenderCellParams) => {\r\n                const isEditing = editingEmail === params.row.email;\r\n\r\n                if (isEditing) {\r\n                    return (\r\n                        <IconButton\r\n                            onClick={handleEditConfirm}\r\n                            size=\"small\"\r\n                            sx={{ padding: '4px' }}\r\n                        >\r\n                            <CheckIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                    );\r\n                }\r\n\r\n                return (\r\n                    <Stack direction=\"row\" spacing={0.5} alignItems=\"center\">\r\n                        <IconButton\r\n                            onClick={() => handleEditStart(params.row.email)}\r\n                            size=\"small\"\r\n                            sx={{ padding: '4px' }}\r\n                        >\r\n                            <PencilSimpleIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                        <IconButton\r\n                            onClick={() => handleDeleteEmail(params.row.email)}\r\n                            size=\"small\"\r\n                            sx={{ padding: '4px' }}\r\n                        >\r\n                            <TrashIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                    </Stack>\r\n                );\r\n            }\r\n        }\r\n    ];\r\n\r\n\r\n\treturn (\r\n        <Stack>\r\n            <Stack direction={\"row\"} spacing={2} alignItems={\"end\"}>\r\n                <FormControl fullWidth error={!!newEmailError}>\r\n                    <InputLabel>Add email</InputLabel>\r\n                    <OutlinedInput\r\n                        inputRef={newEmailRef}\r\n                        error={!!newEmailError}\r\n                        onChange={handleNewEmailChange}\r\n                        onKeyDown={(e) => {\r\n                            if (e.key === 'Enter') {\r\n                                e.preventDefault();\r\n                                e.stopPropagation();\r\n                                addEmail();\r\n                                setTimeout(() => {\r\n                                    (e.target as HTMLInputElement).focus();\r\n                                }, 0);\r\n                            }\r\n                        }}\r\n                    />\r\n                    {newEmailError && (\r\n                        <Box className=\"jobs-emails-list__error-message\">\r\n                            {newEmailError}\r\n                        </Box>\r\n                    )}\r\n                </FormControl>\r\n                <Button variant=\"outlined\" onClick={addEmail}>Add</Button>\r\n            </Stack>\r\n            <DataGridPremium\r\n                rows={allEmails}\r\n                columns={columns}\r\n                getRowId={(row) => row.email}\r\n                pagination\r\n                pageSizeOptions={[5, 10, 25]}\r\n                initialState={{\r\n                    pagination: { paginationModel: { pageSize: 10, page: 0 } },\r\n                }}\r\n                disableRowSelectionOnClick\r\n                rowHeight={52}\r\n                className=\"jobs-emails-list__data-grid\"\r\n            />\r\n        </Stack>\r\n\t);\r\n}", "'use client';\n\nimport * as React from 'react';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport Autocomplete from '@mui/material/Autocomplete';\nimport Box from '@mui/material/Box';\nimport Button from '@mui/material/Button';\nimport Card from '@mui/material/Card';\nimport CardActions from '@mui/material/CardActions';\nimport CardContent from '@mui/material/CardContent';\nimport Divider from '@mui/material/Divider';\nimport FormControl from '@mui/material/FormControl';\nimport FormControlLabel from '@mui/material/FormControlLabel';\nimport FormHelperText from '@mui/material/FormHelperText';\nimport Grid from '@mui/material/Grid';\nimport InputLabel from '@mui/material/InputLabel';\nimport Link from '@mui/material/Link';\nimport OutlinedInput from '@mui/material/OutlinedInput';\nimport Stack from '@mui/material/Stack';\nimport Switch from '@mui/material/Switch';\nimport Tooltip from '@mui/material/Tooltip';\nimport Typography from '@mui/material/Typography';\nimport { CaretDownIcon } from '@phosphor-icons/react/dist/ssr/CaretDown';\nimport { Controller, useForm } from 'react-hook-form';\nimport { useNavigate } from 'react-router-dom';\nimport { z as zod } from 'zod';\n\nimport { RouterLink } from '@/components/core/link';\nimport { Option } from '@/components/core/option';\nimport { toast } from '@/components/core/toaster';\nimport { logger } from '@/lib/default-logger';\nimport { paths } from '@/paths';\n\nimport { useCreateJobMutation, useGetJobQuery, useUpdateJobMutation } from '@/store/api/jobs-api';\nimport { JobsEmailsList } from './jobs-emails-list';\n\nconst JobTypeOptions = [\n\t{ label: 'JobType.AgencyWorkersDeactivationJob', value: 0 },\n\t{ label: 'JobType.NullJob', value: 1 },\n\t{ label: 'JobType.ReportJob', value: 2 },\n] as const;\n\nconst schema = zod.object({\n\tname: zod.string().min(1, 'Name is required').max(255),\n\ttype: zod.number().gte(0).lte(JobTypeOptions.length),\n\tdescription: zod.string().max(255).optional(),\n\tparameters: zod.string().min(1, 'Parameters are required'),\n\tsuccessEmails: zod.string().max(512).optional(), // .email('Must be a valid email')\n\terrorEmails: zod.string().max(512).optional(),\n\tdisabled: zod.boolean()\n});\n\ntype Values = zod.infer<typeof schema>;\n\nconst defaultValues = {\n\tname: '',\n    type: 0,\n    description:'',\n    parameters:'',\n\tsuccessEmails: '',\n\terrorEmails: '',\n    disabled: false,\t\n} satisfies Values;\n\ninterface JobEditFormProps {\n\tjobId?: number;\n}\n\nexport function JobEditForm({ jobId }: JobEditFormProps): React.JSX.Element {\n\tconst navigate = useNavigate();\n\tconst isEditMode = jobId !== undefined;\n\n//\tRTK Query hooks\n\tconst { data: jobData, error: jobError, isLoading: isLoadingJob } = useGetJobQuery(jobId!, {\n\t\tskip: !isEditMode || !jobId,\n\t});\n\tconst [createJob, { isLoading: isCreating }] = useCreateJobMutation();\n\tconst [updateJob, { isLoading: isUpdating }] = useUpdateJobMutation();\n\n\tconst {\n\t\tcontrol,\n\t\thandleSubmit,\n\t\tformState: { errors },\n\t\tsetValue,\n\t\twatch,\n\t\treset,\n\t} = useForm<Values>({ defaultValues, resolver: zodResolver(schema) });\n\n\t// Handle job data loading for edit mode\n\tReact.useEffect(() => {\n\t\tif (isEditMode && jobData?.entity) {\n\t\t\tconst formData: Values = {\n\t\t\t\t...jobData.entity\n\t\t\t};\n\t\t\treset(formData);\n\t\t}\n\t}, [jobData, isEditMode, reset]);\n\n\t// Handle job loading error\n\tReact.useEffect(() => {\n\t\tif (jobError) {\n\t\t\tlogger.error(jobError);\n\t\t\ttoast.error('Failed to load job data');\n\t\t}\n\t}, [jobError]);\n\n\tconst handleEmailsChange = React.useCallback((successEmails: string[], failureEmails: string[]) => {\n\t\tsetValue('successEmails', successEmails.join(';'));\n\t\tsetValue('errorEmails', failureEmails.join(';'));\n\t}, [setValue]);\n\n\tconst successEmailsString = watch('successEmails') ?? '';\n\tconst errorEmailsString = watch('errorEmails') ?? '';\n\n\tconst onSubmit = React.useCallback(\n\t\tasync (formValues: Values): Promise<void> => {\n\t\t\ttry {\n\t\t\t\tif (isEditMode && jobId) {\n\t\t\t\t\tawait updateJob({\n\t\t\t\t\t\tid: jobId,\n\t\t\t\t\t\tjob: formValues\n\t\t\t\t\t}).unwrap();\n\t\t\t\t\ttoast.success('Job updated');\n\t\t\t\t\tnavigate(paths.administration.jobs.edit(jobId));\n\t\t\t\t} else {\n\t\t\t\t\t// Map form values to create request format\n\t\t\t\t\tconst createRequest = {\n\t\t\t\t\t\tname: formValues.name,\n\t\t\t\t\t\ttype: formValues.type,\n\t\t\t\t\t\tdescription: formValues.description,\n\t\t\t\t\t\tparameters: formValues.parameters,\n\t\t\t\t\t\temailSuccess: formValues.successEmails,\n\t\t\t\t\t\temailFail: formValues.errorEmails,\n\t\t\t\t\t\tdisabled: formValues.disabled,\n\t\t\t\t\t};\n\t\t\t\t\tconst {id} = await createJob(createRequest).unwrap();\n\t\t\t\t\ttoast.success('Job created');\n\t\t\t\t\tnavigate(paths.administration.jobs.edit(id, true));\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tlogger.error(error);\n\t\t\t\ttoast.error('Something went wrong!');\n\t\t\t}\n\t\t},\n\t\t[navigate, isEditMode, jobId, updateJob, createJob]\n\t);\n\n\treturn (\n\t\t<form onSubmit={handleSubmit(onSubmit)}>\n\t\t\t<Card>\n\t\t\t\t<CardActions sx={{ justifyContent: 'space-between', alignItems: 'center' }}>\n\t\t\t\t\t<Typography variant=\"h4\">{isEditMode ? 'Edit job' : 'Create new job'}</Typography>\n\t\t\t\t\t<Box>\n\t\t\t\t\t\t<Button color=\"secondary\" component={RouterLink} href={paths.administration.jobs.index()}>\n\t\t\t\t\t\t\tCancel\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t<Button type=\"submit\" variant=\"contained\">\n\t\t\t\t\t\t\t{isEditMode ? 'Update' : 'Create'}\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Box>\n\t\t\t\t</CardActions>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<Stack divider={<Divider />} spacing={4}>\n\t\t\t\t\t\t<Stack spacing={3}>\n\t\t\t\t\t\t\t<Typography variant=\"h5\">Detail</Typography>\n\t\t\t\t\t\t\t<Grid container spacing={3} \t\t\t\t\t\n                                direction=\"row\"\n                                sx={{\n                                    justifyContent: 'center',\n                                    alignItems: 'center',\n                                }}\n                            >\n\t\t\t\t\t\t\t\t<Grid\n\t\t\t\t\t\t\t\t\tsize={{\n\t\t\t\t\t\t\t\t\t\tmd: 6,\n\t\t\t\t\t\t\t\t\t\txs: 12,\n\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<Controller\n\t\t\t\t\t\t\t\t\t\tcontrol={control}\n\t\t\t\t\t\t\t\t\t\tname=\"name\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n                                            <Tooltip disableFocusListener title={<Link href=\"https://aristotelos.cz\" target=\"_blank\">Documentation:</Link>} arrow>\n                                                <FormControl error={Boolean(errors.name)} fullWidth>\n                                                    <InputLabel required>Job Name</InputLabel>\n                                                    <OutlinedInput required {...field} />\n                                                    {errors.name ? <FormHelperText>{errors.name.message}</FormHelperText> : null}\n                                                </FormControl>\n                                            </Tooltip>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</Grid>\n                                <Grid\n\t\t\t\t\t\t\t\t\tsize={{\n\t\t\t\t\t\t\t\t\t\tmd: 6,\n\t\t\t\t\t\t\t\t\t\txs: 12,\n\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<Controller\n\t\t\t\t\t\t\t\t\t\tcontrol={control}\n\t\t\t\t\t\t\t\t\t\tname=\"type\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<Autocomplete\n\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\tgetOptionLabel={(option) => option.label}\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(_, value) => {\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (value) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield.onChange(value.value);\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t\t\t\toptions={JobTypeOptions}\n\t\t\t\t\t\t\t\t\t\t\t\tpopupIcon={<CaretDownIcon fontSize=\"var(--icon-fontSize-sm)\" />}\n\t\t\t\t\t\t\t\t\t\t\t\trenderInput={(params) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl error={Boolean(errors.type)} fullWidth>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<InputLabel>Job Type</InputLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<OutlinedInput\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{...params.InputProps}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tinputProps={params.inputProps}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{errors.type ? (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<FormHelperText>{errors.type.message}</FormHelperText>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t) : null}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t\t\trenderOption={(props, option) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Option {...props} key={option.value} value={option.value}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{option.label}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Option>\n\t\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={JobTypeOptions.find((option) => option.value === field.value)}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</Grid>\n                                <Grid\n\t\t\t\t\t\t\t\t\tsize={{\n\t\t\t\t\t\t\t\t\t\txs: 12,\n\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<Controller\n\t\t\t\t\t\t\t\t\t\tcontrol={control}\n\t\t\t\t\t\t\t\t\t\tname=\"description\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n                                            <Tooltip disableFocusListener title={<Link href=\"https://aristotelos.cz\" target=\"_blank\">Documentation:</Link>} arrow>\n                                                <FormControl error={Boolean(errors.description)} fullWidth>\n                                                    <InputLabel>Description</InputLabel>\n                                                    <OutlinedInput multiline {...field} />\n                                                    {errors.description ? <FormHelperText>{errors.description.message}</FormHelperText> : null}\n                                                </FormControl>\n                                            </Tooltip>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</Grid>\n                                <Grid\n\t\t\t\t\t\t\t\t\tsize={{\n\t\t\t\t\t\t\t\t\t\txs: 12,\n\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<Controller\n\t\t\t\t\t\t\t\t\t\tcontrol={control}\n\t\t\t\t\t\t\t\t\t\tname=\"parameters\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n                                            <Tooltip disableFocusListener title={<Link href=\"https://aristotelos.cz\" target=\"_blank\">Documentation:</Link>} arrow>\n                                                <FormControl error={Boolean(errors.name)} fullWidth>\n                                                    <InputLabel required>Parameters</InputLabel>\n                                                    <OutlinedInput required multiline {...field} />\n                                                    {errors.parameters ? <FormHelperText>{errors.parameters.message}</FormHelperText> : null}\n                                                </FormControl>\n                                            </Tooltip>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</Grid>\n\t\t\t\t\t\t\t\t<Grid\n\t\t\t\t\t\t\t\t\tsize={{\n\t\t\t\t\t\t\t\t\t\txs: 12,\n\t\t\t\t\t\t\t\t\t}}>\n\t\t\t\t\t\t\t\t\t<Divider />\n\t\t\t\t\t\t\t\t\t\t<Stack direction={'column'}>\n\t\t\t\t\t\t\t\t\t\t\t<Typography> Emails </Typography>\n\t\t\t\t\t\t\t\t\t\t\t<JobsEmailsList\n\t\t\t\t\t\t\t\t\t\t\t\terrorEmails={errorEmailsString.split(';').map((e: string) => e.trim()).filter((e: string) => e)}\n\t\t\t\t\t\t\t\t\t\t\t\tsuccessEmails={successEmailsString.split(';').map((e: string) => e.trim()).filter((e: string) => e)}\n\t\t\t\t\t\t\t\t\t\t\t\tonEmailsChange={handleEmailsChange}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</Stack>\n\t\t\t\t\t\t\t\t\t<Divider />\n\t\t\t\t\t\t\t\t</Grid>\n\t\t\t\t\t\t\t\t<Controller\n\t\t\t\t\t\t\t\t\tcontrol={control}\n\t\t\t\t\t\t\t\t\tname=\"successEmails\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<input {...field} type=\"hidden\" />\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<Controller\n\t\t\t\t\t\t\t\t\tcontrol={control}\n\t\t\t\t\t\t\t\t\tname=\"errorEmails\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<input {...field} type=\"hidden\" />\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</Grid>\n\t\t\t\t\t\t</Stack>\n\t\t\t\t\t</Stack>\n\t\t\t\t</CardContent>\n\t\t\t\t<CardActions sx={{ justifyContent: 'space-between', alignItems: 'center' }}>\n\t\t\t\t\t<Controller\n\t\t\t\t\t\tcontrol={control}\n\t\t\t\t\t\tname=\"disabled\"\n\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t<FormControlLabel\n\t\t\t\t\t\t\t\tcontrol={<Switch checked={field.value} onChange={field.onChange} />}\n\t\t\t\t\t\t\t\tlabel=\"Disabled\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t)}\n\t\t\t\t\t/>\n\t\t\t\t\t<Box>\n\t\t\t\t\t\t<Button color=\"secondary\" component={RouterLink} href={paths.administration.jobs.index()}>\n\t\t\t\t\t\t\tCancel\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t<Button type=\"submit\" variant=\"contained\">\n\t\t\t\t\t\t\t{isEditMode ? 'Update' : 'Create'}\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Box>\n\t\t\t\t</CardActions>\n\t\t\t</Card>\n\t\t</form>\n\t);\n}\n", "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { DataGridPremium, GridColDef, GridRenderCellParams, useGridApiRef } from \"@mui/x-data-grid-premium\";\r\nimport { AtApiServiceEndpointsAdministrationModelsJobTriggerDto, useDeleteJobTriggerMutation, useGetJobTriggersQuery } from \"@/store/api/administration\";\r\nimport { <PERSON>ton, Card, CardActions, CardContent, IconButton, Stack, Typography } from \"@mui/material\";\r\nimport { PencilSimpleIcon, TrashIcon } from \"@phosphor-icons/react\";\r\nimport { paths } from \"@/paths\";\r\nimport { RouterLink } from \"@/components/core/link\";\r\nimport { toast } from \"@/components/core/toaster\";\r\n\r\ninterface jobTriggersListProps {\r\n    jobId: number;\r\n};\r\n\r\nexport function JobTriggersList({jobId}: jobTriggersListProps): React.JSX.Element {\r\n    const apiRef = useGridApiRef();\r\n    const [allTriggers, setAllTriggers] = React.useState<AtApiServiceEndpointsAdministrationModelsJobTriggerDto[]>([]);\r\n    const [deleteTrigger, deleteOptions] = useDeleteJobTriggerMutation();\r\n\r\n    const {data} = useGetJobTriggersQuery({jobId: `${jobId}`});\r\n\r\n    React.useEffect(() => {\r\n        if (data && data.value != undefined){\r\n            setAllTriggers(data.value);\r\n        }\r\n    }, [jobId, data, deleteOptions.isSuccess]);\r\n\r\n    React.useEffect(() => {\r\n            if (deleteOptions.isError){\r\n                toast.error(\"Something went wrong!\");\r\n            }\r\n        },\r\n        [deleteOptions.isError]\r\n    )\r\n\r\n    const columns: GridColDef[] = [\r\n        {\r\n\t\t\tfield: 'id',\r\n\t\t\theaderName: 'Id',\r\n            type: 'number',\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n            width: 50\r\n\t\t},\r\n        {\r\n\t\t\tfield: 'name',\r\n\t\t\theaderName: 'Name',\r\n\t\t\ttype: 'string',\r\n            minWidth: 125\r\n\t\t},\r\n\t\t{\r\n\t\t\tfield: 'description',\r\n\t\t\theaderName: 'Description',\r\n\t\t\tminWidth: 250,\r\n\t\t\ttype: 'string',\r\n\t\t},\r\n        {\r\n            field: 'parameters',\r\n            headerName: 'Parameters',\r\n            minWidth: 200,\r\n            type: 'string',\r\n            align: 'center',\r\n            headerAlign: 'center'\r\n        },\r\n        {\r\n\t\t\tfield: 'type',\r\n\t\t\theaderName: 'Type',\r\n\t\t\tminWidth: 150,\r\n\t\t},\r\n        {\r\n\t\t\tfield: 'disabled',\r\n\t\t\theaderName: 'Disabled',\r\n\t\t\twidth: 100,\r\n\t\t\ttype: 'boolean',\r\n\t\t},\r\n        {\r\n            field: 'actions',\r\n            headerName: '',\r\n            width: 100,\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n            sortable: false,\r\n            disableColumnMenu: true,\r\n            renderCell: (params: GridRenderCellParams) => {\r\n                return (\r\n                    <Stack direction=\"row\" spacing={0.5} alignItems=\"center\">\r\n                        <IconButton\r\n                            component={RouterLink} \r\n                            href={paths.administration.jobs.triggers.editTrigger({jobId, triggerId: params.row.id})}\r\n                            size=\"small\"\r\n                            sx={{ padding: '4px' }}\r\n                        >\r\n                            <PencilSimpleIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                        <IconButton\r\n                            onClick={() => {\r\n                                deleteTrigger({entityId1: `${jobId}`, entityId2: `${params.row.id}`});\r\n                            }}\r\n                            size=\"small\"\r\n                            sx={{ padding: '4px' }}\r\n                        >\r\n                            <TrashIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                    </Stack>\r\n                );\r\n            },\r\n        }\r\n    ];\r\n\r\n\treturn (\r\n        <Card>\r\n            <CardContent>\r\n                <Typography> \r\n                    Triggers \r\n                </Typography>\r\n                <DataGridPremium\r\n                    apiRef={apiRef}\r\n                    columns={columns}\r\n                    rows={allTriggers}\r\n                    pagination\r\n                    pageSizeOptions={[5, 10, 25]}\r\n                    initialState={{\r\n                        pagination: { paginationModel: { pageSize: 10, page: 0 } },\r\n                    }}\r\n                    disableRowSelectionOnClick\r\n                    rowHeight={52}\r\n                    sx={{\r\n                        mt: 2,\r\n                        border: '1px solid #e0e0e0',\r\n                        '& .MuiDataGrid-columnHeaders': {\r\n                            backgroundColor: '#f5f5f5',\r\n                            borderBottom: '1px solid #e0e0e0',\r\n                            fontSize: '0.875rem',\r\n                            fontWeight: 500,\r\n                        },\r\n                        '& .MuiDataGrid-columnHeader': {\r\n                            padding: '8px 12px',\r\n                        },\r\n                        '& .MuiDataGrid-cell': {\r\n                            padding: '8px 12px',\r\n                            fontSize: '0.875rem',\r\n                            borderBottom: '1px solid #f0f0f0',\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                        },\r\n                        '& .MuiDataGrid-cell:focus': {\r\n                            outline: 'none',\r\n                        },\r\n                        '& .MuiDataGrid-cell:focus-within': {\r\n                            outline: 'none',\r\n                        },\r\n                        '& .MuiDataGrid-row': {\r\n                            '&:hover': {\r\n                                backgroundColor: '#fafafa',\r\n                            },\r\n                        },\r\n                        '& .MuiDataGrid-row:last-child .MuiDataGrid-cell': {\r\n                            borderBottom: 'none',\r\n                        },\r\n                        '& .MuiDataGrid-footerContainer': {\r\n                            borderTop: '1px solid #e0e0e0',\r\n                            backgroundColor: '#fafafa',\r\n                        },\r\n                        '& .MuiDataGrid-columnSeparator': {\r\n                            display: 'none',\r\n                        },\r\n                    }}\r\n                />\r\n            </CardContent>\r\n            <CardActions>\r\n                <Button \r\n                    style={{ marginLeft: 'auto' }}\r\n                    component={RouterLink} \r\n                    href={paths.administration.jobs.triggers.createTrigger(jobId)}\r\n                >\r\n                    Create new trigger\r\n                </Button>\r\n            </CardActions>\r\n        </Card>\r\n\t);\r\n}\r\n", "import * as React from \"react\";\r\nimport Box from \"@mui/material/Box\";\r\nimport Link from \"@mui/material/Link\";\r\nimport Stack from \"@mui/material/Stack\";\r\nimport { ArrowLeftIcon } from \"@phosphor-icons/react/dist/ssr/ArrowLeft\";\r\nimport { Helmet } from \"react-helmet-async\";\r\n\r\nimport type { Metadata } from \"@/types/metadata\";\r\nimport { appConfig } from \"@/config/app\";\r\nimport { paths } from \"@/paths\";\r\nimport { RouterLink } from \"@/components/core/link\";\r\n\r\nimport { JobEditForm } from \"@/pages/administration/jobs/components/job-edit-form\";\r\nimport { Grid } from \"@mui/material\";\r\nimport { JobTriggersList } from \"./components/job-triggers-list\";\r\n\r\ninterface PageProps {\r\n\tjobId?: number;\r\n\tscrollToTriggers?: boolean;\r\n}\r\n\r\nexport function Page({ jobId, scrollToTriggers }: PageProps): React.JSX.Element {\r\n\tconst metadata = {\r\n\t\ttitle: `${jobId ? 'Edit' : 'Create'} | Jobs | Dashboard | ${appConfig.name}`\r\n\t} satisfies Metadata;\r\n\r\n\tReact.useEffect(\r\n\t\t() => {\r\n\t\t\tif (scrollToTriggers){\r\n\t\t\t\tconst element = document.getElementById(\"triggers\");\r\n\t\t\t\tif (element){\r\n\t\t\t\t\telement.scrollIntoView({behavior: 'auto'});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t[]\r\n\t)\r\n\t\r\n\treturn (\r\n\t\t<React.Fragment>\r\n\t\t\t<Helmet>\r\n\t\t\t\t<title>{metadata.title}</title>\r\n\t\t\t</Helmet>\r\n\t\t\t<Box\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tmaxWidth: \"var(--Content-maxWidth)\",\r\n\t\t\t\t\tm: \"var(--Content-margin)\",\r\n\t\t\t\t\tp: \"var(--Content-padding)\",\r\n\t\t\t\t\twidth: \"var(--Content-width)\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Stack spacing={4}>\r\n\t\t\t\t\t<Stack spacing={3}>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<Link\r\n\t\t\t\t\t\t\t\tcolor=\"text.primary\"\r\n\t\t\t\t\t\t\t\tcomponent={RouterLink}\r\n\t\t\t\t\t\t\t\thref={paths.administration.jobs.index()}\r\n\t\t\t\t\t\t\t\tsx={{ alignItems: \"center\", display: \"inline-flex\", gap: 1 }}\r\n\t\t\t\t\t\t\t\tvariant=\"subtitle2\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<ArrowLeftIcon fontSize=\"var(--icon-fontSize-md)\" />\r\n\t\t\t\t\t\t\t\tBrowse Jobs\r\n\t\t\t\t\t\t\t</Link>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Stack>\r\n                    <JobEditForm jobId={jobId} />\r\n\t\t\t\t\t<div id=\"triggers\">\r\n\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\tsize={{\r\n\t\t\t\t\t\t\t\txs: 12,\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t<Stack direction={\"column\"}>\r\n\t\t\t\t\t\t\t\t{jobId && <JobTriggersList jobId={jobId} />}\r\n\t\t\t\t\t\t\t</Stack>\r\n\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</Stack>\r\n\t\t\t</Box>\r\n\t\t</React.Fragment>\r\n\t);\r\n}\r\n"], "names": ["a", "e.createElement", "e.Fragment", "o", "e.forwardRef", "r", "t", "c", "i", "m", "e", "a.create<PERSON>", "a<PERSON>", "r.forward<PERSON><PERSON>", "r.<PERSON>", "s", "o.forward<PERSON>ef", "o.createElement", "Option", "children", "props", "jsx", "MenuItem", "jobsApi", "baseApi", "builder", "params", "searchParams", "id", "result", "error", "job", "jobId", "triggerId", "trigger", "useGetJobQuery", "useCreateJobMutation", "useUpdateJobMutation", "JobsEmailsList", "successEmails", "errorEmails", "onEmailsChange", "allEmails", "setEmails", "useState", "editingEmail", "setEditingEmail", "newEmailError", "setNewEmailError", "editEmail<PERSON><PERSON>r", "setEditEmailError", "newEmailRef", "useRef", "editInputRef", "notifyEmailsChange", "useCallback", "emails", "email", "failureEmails", "useEffect", "emailMap", "existingEmail", "trimmedEmail", "deduplicatedEmails", "addEmail", "emailValue", "_a", "validationError", "validateEmail", "newEmails", "handleSuccessChange", "emailAddress", "checked", "prevEmails", "handleFailureChange", "handleEditStart", "handleEditConfirm", "newEmailValue", "handleEditCancel", "handleDeleteEmail", "isValidEmail", "isEditing", "handleNewEmailChange", "columns", "<PERSON><PERSON><PERSON>", "Box", "OutlinedInput", "Checkbox", "IconButton", "CheckIcon", "<PERSON><PERSON>", "PencilSimpleIcon", "TrashIcon", "jsxs", "FormControl", "InputLabel", "<PERSON><PERSON>", "DataGridPremium", "row", "JobTypeOptions", "schema", "zod.object", "zod.string", "zod.number", "zod.boolean", "defaultValues", "JobEditForm", "navigate", "useNavigate", "isEditMode", "jobData", "jobError", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "createJob", "isCreating", "updateJob", "isUpdating", "control", "handleSubmit", "errors", "setValue", "watch", "reset", "useForm", "zodResolver", "React.useEffect", "formData", "logger", "toast", "handleEmailsChange", "React.useCallback", "successEmailsString", "errorEmailsString", "onSubmit", "formValues", "paths", "createRequest", "Card", "CardActions", "Typography", "RouterLink", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "Grid", "Controller", "field", "Link", "FormHelperText", "Autocomplete", "option", "_", "value", "CaretDownIcon", "FormControlLabel", "Switch", "JobTriggersList", "apiRef", "useGridApiRef", "allTriggers", "setAllTriggers", "React.useState", "deleteTrigger", "deleteOptions", "useDeleteJobTriggerMutation", "data", "useGetJobTriggersQuery", "Page", "scrollToTriggers", "metadata", "appConfig", "element", "React.Fragment", "<PERSON><PERSON><PERSON>", "ArrowLeftIcon"], "mappings": "+yBACA,MAAMA,GAAoB,IAAI,IAAI,CAChC,CACE,OACgBC,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,0GAA2G,CAAC,CAC/M,EACE,CACE,UACgBA,gBAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAChE,OACA,CACE,EAAG,kGACH,QAAS,KACjB,CACA,EAAuBA,EAAAA,cAAgB,OAAQ,CAAE,EAAG,mHAAmH,CAAE,CAAC,CAC1K,EACE,CACE,OACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,mNAAoN,CAAC,CACxT,EACE,CACE,QACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,iHAAkH,CAAC,CACtN,EACE,CACE,UACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,sHAAuH,CAAC,CAC3N,EACE,CACE,OACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,iHAAkH,CAAC,CACtN,CACA,CAAC,EC7BKE,GAAIC,EAAAA,WAAa,CAACC,EAAGC,IAAsBL,EAAAA,cAAgBM,EAAG,CAAE,IAAKD,EAAG,GAAGD,EAAG,QAASL,EAAC,CAAE,CAAC,EACjGG,GAAE,YAAc,YCDhB,MAAMA,GAAIC,EAAAA,WAAa,CAACI,EAAGC,IAAsBR,EAAAA,cAAgBI,EAAG,CAAE,IAAKI,EAAG,GAAGD,EAAG,QAASF,EAAC,CAAE,CAAC,EACjGH,GAAE,YAAc,mBCHhB,MAAMO,GAAoB,IAAI,IAAI,CAChC,CACE,OACgBC,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,sTAAuT,CAAC,CAC3Z,EACE,CACE,UACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,iDAAkD,QAAS,KAAK,CAAE,EAAmBA,EAAAA,cAAgB,OAAQ,CAAE,EAAG,sSAAuS,CAAC,CAC7f,EACE,CACE,OACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,+QAAgR,CAAC,CACpX,EACE,CACE,QACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,+UAAgV,CAAC,CACpb,EACE,CACE,UACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,sSAAuS,CAAC,CAC3Y,EACE,CACE,OACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,yUAA0U,CAAC,CAC9a,CACA,CAAC,ECvBKR,GAAIU,EAAAA,WAAa,CAACb,EAAGU,IAAsBI,EAAAA,cAAgBC,EAAG,CAAE,IAAKL,EAAG,GAAGV,EAAG,QAASM,EAAC,CAAE,CAAC,EACjGH,GAAE,YAAc,YCDhB,MAAME,GAAIW,EAAAA,WAAa,CAAChB,EAAGU,IAAsBO,EAAAA,cAAgBF,GAAG,CAAE,IAAKL,EAAG,GAAGV,EAAG,QAASM,EAAC,CAAE,CAAC,EACjGD,GAAE,YAAc,YCKT,SAASa,GAAO,CAAE,SAAAC,EAAU,GAAGC,GAAyC,CAC9E,OAAOC,EAAAA,IAACC,GAAA,CAAU,GAAGF,EAAQ,SAAAD,CAAA,CAAS,CACvC,CCyDO,MAAMI,GAAUC,GAAQ,gBAAgB,CAC7C,UAAYC,IAAa,CAEvB,QAASA,EAAQ,MAAqC,CACpD,MAAQC,GAAW,CACjB,MAAMC,EAAe,IAAI,gBAGzB,OAAID,EAAO,OAAS,QAClBC,EAAa,OAAO,OAAQD,EAAO,KAAK,UAAU,EAEhDA,EAAO,QAAU,QACnBC,EAAa,OAAO,QAASD,EAAO,MAAM,UAAU,EAElDA,EAAO,SAAW,QACpBC,EAAa,OAAO,SAAUD,EAAO,OAAO,UAAU,EAEpDA,EAAO,SACTC,EAAa,OAAO,UAAWD,EAAO,OAAO,EAE3CA,EAAO,UACTC,EAAa,OAAO,WAAYD,EAAO,QAAQ,EAE7CA,EAAO,SACTC,EAAa,OAAO,UAAWD,EAAO,OAAO,EAGxC,CACL,IAAK,gBAAgBC,EAAa,SAAA,CAAU,GAC5C,OAAQ,KAAA,CAEZ,EACA,aAAc,CAAC,KAAK,CAAA,CACrB,EAGD,OAAQF,EAAQ,MAAiC,CAC/C,MAAQG,IAAQ,CACd,IAAK,gBAAgBA,CAAE,GACvB,OAAQ,KAAA,GAEV,aAAc,CAACC,EAAQC,EAAOF,IAAO,CAAC,CAAE,KAAM,MAAO,GAAAA,CAAA,CAAI,CAAA,CAC1D,EAGD,UAAWH,EAAQ,SAAgC,CACjD,MAAQM,IAAS,CACf,IAAK,eACL,OAAQ,OACR,KAAMA,EACN,QAAS,CACP,eAAgB,kBAAA,CAClB,GAEF,gBAAiB,CAAC,KAAK,CAAA,CACxB,EAGD,UAAWN,EAAQ,SAAqD,CACtE,MAAO,CAAC,CAAE,GAAAG,EAAI,IAAAG,MAAW,CACvB,IAAK,gBAAgBH,CAAE,GACvB,OAAQ,MACR,KAAM,CAAE,MAAOG,CAAA,EACf,QAAS,CACP,eAAgB,kBAAA,CAClB,GAEF,gBAAiB,CAACF,EAAQC,EAAO,CAAE,GAAAF,CAAA,IAAS,CAAC,CAAE,KAAM,MAAO,GAAAA,CAAA,EAAM,KAAK,CAAA,CACxE,EAED,UAAWH,EAAQ,SAAsB,CACvC,MAAQG,IAAQ,CACd,IAAK,gBAAgBA,CAAE,GACvB,OAAQ,QAAA,GAEV,gBAAiB,CAAC,KAAK,CAAA,CACxB,EAED,eAAgBH,EAAQ,MAAwE,CAC9F,MAAO,CAAC,CAAC,MAAAO,EAAO,OAAAN,KAAY,CAC1B,MAAMC,EAAe,IAAI,gBAGzB,OAAID,EAAO,OAAS,QAClBC,EAAa,OAAO,OAAQD,EAAO,KAAK,UAAU,EAEhDA,EAAO,QAAU,QACnBC,EAAa,OAAO,QAASD,EAAO,MAAM,UAAU,EAElDA,EAAO,SAAW,QACpBC,EAAa,OAAO,SAAUD,EAAO,OAAO,UAAU,EAEpDA,EAAO,SACTC,EAAa,OAAO,UAAWD,EAAO,OAAO,EAE3CA,EAAO,UACTC,EAAa,OAAO,WAAYD,EAAO,QAAQ,EAE7CA,EAAO,SACTC,EAAa,OAAO,UAAWD,EAAO,OAAO,EAGxC,CACL,IAAK,gBAAgBM,CAAK,aAAaL,EAAa,UAAU,GAC9D,OAAQ,KAAA,CAEZ,CAAA,CAED,EAED,cAAeF,EAAQ,MAAsD,CAC3E,MAAO,CAAC,CAAC,MAAAO,EAAO,UAAAC,MAAgB,CAC9B,IAAK,gBAAgBD,CAAK,aAAaC,CAAS,GAChD,OAAQ,KAAA,EACV,CAED,EAED,iBAAkBR,EAAQ,SAAoD,CAC5E,MAAO,CAAC,CAAC,MAAAO,EAAO,QAAAE,MAAc,CAC5B,IAAK,gBAAgBF,CAAK,YAC1B,OAAQ,OACR,KAAME,EACN,QAAS,CACP,eAAgB,kBAAA,CAClB,GAEF,gBAAiB,CAAC,KAAK,CAAA,CACxB,EAED,iBAAkBT,EAAQ,SAAuE,CAC/F,MAAO,CAAC,CAAC,MAAAO,EAAO,UAAAC,EAAW,QAAAC,MAAc,CACvC,IAAK,gBAAgBF,CAAK,aAAaC,CAAS,GAChD,OAAQ,MACR,KAAM,CAAE,MAAOC,CAAA,EACf,QAAS,CACP,eAAgB,kBAAA,CAClB,GAEF,gBAAiB,CAAC,KAAK,CAAA,CACxB,EAED,iBAAkBT,EAAQ,SAAkD,CAC1E,MAAO,CAAC,CAAC,MAAAO,EAAO,UAAAC,MAAgB,CAC9B,IAAK,gBAAgBD,CAAK,aAAaC,CAAS,GAChD,OAAQ,QAAA,GAEV,gBAAiB,CAAC,KAAK,CAAA,CACxB,CAAA,EAEL,CAAC,EAGY,CAEX,eAAAE,GACA,qBAAAC,GACA,qBAAAC,EAOF,EAAId,GCjNG,SAASe,GAAe,CAAC,cAAAC,EAAe,YAAAC,EAAa,eAAAC,GAAyD,CACjH,KAAM,CAACC,EAAWC,CAAS,EAAIC,EAAAA,SAAsB,CAAA,CAAE,EACjD,CAACC,EAAcC,CAAe,EAAIF,EAAAA,SAAwB,IAAI,EAC9D,CAACG,EAAeC,CAAgB,EAAIJ,EAAAA,SAAiB,EAAE,EACvD,CAACK,EAAgBC,CAAiB,EAAIN,EAAAA,SAAiB,EAAE,EAEzDO,EAAcC,EAAAA,OAAyB,IAAI,EAC3CC,EAAeD,EAAAA,OAAyB,IAAI,EAE5CE,EAAqBC,cAAaC,GAAwB,CAC5D,GAAIf,EAAgB,CAChB,MAAMF,EAAgBiB,EACjB,OAAOC,GAASA,EAAM,SAAS,EAC/B,IAAIA,GAASA,EAAM,KAAK,EACvBC,EAAgBF,EACjB,OAAOC,GAASA,EAAM,SAAS,EAC/B,IAAIA,GAASA,EAAM,KAAK,EAC7BhB,EAAeF,EAAemB,CAAa,CAC/C,CACJ,EAAG,CAACjB,CAAc,CAAC,EAEnBkB,EAAAA,UAAU,IAAM,CACZ,MAAMC,MAAe,IAErB,UAAWC,KAAiBnB,EACxBkB,EAAS,IAAIC,EAAc,MAAO,CAC9B,MAAOA,EAAc,MACrB,UAAW,GACX,UAAW,EAAA,CACd,EAGL,UAAWJ,KAASlB,EAAe,CAC3B,MAAMuB,EAAeL,EAAM,KAAA,EACrBI,EAAgBD,EAAS,IAAIE,CAAY,EAC/C,GAAGD,EAAe,CACdA,EAAc,UAAY,GAC1B,QACJ,CAEAD,EAAS,IAAIE,EAAc,CACvB,MAAOA,EACP,UAAW,GACX,UAAW,EAAA,CACd,CACT,CAEA,UAAWL,KAASjB,EAAa,CACzB,MAAMsB,EAAeL,EAAM,KAAA,EACrBI,EAAgBD,EAAS,IAAIE,CAAY,EAC/C,GAAGD,EAAe,CACdA,EAAc,UAAY,GAC1B,QACJ,CAEAD,EAAS,IAAIE,EAAc,CACvB,MAAOA,EACP,UAAW,GACX,UAAW,EAAA,CACd,CACT,CAEA,MAAMC,EAAqB,MAAM,KAAKH,EAAS,QAAQ,EACvDjB,EAAUoB,CAAkB,CAChC,EAAG,CAACxB,EAAeC,CAAW,CAAC,EAE/B,MAAMwB,EAAW,IAAM,OACnB,MAAMC,IAAaC,EAAAf,EAAY,UAAZ,YAAAe,EAAqB,QAAS,GAC3CC,EAAkBC,EAAcH,CAAU,EAEhD,GAAIE,EAAiB,CACjBnB,EAAiBmB,CAAe,EAChC,MACJ,CAEAnB,EAAiB,EAAE,EAEnB,MAAMc,EAAeG,EAAW,KAAA,EAC1BI,EAAY,CAAC,GAAG3B,EAAW,CAC7B,MAAOoB,EACP,UAAW,GACX,UAAW,EAAA,CACd,EACDnB,EAAU0B,CAAS,EACnBf,EAAmBe,CAAS,EAExBlB,EAAY,UACZA,EAAY,QAAQ,MAAQ,GAEpC,EAEMmB,EAAsB,CAACC,EAAsBC,IAAqB,CACpE7B,EAAU8B,GAAc,CACpB,MAAMJ,EAAYI,EAAW,IAAIhB,GAC7BA,EAAM,QAAUc,EACV,CAAE,GAAGd,EAAO,UAAWe,GACvBf,CAAA,EAEV,OAAAH,EAAmBe,CAAS,EACrBA,CACX,CAAC,CACL,EAEMK,EAAsB,CAACH,EAAsBC,IAAqB,CACpE7B,EAAU8B,GAAc,CACpB,MAAMJ,EAAYI,EAAW,IAAIhB,GAC7BA,EAAM,QAAUc,EACV,CAAE,GAAGd,EAAO,UAAWe,GACvBf,CAAA,EAEV,OAAAH,EAAmBe,CAAS,EACrBA,CACX,CAAC,CACL,EAEMM,EAAmBJ,GAAyB,CAC9CzB,EAAgByB,CAAY,EAC5BrB,EAAkB,EAAE,EACpB,WAAW,IAAM,CACTG,EAAa,UACbA,EAAa,QAAQ,MAAA,EACrB,sBAAsB,IAAM,CACpBA,EAAa,SACbA,EAAa,QAAQ,OAAA,CAE7B,CAAC,EAET,EAAG,EAAE,CACT,EAEMuB,EAAoB,IAAM,OAC5B,MAAMC,IAAgBX,EAAAb,EAAa,UAAb,YAAAa,EAAsB,QAAS,GAC/CC,EAAkBC,EAAcS,EAAe,EAAI,EAEzD,GAAIV,EAAiB,CACjBjB,EAAkBiB,CAAe,EACjC,MACJ,CAEAjB,EAAkB,EAAE,EAEhBL,GAAgBgC,EAAc,QAC9BlC,EAAU8B,GAAc,CACpB,MAAMJ,EAAYI,EAAW,IAAIhB,GAC7BA,EAAM,QAAUZ,EACV,CAAE,GAAGY,EAAO,MAAOoB,EAAc,KAAA,GACjCpB,CAAA,EAEV,OAAAH,EAAmBe,CAAS,EACrBA,CACX,CAAC,EAELvB,EAAgB,IAAI,CACxB,EAEMgC,EAAmB,IAAM,CAC3BhC,EAAgB,IAAI,EACpBI,EAAkB,EAAE,CACxB,EAEM6B,EAAqBR,GAAyB,CAChD5B,EAAU8B,GAAc,CACpB,MAAMJ,EAAYI,EAAW,OAAOhB,GAASA,EAAM,QAAUc,CAAY,EACzE,OAAAjB,EAAmBe,CAAS,EACrBA,CACX,CAAC,EAEGxB,IAAiB0B,GACjBzB,EAAgB,IAAI,CAE5B,EAEMkC,EAAgBvB,GACC,6BACD,KAAKA,CAAK,EAG1BW,EAAgB,CAACX,EAAewB,EAAqB,KAClDxB,EAAM,OAINuB,EAAavB,EAAM,KAAA,CAAM,EAIVf,EAAU,KAAKmB,GAC/BA,EAAc,QAAUJ,EAAM,SAC7B,CAACwB,GAAapB,EAAc,QAAUhB,EAAA,EAIhC,oCAGJ,GAZI,qCAJA,4BAmBTqC,GAAuB,IAAM,CAC3BnC,GACAC,EAAiB,EAAE,CAE3B,EAEMmC,GAAwB,CAC1B,CACI,MAAO,QACP,WAAY,QACZ,KAAM,EACN,SAAU,IACV,WAAazD,GACSmB,IAAiBnB,EAAO,IAAI,MAItCL,EAAAA,IAAC+D,EAAA,CACG,MAAOnC,GAAkB,GACzB,KAAM,CAAC,CAACA,EACR,UAAU,eACV,MAAK,GACL,UAAW,CACP,QAAS,CACL,UAAW,iCAAA,CACf,EAGJ,SAAA5B,EAAAA,IAACgE,EAAA,CAAI,UAAU,yCACX,SAAAhE,EAAAA,IAACiE,EAAA,CACG,SAAUjC,EACV,aAAcR,EACd,MAAO,CAAC,CAACI,EACT,SAAU,IAAM,CACRA,GACAC,EAAkB,EAAE,CAE5B,EACA,UAAYxC,GAAM,CACVA,EAAE,MAAQ,SACVA,EAAE,eAAA,EACFA,EAAE,gBAAA,EACFkE,EAAA,GACOlE,EAAE,MAAQ,UACjBA,EAAE,eAAA,EACFA,EAAE,gBAAA,EACFoE,EAAA,GACO,CAAC,YAAa,aAAc,UAAW,YAAa,OAAQ,KAAK,EAAE,SAASpE,EAAE,GAAG,GACxFA,EAAE,gBAAA,CAEV,EACA,KAAK,QACL,GAAI,CAAE,MAAO,MAAA,CAAO,CAAA,CACxB,CACJ,CAAA,CAAA,EAMRW,EAAAA,IAAC,OAAA,CACG,cAAe,IAAMsD,EAAgBjD,EAAO,IAAI,KAAK,EACrD,UAAU,+BAET,SAAAA,EAAO,KAAA,CAAA,CAGpB,EAEJ,CACI,MAAO,YACP,WAAY,2BACZ,MAAO,IACP,MAAO,SACP,YAAa,SACb,WAAaA,GACTL,EAAAA,IAACkE,EAAA,CACG,QAAS7D,EAAO,MAChB,SAAWhB,GAAM4D,EAAoB5C,EAAO,IAAI,MAAOhB,EAAE,OAAO,OAAO,EACvE,KAAK,QACL,GAAI,CAAE,QAAS,CAAA,CAAE,CAAA,CACrB,EAGR,CACI,MAAO,YACP,WAAY,UACZ,MAAO,IACP,MAAO,SACP,YAAa,SACb,WAAagB,GACTL,EAAAA,IAACkE,EAAA,CACG,QAAS7D,EAAO,MAChB,SAAWhB,GAAMgE,EAAoBhD,EAAO,IAAI,MAAOhB,EAAE,OAAO,OAAO,EACvE,KAAK,QACL,GAAI,CAAE,QAAS,CAAA,CAAE,CAAA,CACrB,EAGR,CACI,MAAO,UACP,WAAY,GACZ,MAAO,IACP,MAAO,SACP,YAAa,SACb,SAAU,GACV,kBAAmB,GACnB,WAAagB,GACSmB,IAAiBnB,EAAO,IAAI,MAItCL,EAAAA,IAACmE,EAAA,CACG,QAASZ,EACT,KAAK,QACL,GAAI,CAAE,QAAS,KAAA,EAEf,SAAAvD,EAAAA,IAACoE,GAAA,CAAU,SAAS,OAAA,CAAQ,CAAA,CAAA,SAMnCC,EAAA,CAAM,UAAU,MAAM,QAAS,GAAK,WAAW,SAC5C,SAAA,CAAArE,EAAAA,IAACmE,EAAA,CACG,QAAS,IAAMb,EAAgBjD,EAAO,IAAI,KAAK,EAC/C,KAAK,QACL,GAAI,CAAE,QAAS,KAAA,EAEf,SAAAL,EAAAA,IAACsE,GAAA,CAAiB,SAAS,OAAA,CAAQ,CAAA,CAAA,EAEvCtE,EAAAA,IAACmE,EAAA,CACG,QAAS,IAAMT,EAAkBrD,EAAO,IAAI,KAAK,EACjD,KAAK,QACL,GAAI,CAAE,QAAS,KAAA,EAEf,SAAAL,EAAAA,IAACuE,GAAA,CAAU,SAAS,OAAA,CAAQ,CAAA,CAAA,CAChC,EACJ,CAER,CACJ,EAIP,cACQF,EAAA,CACG,SAAA,CAAAG,OAACH,GAAM,UAAW,MAAO,QAAS,EAAG,WAAY,MAC7C,SAAA,CAAAG,OAACC,GAAY,UAAS,GAAC,MAAO,CAAC,CAAC/C,EAC5B,SAAA,CAAA1B,EAAAA,IAAC0E,GAAW,SAAA,WAAA,CAAS,EACrB1E,EAAAA,IAACiE,EAAA,CACG,SAAUnC,EACV,MAAO,CAAC,CAACJ,EACT,SAAUmC,GACV,UAAYxE,GAAM,CACVA,EAAE,MAAQ,UACVA,EAAE,eAAA,EACFA,EAAE,gBAAA,EACFsD,EAAA,EACA,WAAW,IAAM,CACZtD,EAAE,OAA4B,MAAA,CACnC,EAAG,CAAC,EAEZ,CAAA,CAAA,EAEHqC,GACG1B,EAAAA,IAACgE,EAAA,CAAI,UAAU,kCACV,SAAAtC,CAAA,CACL,CAAA,EAER,QACCiD,EAAA,CAAO,QAAQ,WAAW,QAAShC,EAAU,SAAA,KAAA,CAAG,CAAA,EACrD,EACA3C,EAAAA,IAAC4E,GAAA,CACG,KAAMvD,EACN,QAAAyC,GACA,SAAWe,GAAQA,EAAI,MACvB,WAAU,GACV,gBAAiB,CAAC,EAAG,GAAI,EAAE,EAC3B,aAAc,CACV,WAAY,CAAE,gBAAiB,CAAE,SAAU,GAAI,KAAM,EAAE,CAAE,EAE7D,2BAA0B,GAC1B,UAAW,GACX,UAAU,6BAAA,CAAA,CACd,EACJ,CAER,CCrXA,MAAMC,EAAiB,CACtB,CAAE,MAAO,uCAAwC,MAAO,CAAA,EACxD,CAAE,MAAO,kBAAmB,MAAO,CAAA,EACnC,CAAE,MAAO,oBAAqB,MAAO,CAAA,CACtC,EAEMC,GAASC,GAAW,CACzB,KAAMC,EAAI,EAAS,IAAI,EAAG,kBAAkB,EAAE,IAAI,GAAG,EACrD,KAAMC,GAAI,EAAS,IAAI,CAAC,EAAE,IAAIJ,EAAe,MAAM,EACnD,YAAaG,EAAI,EAAS,IAAI,GAAG,EAAE,SAAA,EACnC,WAAYA,EAAI,EAAS,IAAI,EAAG,yBAAyB,EACzD,cAAeA,EAAI,EAAS,IAAI,GAAG,EAAE,SAAA,EACrC,YAAaA,EAAI,EAAS,IAAI,GAAG,EAAE,SAAA,EACnC,SAAUE,GAAI,CACf,CAAC,EAIKC,GAAgB,CACrB,KAAM,GACH,KAAM,EACN,YAAY,GACZ,WAAW,GACd,cAAe,GACf,YAAa,GACV,SAAU,EACd,EAMO,SAASC,GAAY,CAAE,MAAA1E,GAA8C,CAC3E,MAAM2E,EAAWC,GAAA,EACXC,EAAa7E,IAAU,OAGvB,CAAE,KAAM8E,EAAS,MAAOC,EAAU,UAAWC,CAAA,EAAiB7E,GAAeH,EAAQ,CAC1F,KAAM,CAAC6E,GAAc,CAAC7E,CAAA,CACtB,EACK,CAACiF,EAAW,CAAE,UAAWC,CAAA,CAAY,EAAI9E,GAAA,EACzC,CAAC+E,EAAW,CAAE,UAAWC,CAAA,CAAY,EAAI/E,GAAA,EAEzC,CACL,QAAAgF,EACA,aAAAC,EACA,UAAW,CAAE,OAAAC,CAAA,EACb,SAAAC,EACA,MAAAC,EACA,MAAAC,CAAA,EACGC,GAAgB,CAAE,cAAAlB,GAAe,SAAUmB,GAAYxB,EAAM,EAAG,EAGpEyB,EAAAA,UAAgB,IAAM,CACrB,GAAIhB,IAAcC,GAAA,MAAAA,EAAS,QAAQ,CAClC,MAAMgB,EAAmB,CACxB,GAAGhB,EAAQ,MAAA,EAEZY,EAAMI,CAAQ,CACf,CACD,EAAG,CAAChB,EAASD,EAAYa,CAAK,CAAC,EAG/BG,EAAAA,UAAgB,IAAM,CACjBd,IACHgB,EAAO,MAAMhB,CAAQ,EACrBiB,EAAM,MAAM,yBAAyB,EAEvC,EAAG,CAACjB,CAAQ,CAAC,EAEb,MAAMkB,EAAqBC,EAAAA,YAAkB,CAAC3F,EAAyBmB,IAA4B,CAClG8D,EAAS,gBAAiBjF,EAAc,KAAK,GAAG,CAAC,EACjDiF,EAAS,cAAe9D,EAAc,KAAK,GAAG,CAAC,CAChD,EAAG,CAAC8D,CAAQ,CAAC,EAEPW,EAAsBV,EAAM,eAAe,GAAK,GAChDW,EAAoBX,EAAM,aAAa,GAAK,GAE5CY,EAAWH,EAAAA,YAChB,MAAOI,GAAsC,CAC5C,GAAI,CACH,GAAIzB,GAAc7E,EACjB,MAAMmF,EAAU,CACf,GAAInF,EACJ,IAAKsG,CAAA,CACL,EAAE,OAAA,EACHN,EAAM,QAAQ,aAAa,EAC3BrB,EAAS4B,EAAM,eAAe,KAAK,KAAKvG,CAAK,CAAC,MACxC,CAEN,MAAMwG,EAAgB,CACrB,KAAMF,EAAW,KACjB,KAAMA,EAAW,KACjB,YAAaA,EAAW,YACxB,WAAYA,EAAW,WACvB,aAAcA,EAAW,cACzB,UAAWA,EAAW,YACtB,SAAUA,EAAW,QAAA,EAEhB,CAAC,GAAA1G,CAAA,EAAM,MAAMqF,EAAUuB,CAAa,EAAE,OAAA,EAC5CR,EAAM,QAAQ,aAAa,EAC3BrB,EAAS4B,EAAM,eAAe,KAAK,KAAK3G,EAAI,EAAI,CAAC,CAClD,CACD,OAASE,EAAO,CACfiG,EAAO,MAAMjG,CAAK,EAClBkG,EAAM,MAAM,uBAAuB,CACpC,CACD,EACA,CAACrB,EAAUE,EAAY7E,EAAOmF,EAAWF,CAAS,CAAA,EAGnD,aACE,OAAA,CAAK,SAAUK,EAAae,CAAQ,EACpC,gBAACI,GAAA,CACA,SAAA,CAAA5C,OAAC6C,GAAY,GAAI,CAAE,eAAgB,gBAAiB,WAAY,UAC/D,SAAA,CAAArH,MAACsH,EAAA,CAAW,QAAQ,KAAM,SAAA9B,EAAa,WAAa,iBAAiB,SACpExB,EAAA,CACA,SAAA,CAAAhE,EAAAA,IAAC2E,EAAA,CAAO,MAAM,YAAY,UAAW4C,EAAY,KAAML,EAAM,eAAe,KAAK,MAAA,EAAS,SAAA,QAAA,CAE1F,EACAlH,EAAAA,IAAC2E,GAAO,KAAK,SAAS,QAAQ,YAC5B,SAAAa,EAAa,SAAW,QAAA,CAC1B,CAAA,CAAA,CACD,CAAA,EACD,EACAxF,MAACwH,GAAA,CACA,SAAAxH,EAAAA,IAACqE,EAAA,CAAM,QAASrE,EAAAA,IAACyH,EAAA,CAAA,CAAQ,EAAI,QAAS,EACrC,SAAAjD,EAAAA,KAACH,EAAA,CAAM,QAAS,EACf,SAAA,CAAArE,EAAAA,IAACsH,EAAA,CAAW,QAAQ,KAAK,SAAA,SAAM,EAC/B9C,EAAAA,KAACkD,EAAA,CAAK,UAAS,GAAC,QAAS,EACA,UAAU,MACV,GAAI,CACA,eAAgB,SAChB,WAAY,QAAA,EAGxC,SAAA,CAAA1H,EAAAA,IAAC0H,EAAA,CACA,KAAM,CACL,GAAI,EACJ,GAAI,EAAA,EAGL,SAAA1H,EAAAA,IAAC2H,EAAA,CACA,QAAA3B,EACA,KAAK,OACL,OAAQ,CAAC,CAAE,MAAA4B,CAAA,IACuB5H,EAAAA,IAAC+D,EAAA,CAAQ,qBAAoB,GAAC,MAAO/D,EAAAA,IAAC6H,EAAA,CAAK,KAAK,yBAAyB,OAAO,SAAS,SAAA,gBAAA,CAAc,EAAS,MAAK,GACjH,SAAArD,EAAAA,KAACC,EAAA,CAAY,MAAO,EAAQyB,EAAO,KAAO,UAAS,GAC/C,SAAA,CAAAlG,EAAAA,IAAC0E,EAAA,CAAW,SAAQ,GAAC,SAAA,WAAQ,EAC7B1E,EAAAA,IAACiE,EAAA,CAAc,SAAQ,GAAE,GAAG2D,CAAA,CAAO,EAClC1B,EAAO,KAAOlG,EAAAA,IAAC8H,GAAgB,SAAA5B,EAAO,KAAK,QAAQ,EAAoB,IAAA,CAAA,CAC5E,CAAA,CACJ,CAAA,CAAA,CAEnC,CAAA,EAEuBlG,EAAAA,IAAC0H,EAAA,CACxB,KAAM,CACL,GAAI,EACJ,GAAI,EAAA,EAGL,SAAA1H,EAAAA,IAAC2H,EAAA,CACA,QAAA3B,EACA,KAAK,OACL,OAAQ,CAAC,CAAE,MAAA4B,CAAA,IACV5H,EAAAA,IAAC+H,GAAA,CACC,GAAGH,EACJ,eAAiBI,GAAWA,EAAO,MACnC,SAAU,CAACC,EAAGC,IAAU,CACnBA,GACHN,EAAM,SAASM,EAAM,KAAK,CAE5B,EACA,QAASpD,EACT,UAAW9E,EAAAA,IAACmI,GAAA,CAAc,SAAS,yBAAA,CAA0B,EAC7D,YAAc9H,GACbmE,OAACC,EAAA,CAAY,MAAO,EAAQyB,EAAO,KAAO,UAAS,GAClD,SAAA,CAAAlG,EAAAA,IAAC0E,GAAW,SAAA,UAAA,CAAQ,EACpB1E,EAAAA,IAACiE,EAAA,CACC,GAAG5D,EAAO,WACX,WAAYA,EAAO,UAAA,CAAA,EAEnB6F,EAAO,KACPlG,EAAAA,IAAC8H,GAAgB,SAAA5B,EAAO,KAAK,QAAQ,EAClC,IAAA,EACL,EAED,aAAc,CAACnG,EAAOiI,oBACpBnI,GAAA,CAAQ,GAAGE,EAAO,IAAKiI,EAAO,MAAO,MAAOA,EAAO,KAAA,EAClDA,EAAO,KACT,EAED,MAAOlD,EAAe,KAAMkD,GAAWA,EAAO,QAAUJ,EAAM,KAAK,CAAA,CAAA,CACpE,CAAA,CAEF,CAAA,EAEuB5H,EAAAA,IAAC0H,EAAA,CACxB,KAAM,CACL,GAAI,EAAA,EAGL,SAAA1H,EAAAA,IAAC2H,EAAA,CACA,QAAA3B,EACA,KAAK,cACL,OAAQ,CAAC,CAAE,MAAA4B,CAAA,IACuB5H,EAAAA,IAAC+D,EAAA,CAAQ,qBAAoB,GAAC,MAAO/D,EAAAA,IAAC6H,EAAA,CAAK,KAAK,yBAAyB,OAAO,SAAS,SAAA,gBAAA,CAAc,EAAS,MAAK,GACjH,SAAArD,EAAAA,KAACC,EAAA,CAAY,MAAO,EAAQyB,EAAO,YAAc,UAAS,GACtD,SAAA,CAAAlG,EAAAA,IAAC0E,GAAW,SAAA,aAAA,CAAW,EACvB1E,EAAAA,IAACiE,EAAA,CAAc,UAAS,GAAE,GAAG2D,CAAA,CAAO,EACnC1B,EAAO,YAAclG,EAAAA,IAAC8H,GAAgB,SAAA5B,EAAO,YAAY,QAAQ,EAAoB,IAAA,CAAA,CAC1F,CAAA,CACJ,CAAA,CAAA,CAEnC,CAAA,EAEuBlG,EAAAA,IAAC0H,EAAA,CACxB,KAAM,CACL,GAAI,EAAA,EAGL,SAAA1H,EAAAA,IAAC2H,EAAA,CACA,QAAA3B,EACA,KAAK,aACL,OAAQ,CAAC,CAAE,MAAA4B,CAAA,IACuB5H,EAAAA,IAAC+D,EAAA,CAAQ,qBAAoB,GAAC,MAAO/D,EAAAA,IAAC6H,EAAA,CAAK,KAAK,yBAAyB,OAAO,SAAS,SAAA,gBAAA,CAAc,EAAS,MAAK,GACjH,SAAArD,EAAAA,KAACC,EAAA,CAAY,MAAO,EAAQyB,EAAO,KAAO,UAAS,GAC/C,SAAA,CAAAlG,EAAAA,IAAC0E,EAAA,CAAW,SAAQ,GAAC,SAAA,aAAU,QAC9BT,EAAA,CAAc,SAAQ,GAAC,UAAS,GAAE,GAAG2D,EAAO,EAC5C1B,EAAO,WAAalG,EAAAA,IAAC8H,GAAgB,SAAA5B,EAAO,WAAW,QAAQ,EAAoB,IAAA,CAAA,CACxF,CAAA,CACJ,CAAA,CAAA,CAEnC,CAAA,EAED1B,EAAAA,KAACkD,EAAA,CACA,KAAM,CACL,GAAI,EAAA,EAEL,SAAA,CAAA1H,EAAAA,IAACyH,EAAA,EAAQ,EACRjD,EAAAA,KAACH,EAAA,CAAM,UAAW,SACjB,SAAA,CAAArE,EAAAA,IAACsH,GAAW,SAAA,UAAA,CAAQ,EACpBtH,EAAAA,IAACiB,GAAA,CACA,YAAa8F,EAAkB,MAAM,GAAG,EAAE,IAAK1H,GAAcA,EAAE,MAAM,EAAE,OAAQA,GAAcA,CAAC,EAC9F,cAAeyH,EAAoB,MAAM,GAAG,EAAE,IAAKzH,GAAcA,EAAE,MAAM,EAAE,OAAQA,GAAcA,CAAC,EAClG,eAAgBuH,CAAA,CAAA,CACjB,EACD,QACAa,EAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,EAEVzH,EAAAA,IAAC2H,EAAA,CACA,QAAA3B,EACA,KAAK,gBACL,OAAQ,CAAC,CAAE,MAAA4B,CAAA,UACT,QAAA,CAAO,GAAGA,EAAO,KAAK,QAAA,CAAS,CAAA,CAAA,EAGlC5H,EAAAA,IAAC2H,EAAA,CACA,QAAA3B,EACA,KAAK,cACL,OAAQ,CAAC,CAAE,MAAA4B,CAAA,UACT,QAAA,CAAO,GAAGA,EAAO,KAAK,QAAA,CAAS,CAAA,CAAA,CAElC,CAAA,CAAA,CACD,CAAA,CACD,EACD,EACD,EACApD,OAAC6C,GAAY,GAAI,CAAE,eAAgB,gBAAiB,WAAY,UAC/D,SAAA,CAAArH,EAAAA,IAAC2H,EAAA,CACA,QAAA3B,EACA,KAAK,WACL,OAAQ,CAAC,CAAE,MAAA4B,CAAA,IACV5H,EAAAA,IAACoI,GAAA,CACA,cAAUC,GAAA,CAAO,QAAST,EAAM,MAAO,SAAUA,EAAM,SAAU,EACjE,MAAM,UAAA,CAAA,CACP,CAAA,SAGD5D,EAAA,CACA,SAAA,CAAAhE,EAAAA,IAAC2E,EAAA,CAAO,MAAM,YAAY,UAAW4C,EAAY,KAAML,EAAM,eAAe,KAAK,MAAA,EAAS,SAAA,QAAA,CAE1F,EACAlH,EAAAA,IAAC2E,GAAO,KAAK,SAAS,QAAQ,YAC5B,SAAAa,EAAa,SAAW,QAAA,CAC1B,CAAA,CAAA,CACD,CAAA,CAAA,CACD,CAAA,CAAA,CACD,CAAA,CACD,CAEF,CCzTO,SAAS8C,GAAgB,CAAC,MAAA3H,GAAiD,CAC9E,MAAM4H,EAASC,GAAA,EACT,CAACC,EAAaC,CAAc,EAAIC,EAAAA,SAAyE,CAAA,CAAE,EAC3G,CAACC,EAAeC,CAAa,EAAIC,GAAA,EAEjC,CAAC,KAAAC,GAAQC,GAAuB,CAAC,MAAO,GAAGrI,CAAK,GAAG,EAEzD6F,EAAAA,UAAgB,IAAM,CACduC,GAAQA,EAAK,OAAS,MACtBL,EAAeK,EAAK,KAAK,CAEjC,EAAG,CAACpI,EAAOoI,EAAMF,EAAc,SAAS,CAAC,EAEzCrC,EAAAA,UAAgB,IAAM,CACVqC,EAAc,SACdlC,EAAM,MAAM,uBAAuB,CAE3C,EACA,CAACkC,EAAc,OAAO,CAAA,EAG1B,MAAM/E,EAAwB,CAC1B,CACL,MAAO,KACP,WAAY,KACH,KAAM,SACN,MAAO,SACP,YAAa,SACb,MAAO,EAAA,EAEX,CACL,MAAO,OACP,WAAY,OACZ,KAAM,SACG,SAAU,GAAA,EAEpB,CACC,MAAO,cACP,WAAY,cACZ,SAAU,IACV,KAAM,QAAA,EAED,CACI,MAAO,aACP,WAAY,aACZ,SAAU,IACV,KAAM,SACN,MAAO,SACP,YAAa,QAAA,EAEjB,CACL,MAAO,OACP,WAAY,OACZ,SAAU,GAAA,EAEL,CACL,MAAO,WACP,WAAY,WACZ,MAAO,IACP,KAAM,SAAA,EAED,CACI,MAAO,UACP,WAAY,GACZ,MAAO,IACP,MAAO,SACP,YAAa,SACb,SAAU,GACV,kBAAmB,GACnB,WAAazD,UAEJgE,EAAA,CAAM,UAAU,MAAM,QAAS,GAAK,WAAW,SAC5C,SAAA,CAAArE,EAAAA,IAACmE,EAAA,CACG,UAAWoD,EACX,KAAML,EAAM,eAAe,KAAK,SAAS,YAAY,CAAC,MAAAvG,EAAO,UAAWN,EAAO,IAAI,EAAA,CAAG,EACtF,KAAK,QACL,GAAI,CAAE,QAAS,KAAA,EAEf,SAAAL,EAAAA,IAACsE,GAAA,CAAiB,SAAS,OAAA,CAAQ,CAAA,CAAA,EAEvCtE,EAAAA,IAACmE,EAAA,CACG,QAAS,IAAM,CACXyE,EAAc,CAAC,UAAW,GAAGjI,CAAK,GAAI,UAAW,GAAGN,EAAO,IAAI,EAAE,EAAA,CAAG,CACxE,EACA,KAAK,QACL,GAAI,CAAE,QAAS,KAAA,EAEf,SAAAL,EAAAA,IAACuE,GAAA,CAAU,SAAS,OAAA,CAAQ,CAAA,CAAA,CAChC,EACJ,CAER,CACJ,EAGP,cACQ6C,GAAA,CACG,SAAA,CAAA5C,OAACgD,GAAA,CACG,SAAA,CAAAxH,EAAAA,IAACsH,GAAW,SAAA,UAAA,CAEZ,EACAtH,EAAAA,IAAC4E,GAAA,CACG,OAAA2D,EACA,QAAAzE,EACA,KAAM2E,EACN,WAAU,GACV,gBAAiB,CAAC,EAAG,GAAI,EAAE,EAC3B,aAAc,CACV,WAAY,CAAE,gBAAiB,CAAE,SAAU,GAAI,KAAM,EAAE,CAAE,EAE7D,2BAA0B,GAC1B,UAAW,GACX,GAAI,CACA,GAAI,EACJ,OAAQ,oBACR,+BAAgC,CAC5B,gBAAiB,UACjB,aAAc,oBACd,SAAU,WACV,WAAY,GAAA,EAEhB,8BAA+B,CAC3B,QAAS,UAAA,EAEb,sBAAuB,CACnB,QAAS,WACT,SAAU,WACV,aAAc,oBACd,QAAS,OACT,WAAY,QAAA,EAEhB,4BAA6B,CACzB,QAAS,MAAA,EAEb,mCAAoC,CAChC,QAAS,MAAA,EAEb,qBAAsB,CAClB,UAAW,CACP,gBAAiB,SAAA,CACrB,EAEJ,kDAAmD,CAC/C,aAAc,MAAA,EAElB,iCAAkC,CAC9B,UAAW,oBACX,gBAAiB,SAAA,EAErB,iCAAkC,CAC9B,QAAS,MAAA,CACb,CACJ,CAAA,CACJ,EACJ,QACCpB,EAAA,CACG,SAAArH,EAAAA,IAAC2E,EAAA,CACG,MAAO,CAAE,WAAY,MAAA,EACrB,UAAW4C,EACX,KAAML,EAAM,eAAe,KAAK,SAAS,cAAcvG,CAAK,EAC/D,SAAA,oBAAA,CAAA,CAED,CACJ,CAAA,EACJ,CAER,CChKO,SAASsI,GAAK,CAAE,MAAAtI,EAAO,iBAAAuI,GAAkD,CAC/E,MAAMC,EAAW,CAChB,MAAO,GAAGxI,EAAQ,OAAS,QAAQ,yBAAyByI,GAAU,IAAI,EAAA,EAG3E5C,OAAAA,EAAAA,UACC,IAAM,CACL,GAAI0C,EAAiB,CACpB,MAAMG,EAAU,SAAS,eAAe,UAAU,EAC9CA,GACHA,EAAQ,eAAe,CAAC,SAAU,MAAA,CAAO,CAE3C,CACD,EACA,CAAA,CAAC,EAID7E,EAAAA,KAAC8E,WAAA,CACA,SAAA,CAAAtJ,EAAAA,IAACuJ,GAAA,CACA,SAAAvJ,MAAC,QAAA,CAAO,SAAAmJ,EAAS,MAAM,EACxB,EACAnJ,EAAAA,IAACgE,EAAA,CACA,GAAI,CACH,SAAU,0BACV,EAAG,wBACH,EAAG,yBACH,MAAO,sBAAA,EAGR,SAAAQ,EAAAA,KAACH,EAAA,CAAM,QAAS,EACf,SAAA,CAAArE,MAACqE,EAAA,CAAM,QAAS,EACf,SAAArE,MAAC,MAAA,CACA,SAAAwE,EAAAA,KAACqD,EAAA,CACA,MAAM,eACN,UAAWN,EACX,KAAML,EAAM,eAAe,KAAK,MAAA,EAChC,GAAI,CAAE,WAAY,SAAU,QAAS,cAAe,IAAK,CAAA,EACzD,QAAQ,YAER,SAAA,CAAAlH,EAAAA,IAACwJ,GAAA,CAAc,SAAS,yBAAA,CAA0B,EAAE,aAAA,CAAA,CAAA,EAGtD,CAAA,CACD,EACexJ,MAACqF,IAAY,MAAA1E,EAAc,EAC1CX,EAAAA,IAAC,MAAA,CAAI,GAAG,WACP,SAAAA,EAAAA,IAAC0H,EAAA,CACA,KAAM,CACL,GAAI,EAAA,EAEL,SAAA1H,EAAAA,IAACqE,GAAM,UAAW,SAChB,YAASrE,EAAAA,IAACsI,GAAA,CAAgB,MAAA3H,EAAc,CAAA,CAC1C,CAAA,CAAA,CACD,CACD,CAAA,CAAA,CACD,CAAA,CAAA,CACD,EACD,CAEF", "x_google_ignoreList": [0, 1, 2, 3, 4, 5]}