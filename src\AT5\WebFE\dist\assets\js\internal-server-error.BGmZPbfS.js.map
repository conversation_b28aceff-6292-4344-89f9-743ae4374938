{"version": 3, "file": "internal-server-error-DgOTgalT.js", "sources": ["../../../src/pages/errors/internal-server-error.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport Box from \"@mui/material/Box\";\nimport Button from \"@mui/material/Button\";\nimport Container from \"@mui/material/Container\";\nimport Stack from \"@mui/material/Stack\";\nimport Typography from \"@mui/material/Typography\";\nimport { Helmet } from \"react-helmet-async\";\n\nimport type { Metadata } from \"@/types/metadata\";\nimport { appConfig } from \"@/config/app\";\nimport { paths } from \"@/paths\";\nimport { RouterLink } from \"@/components/core/link\";\nimport { assets } from \"@/lib/assets\";\n\nconst metadata = { title: `Internal server error | Errors | ${appConfig.name}` } satisfies Metadata;\n\nexport function Page(): React.JSX.Element {\n\treturn (\n\t\t<React.Fragment>\n\t\t\t<Helmet>\n\t\t\t\t<title>{metadata.title}</title>\n\t\t\t</Helmet>\n\t\t\t<Box\n\t\t\t\tcomponent=\"main\"\n\t\t\t\tsx={{\n\t\t\t\t\talignItems: \"center\",\n\t\t\t\t\tdisplay: \"flex\",\n\t\t\t\t\tflexDirection: \"column\",\n\t\t\t\t\tjustifyContent: \"center\",\n\t\t\t\t\tminHeight: \"100%\",\n\t\t\t\t\tpy: \"64px\",\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<Container maxWidth=\"lg\">\n\t\t\t\t\t<Stack spacing={6}>\n\t\t\t\t\t\t<Box sx={{ display: \"flex\", justifyContent: \"center\" }}>\n\t\t\t\t\t\t\t<Box\n\t\t\t\t\t\t\t\talt=\"Internal server error\"\n\t\t\t\t\t\t\t\tcomponent=\"img\"\n\t\t\t\t\t\t\t\tsrc={assets.error()}\n\t\t\t\t\t\t\t\tsx={{ height: \"auto\", maxWidth: \"100%\", width: \"200px\" }}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</Box>\n\t\t\t\t\t\t<Stack spacing={1} sx={{ textAlign: \"center\" }}>\n\t\t\t\t\t\t\t<Typography variant=\"h4\">500: Internal server error</Typography>\n\t\t\t\t\t\t\t<Typography color=\"text.secondary\">\n\t\t\t\t\t\t\t\tYou either tried some shady route or you came here by mistake. Whichever it is, try using the\n\t\t\t\t\t\t\t\tnavigation.\n\t\t\t\t\t\t\t</Typography>\n\t\t\t\t\t\t</Stack>\n\t\t\t\t\t\t<Box sx={{ display: \"flex\", justifyContent: \"center\" }}>\n\t\t\t\t\t\t\t<Button component={RouterLink} href={paths.home} variant=\"contained\">\n\t\t\t\t\t\t\t\tBack to home\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t</Box>\n\t\t\t\t\t</Stack>\n\t\t\t\t</Container>\n\t\t\t</Box>\n\t\t</React.Fragment>\n\t);\n}\n"], "names": ["metadata", "appConfig", "Page", "jsxs", "React.Fragment", "jsx", "<PERSON><PERSON><PERSON>", "Box", "Container", "<PERSON><PERSON>", "assets", "Typography", "<PERSON><PERSON>", "RouterLink", "paths"], "mappings": "0OAcA,MAAMA,EAAW,CAAE,MAAO,oCAAoCC,EAAU,IAAI,EAAA,EAErE,SAASC,GAA0B,CACzC,OACCC,EAAAA,KAACC,WAAA,CACA,SAAA,CAAAC,EAAAA,IAACC,EAAA,CACA,SAAAD,MAAC,QAAA,CAAO,SAAAL,EAAS,MAAM,EACxB,EACAK,EAAAA,IAACE,EAAA,CACA,UAAU,OACV,GAAI,CACH,WAAY,SACZ,QAAS,OACT,cAAe,SACf,eAAgB,SAChB,UAAW,OACX,GAAI,MAAA,EAGL,eAACC,EAAA,CAAU,SAAS,KACnB,SAAAL,EAAAA,KAACM,EAAA,CAAM,QAAS,EACf,SAAA,CAAAJ,MAACE,GAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,UAC3C,SAAAF,EAAAA,IAACE,EAAA,CACA,IAAI,wBACJ,UAAU,MACV,IAAKG,EAAO,MAAA,EACZ,GAAI,CAAE,OAAQ,OAAQ,SAAU,OAAQ,MAAO,OAAA,CAAQ,CAAA,EAEzD,EACAP,OAACM,GAAM,QAAS,EAAG,GAAI,CAAE,UAAW,UACnC,SAAA,CAAAJ,EAAAA,IAACM,EAAA,CAAW,QAAQ,KAAK,SAAA,6BAA0B,EACnDN,EAAAA,IAACM,EAAA,CAAW,MAAM,iBAAiB,SAAA,2GAAA,CAGnC,CAAA,EACD,QACCJ,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,UAC3C,eAACK,EAAA,CAAO,UAAWC,EAAY,KAAMC,EAAM,KAAM,QAAQ,YAAY,wBAErE,CAAA,CACD,CAAA,CAAA,CACD,CAAA,CACD,CAAA,CAAA,CACD,EACD,CAEF"}