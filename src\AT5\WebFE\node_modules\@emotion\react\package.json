{"name": "@emotion/react", "version": "11.14.0", "main": "dist/emotion-react.cjs.js", "module": "dist/emotion-react.esm.js", "types": "dist/emotion-react.cjs.d.ts", "exports": {".": {"types": {"import": "./dist/emotion-react.cjs.mjs", "default": "./dist/emotion-react.cjs.js"}, "development": {"edge-light": {"module": "./dist/emotion-react.development.edge-light.esm.js", "import": "./dist/emotion-react.development.edge-light.cjs.mjs", "default": "./dist/emotion-react.development.edge-light.cjs.js"}, "worker": {"module": "./dist/emotion-react.development.edge-light.esm.js", "import": "./dist/emotion-react.development.edge-light.cjs.mjs", "default": "./dist/emotion-react.development.edge-light.cjs.js"}, "workerd": {"module": "./dist/emotion-react.development.edge-light.esm.js", "import": "./dist/emotion-react.development.edge-light.cjs.mjs", "default": "./dist/emotion-react.development.edge-light.cjs.js"}, "browser": {"module": "./dist/emotion-react.browser.development.esm.js", "import": "./dist/emotion-react.browser.development.cjs.mjs", "default": "./dist/emotion-react.browser.development.cjs.js"}, "module": "./dist/emotion-react.development.esm.js", "import": "./dist/emotion-react.development.cjs.mjs", "default": "./dist/emotion-react.development.cjs.js"}, "edge-light": {"module": "./dist/emotion-react.edge-light.esm.js", "import": "./dist/emotion-react.edge-light.cjs.mjs", "default": "./dist/emotion-react.edge-light.cjs.js"}, "worker": {"module": "./dist/emotion-react.edge-light.esm.js", "import": "./dist/emotion-react.edge-light.cjs.mjs", "default": "./dist/emotion-react.edge-light.cjs.js"}, "workerd": {"module": "./dist/emotion-react.edge-light.esm.js", "import": "./dist/emotion-react.edge-light.cjs.mjs", "default": "./dist/emotion-react.edge-light.cjs.js"}, "browser": {"module": "./dist/emotion-react.browser.esm.js", "import": "./dist/emotion-react.browser.cjs.mjs", "default": "./dist/emotion-react.browser.cjs.js"}, "module": "./dist/emotion-react.esm.js", "import": "./dist/emotion-react.cjs.mjs", "default": "./dist/emotion-react.cjs.js"}, "./jsx-runtime": {"types": {"import": "./jsx-runtime/dist/emotion-react-jsx-runtime.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.cjs.js"}, "development": {"edge-light": {"module": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.edge-light.esm.js", "import": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.edge-light.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.edge-light.cjs.js"}, "worker": {"module": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.edge-light.esm.js", "import": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.edge-light.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.edge-light.cjs.js"}, "workerd": {"module": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.edge-light.esm.js", "import": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.edge-light.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.edge-light.cjs.js"}, "browser": {"module": "./jsx-runtime/dist/emotion-react-jsx-runtime.browser.development.esm.js", "import": "./jsx-runtime/dist/emotion-react-jsx-runtime.browser.development.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.browser.development.cjs.js"}, "module": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.esm.js", "import": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.development.cjs.js"}, "edge-light": {"module": "./jsx-runtime/dist/emotion-react-jsx-runtime.edge-light.esm.js", "import": "./jsx-runtime/dist/emotion-react-jsx-runtime.edge-light.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.edge-light.cjs.js"}, "worker": {"module": "./jsx-runtime/dist/emotion-react-jsx-runtime.edge-light.esm.js", "import": "./jsx-runtime/dist/emotion-react-jsx-runtime.edge-light.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.edge-light.cjs.js"}, "workerd": {"module": "./jsx-runtime/dist/emotion-react-jsx-runtime.edge-light.esm.js", "import": "./jsx-runtime/dist/emotion-react-jsx-runtime.edge-light.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.edge-light.cjs.js"}, "browser": {"module": "./jsx-runtime/dist/emotion-react-jsx-runtime.browser.esm.js", "import": "./jsx-runtime/dist/emotion-react-jsx-runtime.browser.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.browser.cjs.js"}, "module": "./jsx-runtime/dist/emotion-react-jsx-runtime.esm.js", "import": "./jsx-runtime/dist/emotion-react-jsx-runtime.cjs.mjs", "default": "./jsx-runtime/dist/emotion-react-jsx-runtime.cjs.js"}, "./_isolated-hnrs": {"types": {"import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.cjs.js"}, "development": {"edge-light": {"module": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.edge-light.esm.js", "import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.edge-light.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.edge-light.cjs.js"}, "worker": {"module": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.edge-light.esm.js", "import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.edge-light.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.edge-light.cjs.js"}, "workerd": {"module": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.edge-light.esm.js", "import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.edge-light.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.edge-light.cjs.js"}, "browser": {"module": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.development.esm.js", "import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.development.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.development.cjs.js"}, "module": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.esm.js", "import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.development.cjs.js"}, "edge-light": {"module": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.edge-light.esm.js", "import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.edge-light.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.edge-light.cjs.js"}, "worker": {"module": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.edge-light.esm.js", "import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.edge-light.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.edge-light.cjs.js"}, "workerd": {"module": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.edge-light.esm.js", "import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.edge-light.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.edge-light.cjs.js"}, "browser": {"module": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.esm.js", "import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.cjs.js"}, "module": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.esm.js", "import": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.cjs.mjs", "default": "./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.cjs.js"}, "./jsx-dev-runtime": {"types": {"import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.cjs.js"}, "development": {"edge-light": {"module": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.edge-light.esm.js", "import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.edge-light.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.edge-light.cjs.js"}, "worker": {"module": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.edge-light.esm.js", "import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.edge-light.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.edge-light.cjs.js"}, "workerd": {"module": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.edge-light.esm.js", "import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.edge-light.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.edge-light.cjs.js"}, "browser": {"module": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.browser.development.esm.js", "import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.browser.development.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.browser.development.cjs.js"}, "module": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.esm.js", "import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.development.cjs.js"}, "edge-light": {"module": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.edge-light.esm.js", "import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.edge-light.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.edge-light.cjs.js"}, "worker": {"module": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.edge-light.esm.js", "import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.edge-light.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.edge-light.cjs.js"}, "workerd": {"module": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.edge-light.esm.js", "import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.edge-light.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.edge-light.cjs.js"}, "browser": {"module": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.browser.esm.js", "import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.browser.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.browser.cjs.js"}, "module": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.esm.js", "import": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.cjs.mjs", "default": "./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.cjs.js"}, "./package.json": "./package.json", "./types/css-prop": "./types/css-prop.d.ts", "./macro": {"types": {"import": "./macro.d.mts", "default": "./macro.d.ts"}, "default": "./macro.js"}}, "imports": {"#is-development": {"development": "./src/conditions/true.ts", "default": "./src/conditions/false.ts"}, "#is-browser": {"edge-light": "./src/conditions/false.ts", "workerd": "./src/conditions/false.ts", "worker": "./src/conditions/false.ts", "browser": "./src/conditions/true.ts", "default": "./src/conditions/is-browser.ts"}}, "files": ["src", "dist", "jsx-runtime", "jsx-dev-runtime", "_isolated-hnrs", "types/css-prop.d.ts", "macro.*"], "sideEffects": false, "author": "Emotion Contributors", "license": "MIT", "scripts": {"test:typescript": "dtslint types"}, "dependencies": {"@babel/runtime": "^7.18.3", "@emotion/babel-plugin": "^11.13.5", "@emotion/cache": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/use-insertion-effect-with-fallbacks": "^1.2.0", "@emotion/utils": "^1.4.2", "@emotion/weak-memoize": "^0.4.0", "hoist-non-react-statics": "^3.3.1"}, "peerDependencies": {"react": ">=16.8.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "@emotion/css": "11.13.5", "@emotion/css-prettifier": "1.2.0", "@emotion/server": "11.11.0", "@emotion/styled": "11.14.0", "@types/hoist-non-react-statics": "^3.3.5", "html-tag-names": "^1.1.2", "react": "16.14.0", "svg-tag-names": "^1.1.1", "typescript": "^5.4.5"}, "repository": "https://github.com/emotion-js/emotion/tree/main/packages/react", "publishConfig": {"access": "public"}, "umd:main": "dist/emotion-react.umd.min.js", "preconstruct": {"entrypoints": ["./index.ts", "./jsx-runtime.ts", "./jsx-dev-runtime.ts", "./_isolated-hnrs.ts"], "umdName": "emotionReact", "exports": {"extra": {"./types/css-prop": "./types/css-prop.d.ts", "./macro": {"types": {"import": "./macro.d.mts", "default": "./macro.d.ts"}, "default": "./macro.js"}}}}}