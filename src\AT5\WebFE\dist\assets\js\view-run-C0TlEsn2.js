import{r as f,j as e,a2 as T,a4 as z,T as J,B as g,b as w,a7 as W,S as d,a9 as r,t as l,aE as o,m as n,g as C}from"./mui-51Y1Yx8M.js";import{o as F}from"./jobs-emails-list-B_USfMsX.js";import{B as L,t as k,p as b,R as j,H as _,a as A}from"./index-CQLEdRvh.js";import{f as R,a as N}from"./administration-DCdGn-A8.js";import{c as E}from"./odata-grid-data-source-CWOTXcJM.js";import{u as G,D as I}from"./DataGridPremium-Dv14NGpu.js";import"./vendor-Csw2ODfV.js";import"./ArrowLeft.es-DzNS2JFL.js";import"./router-Bd8Y3ArC.js";import"./redux-BKAL-i9G.js";const B=[{label:"JobResultType.None",value:0},{label:"JobResultType.Success",value:1},{label:"JobResultType.SuccessWithWarning",value:2},{label:"JobResultType.PartialSuccess",value:3},{label:"JobResultType.Failure",value:4}],O=[{label:"JobRunState.Starting",value:0},{label:"JobRunState.Running",value:1},{label:"JobRunState.Finished",value:2}];function P({jobRunId:s}){var m,i,x,u,a,y,S;const{data:t,error:c,isLoading:v}=R({rootEntityId:`${s}`},{skip:s===void 0});return f.useEffect(()=>{c&&(L.error(c),k.error("Failed to load job run data"))},[c]),v?e.jsx(e.Fragment,{children:"Loading..."}):e.jsxs(T,{children:[e.jsxs(z,{sx:{justifyContent:"space-between",alignItems:"center"},children:[e.jsx(J,{variant:"h4",children:`Job run ${s} details`}),e.jsx(g,{children:e.jsx(w,{color:"secondary",component:j,href:b.administration.jobs.index(!0),children:"Go back"})})]}),e.jsx(W,{children:e.jsx(d,{divider:e.jsx(C,{}),spacing:4,children:e.jsxs(d,{spacing:3,children:[e.jsx(J,{variant:"h5",children:"Detail"}),e.jsxs(r,{container:!0,spacing:3,direction:"row",sx:{justifyContent:"left",alignItems:"center"},children:[e.jsx(r,{size:{md:6,xs:3},children:e.jsx(l,{disableFocusListener:!0,title:e.jsx(n,{href:"https://aristotelos.cz",target:"_blank",children:"Documentation:"}),arrow:!0,children:e.jsx(o,{fullWidth:!0,label:"Job Name",value:((m=t==null?void 0:t.entity)==null?void 0:m.jobName)??"",disabled:!0})})}),e.jsx(r,{size:{md:6,xs:3},children:e.jsx(l,{disableFocusListener:!0,title:e.jsx(n,{href:"https://aristotelos.cz",target:"_blank",children:"Documentation:"}),arrow:!0,children:e.jsx(o,{fullWidth:!0,label:"Author Id",value:((i=t==null?void 0:t.entity)==null?void 0:i.authorId)??"",disabled:!0})})}),e.jsx(r,{size:{md:6,xs:12},children:e.jsx(l,{disableFocusListener:!0,title:e.jsx(n,{href:"https://aristotelos.cz",target:"_blank",children:"Documentation:"}),arrow:!0,children:e.jsx(o,{fullWidth:!0,label:"State",value:((x=O.find(p=>{var h;return p.value===((h=t==null?void 0:t.entity)==null?void 0:h.state)}))==null?void 0:x.label)??"",disabled:!0})})}),e.jsx(r,{size:{md:6,xs:12},children:e.jsx(l,{disableFocusListener:!0,title:e.jsx(n,{href:"https://aristotelos.cz",target:"_blank",children:"Documentation:"}),arrow:!0,children:e.jsx(o,{fullWidth:!0,label:"Result",value:((u=B.find(p=>{var h;return p.value===((h=t==null?void 0:t.entity)==null?void 0:h.state)}))==null?void 0:u.label)??"",disabled:!0})})}),e.jsx(r,{size:{md:6,xs:12},children:e.jsx(l,{disableFocusListener:!0,title:e.jsx(n,{href:"https://aristotelos.cz",target:"_blank",children:"Documentation:"}),arrow:!0,children:e.jsx(o,{fullWidth:!0,label:"Triggered",value:((a=t==null?void 0:t.entity)==null?void 0:a.triggered)??"",disabled:!0})})}),e.jsx(r,{size:{md:6,xs:12},children:e.jsx(l,{disableFocusListener:!0,title:e.jsx(n,{href:"https://aristotelos.cz",target:"_blank",children:"Documentation:"}),arrow:!0,children:e.jsx(o,{fullWidth:!0,label:"Started",value:((y=t==null?void 0:t.entity)==null?void 0:y.started)??"",disabled:!0})})}),e.jsx(r,{size:{md:6,xs:12},children:e.jsx(l,{disableFocusListener:!0,title:e.jsx(n,{href:"https://aristotelos.cz",target:"_blank",children:"Documentation:"}),arrow:!0,children:e.jsx(o,{fullWidth:!0,label:"Finished",value:((S=t==null?void 0:t.entity)==null?void 0:S.finished)??"",disabled:!0})})})]})]})})}),e.jsx(z,{sx:{justifyContent:"space-between",alignItems:"center"},children:e.jsx(g,{children:e.jsx(w,{color:"secondary",component:j,href:b.administration.jobs.index(!0),children:"Return"})})})]})}function M({jobRunId:s}){const t=G(),[c,v]=f.useState(()=>E(N.endpoints.atApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpoint.initiate,()=>{var u;const i=t.current;return i?(((u=i.getVisibleColumns)==null?void 0:u.call(i))??[]).map(a=>a.field).filter(a=>a&&a!=="actions"&&!a.startsWith("__")):void 0},{jobRunId:s})),m=[{field:"id",headerName:"Id",flex:1,minWidth:90,type:"number"},{field:"timestamp",headerName:"Time Stamp",minWidth:180,align:"center",headerAlign:"center",type:"dateTime",valueGetter:i=>i?new Date(i):null},{field:"messageId",headerName:"Message Id",minWidth:240,align:"center",headerAlign:"center",type:"string"},{field:"messageTranslated",headerName:"Message Translated",minWidth:240,align:"center",headerAlign:"center",type:"string"},{field:"messageParameters",headerName:"Message Parameters",minWidth:240,align:"center",headerAlign:"center",type:"string"}];return e.jsx(d,{children:e.jsx(I,{apiRef:t,dataSource:c,columns:m,pagination:!0,showToolbar:!0,pageSizeOptions:[5,10,25],initialState:{pagination:{paginationModel:{pageSize:10,page:0}}},disableRowSelectionOnClick:!0,rowHeight:52,className:"jobs-emails-list__data-grid"})})}function Z({jobRunId:s}){const t={title:`Runs | Jobs | Dashboard | ${A.name}`};return e.jsxs(f.Fragment,{children:[e.jsx(_,{children:e.jsx("title",{children:t.title})}),e.jsx(g,{sx:{maxWidth:"var(--Content-maxWidth)",m:"var(--Content-margin)",p:"var(--Content-padding)",width:"var(--Content-width)"},children:e.jsxs(d,{spacing:4,children:[e.jsx(d,{spacing:3,children:e.jsx("div",{children:e.jsxs(n,{color:"text.primary",component:j,href:b.administration.jobs.index(!0),sx:{alignItems:"center",display:"inline-flex",gap:1},variant:"subtitle2",children:[e.jsx(F,{fontSize:"var(--icon-fontSize-md)"}),"Browse Job Runs"]})})}),e.jsx(P,{jobRunId:s}),e.jsx(r,{size:{xs:12},children:e.jsx(d,{direction:"column",children:s&&e.jsx(M,{jobRunId:s})})})]})})]})}export{Z as Page};
//# sourceMappingURL=view-run.DnXh2HbY.js.map
