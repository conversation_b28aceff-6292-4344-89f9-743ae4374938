import path from 'node:path';

import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import type { Plugin } from 'vite';

// https://github.com/vitejs/vite/issues/15012#issuecomment-1825035992
function muteWarningsPlugin(warningsToIgnore: string[][]): Plugin {
  const mutedMessages = new Set();

  return {
    name: 'mute-warnings',
    enforce: 'pre',
    config: (userConfig) => ({
      build: {
        rollupOptions: {
          onwarn(warning, defaultHandler) {
            if (warning.code) {
              const muted = warningsToIgnore.find(
                ([code, message]) => code == warning.code && warning.message.includes(message)
              );

              if (muted) {
                mutedMessages.add(muted.join());
                return;
              }
            }

            if (userConfig.build?.rollupOptions?.onwarn) {
              userConfig.build.rollupOptions.onwarn(warning, defaultHandler);
            } else {
              defaultHandler(warning);
            }
          },
        },
      },
    }),
    closeBundle() {
      const diff = warningsToIgnore.filter((x) => !mutedMessages.has(x.join()));
      if (diff.length > 0) {
        this.warn('Some of your muted warnings never appeared during the build process:');
        diff.forEach((m) => this.warn(`- ${m.join(': ')}`));
      }
    },
  };
}

// See this: https://github.com/vitejs/vite/issues/15012
const warningsToIgnore = [
  ['SOURCEMAP_ERROR', "Can't resolve original location of error"],
  ['INVALID_ANNOTATION', 'contains an annotation that Rollup cannot interpret'],
];


export default defineConfig({
  base: (() => {
    const basePath = process.env.VITE_APP_BASE_PATH ?? '/';
    return basePath.endsWith('/') ? basePath : `${basePath}/`;
  })(),
  plugins: [react(), muteWarningsPlugin(warningsToIgnore)],
  resolve: {
    alias: [
      {
        find: /^~(.+)/,
        replacement: path.join(process.cwd(), 'node_modules/$1'),
      },
      {
        find: /^@\/(.+)/,
        replacement: path.join(process.cwd(), 'src/$1'),
      },
    ],
  },
  build: {
    // Emit a manifest.json so your server can look up the hashed filenames. Useful for SSR or traditional backends.
    manifest: true,
    // Enable source maps for better debugging. Disable for production in the future?
    sourcemap: true,
    // Optimize chunk splitting for better caching
    rollupOptions: {
      output: {
        // Create separate chunks for vendor libraries
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/system', '@mui/utils'],
          redux: ['@reduxjs/toolkit', 'react-redux'],
          router: ['react-router-dom'],
        },
        // Add content hash to filenames for cache busting
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const ext = path.extname(assetInfo.name ?? '').toLowerCase();

          if (/\.(png|jpe?g|svg|gif|webp|avif|bmp)$/.test(ext)) {
            return 'assets/images/[name]-[hash][extname]';
          }
          if (/\.(css)$/.test(ext)) {
            return 'assets/css/[name]-[hash][extname]';
          }
          if (/\.(woff2?|ttf|eot|otf)$/.test(ext)) {
            return 'assets/fonts/[name]-[hash][extname]';
          }
          return 'assets/[name]-[hash][extname]';
        },
        // Move sourcemap to the same directory as the chunk
        sourcemapFileNames: 'assets/js/[name].[hash].js.map',
      },
    },
  },
  server: {
    port: 3000,
  },
  preview: {
    port: 3000,
  },
});
